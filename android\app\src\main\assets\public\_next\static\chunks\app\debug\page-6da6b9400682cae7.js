(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9302],{18257:(e,r,t)=>{Promise.resolve().then(t.bind(t,57821))},57740:(e,r,t)=>{"use strict";t.d(r,{DP:()=>s,ThemeProvider:()=>o,Yx:()=>d});var n=t(95155),i=t(12115);let a=(0,i.createContext)(void 0);function o(e){let{children:r}=e,[t,o]=(0,i.useState)("dark"),[s,l]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{l(!0);let e=localStorage.getItem("theme");e?o(e):o("dark")},[]),(0,i.useEffect)(()=>{s&&(document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(t),localStorage.setItem("theme",t))},[t,s]),s)?(0,n.jsx)(a.Provider,{value:{theme:t,toggleTheme:()=>{o(e=>"light"===e?"dark":"light")},setTheme:e=>{o(e)}},children:r}):(0,n.jsx)("div",{className:"min-h-screen bg-black",children:r})}function s(){let e=(0,i.useContext)(a);return void 0===e?(console.warn("useTheme called outside of ThemeProvider, using fallback values"),{theme:"dark",toggleTheme:()=>{},setTheme:()=>{}}):e}let l={light:{bg:{primary:"bg-white",secondary:"bg-gray-50",tertiary:"bg-gray-100",card:"bg-white",sidebar:"bg-white",header:"bg-white/80"},text:{primary:"text-gray-900",secondary:"text-gray-600",tertiary:"text-gray-500",inverse:"text-white"},border:{primary:"border-gray-200",secondary:"border-gray-300",accent:"border-green-200"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"},accent:{green:"text-green-600",blue:"text-blue-600",purple:"text-purple-600",yellow:"text-yellow-600"}},dark:{bg:{primary:"bg-black",secondary:"bg-gray-900",tertiary:"bg-gray-800",card:"bg-gray-900",sidebar:"bg-gray-900",header:"bg-gray-900/80"},text:{primary:"text-white",secondary:"text-gray-300",tertiary:"text-gray-400",inverse:"text-black"},border:{primary:"border-gray-800",secondary:"border-gray-700",accent:"border-green-500/30"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-black"},accent:{green:"text-green-400",blue:"text-blue-400",purple:"text-purple-400",yellow:"text-yellow-400"}}};function d(e){return l[e]}},57821:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(95155),i=t(12115),a=t(57740);function o(){let[e,r]=(0,i.useState)(!1),[t,o]=(0,i.useState)({width:0,height:0}),{theme:s,toggleTheme:l}=(0,a.DP)();return((0,i.useEffect)(()=>{r(!0);let e=()=>{o({width:window.innerWidth,height:window.innerHeight})};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e)?(0,n.jsxs)("div",{className:"min-h-screen p-8 ".concat("light"===s?"bg-white text-black":"bg-black text-white"),children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Debug Information"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Current Theme:"})," ",s]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Screen Size:"})," ",t.width," x ",t.height]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("strong",{children:"Is Large Screen (≥1024px):"})," ",t.width>=1024?"Yes":"No"]}),(0,n.jsx)("button",{onClick:l,className:"px-4 py-2 rounded ".concat("light"===s?"bg-gray-800 text-white hover:bg-gray-700":"bg-white text-black hover:bg-gray-200"),children:"Toggle Theme"}),(0,n.jsxs)("div",{className:"mt-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Responsive Test"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{className:"p-4 rounded ".concat("light"===s?"bg-gray-100":"bg-gray-800"),children:[(0,n.jsx)("h3",{className:"font-semibold",children:"Always Visible"}),(0,n.jsx)("p",{children:"This content is always visible"})]}),(0,n.jsxs)("div",{className:"p-4 rounded lg:block hidden ".concat("light"===s?"bg-blue-100":"bg-blue-900"),children:[(0,n.jsx)("h3",{className:"font-semibold",children:"Large Screen Only"}),(0,n.jsx)("p",{children:"This content is only visible on large screens (lg:block hidden)"})]})]})]})]})]}):(0,n.jsx)("div",{children:"Loading..."})}}},e=>{e.O(0,[8441,5964,7358],()=>e(e.s=18257)),_N_E=e.O()}]);