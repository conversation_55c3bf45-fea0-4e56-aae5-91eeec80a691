(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1654],{44361:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(95155),i=a(68289),l=a(12115),r=a(10351),n=a(11846),c=a(52814),o=a(13741),d=a(17703),x=a(93915),u=a(30353),m=a(64198),h=a(24630);let g={DEPOSIT:"\uD83D\uDCB0",WITHDRAWAL:"\uD83C\uDFE6",CONTRIBUTION:"\uD83D\uDCC8",PLAN_CREATED:"\uD83D\uDCCB",PLAN_COMPLETED:"✅",PLAN_PAUSED:"⏸️",GOAL_ACHIEVED:"\uD83C\uDFAF",MILESTONE_REACHED:"\uD83C\uDFC6",PAYMENT_DUE:"⏰",PAYMENT_OVERDUE:"⚠️",KYC_APPROVED:"✅",KYC_REJECTED:"❌",GROUP_INVITATION:"\uD83D\uDC65",GROUP_PAYOUT:"\uD83D\uDCB8",SYSTEM:"⚙️",PROMOTIONAL:"\uD83C\uDF81",SECURITY:"\uD83D\uDD12",OTHER:"\uD83D\uDCE2"},f={LOW:"bg-green-500",MEDIUM:"bg-yellow-500",HIGH:"bg-orange-500",URGENT:"bg-red-500"};function N(){let[e,t]=(0,l.useState)([]),[a,N]=(0,l.useState)(!0),[v,y]=(0,l.useState)(0),[b,j]=(0,l.useState)([]),[p,A]=(0,l.useState)({status:void 0,type:void 0,priority:void 0,page:1,limit:20});(0,l.useEffect)(()=>{E(),R()},[p]);let E=async()=>{try{N(!0);let e=await h.wy.getUserNotifications(p);t(e.notifications)}catch(e){m.oR.error("Failed to load notifications")}finally{N(!1)}},R=async()=>{try{let e=await h.wy.getUnreadCount();y(e.count)}catch(e){console.error("Failed to load unread count:",e)}},w=async a=>{try{await h.wy.markAsRead(a),t(e.map(e=>e.id===a?{...e,status:"READ",readAt:new Date().toISOString()}:e)),R(),m.oR.success("Notification marked as read")}catch(e){m.oR.error("Failed to mark notification as read")}},k=async()=>{try{await h.wy.markAllAsRead(),t(e.map(e=>({...e,status:"READ",readAt:new Date().toISOString()}))),y(0),m.oR.success("All notifications marked as read")}catch(e){m.oR.error("Failed to mark all notifications as read")}},C=async a=>{try{await h.wy.archiveNotification(a),t(e.filter(e=>e.id!==a)),m.oR.success("Notification archived")}catch(e){m.oR.error("Failed to archive notification")}},S=async a=>{try{await h.wy.deleteNotification(a),t(e.filter(e=>e.id!==a)),m.oR.success("Notification deleted")}catch(e){m.oR.error("Failed to delete notification")}},T=async a=>{try{if(0===b.length)return void m.oR.error("No notifications selected");switch(a){case"read":for(let e of b)await h.wy.markAsRead(e);t(e.map(e=>b.includes(e.id)?{...e,status:"READ",readAt:new Date().toISOString()}:e)),m.oR.success("Selected notifications marked as read");break;case"archive":for(let e of b)await h.wy.archiveNotification(e);t(e.filter(e=>!b.includes(e.id))),m.oR.success("Selected notifications archived");break;case"delete":for(let e of b)await h.wy.deleteNotification(e);t(e.filter(e=>!b.includes(e.id))),m.oR.success("Selected notifications deleted")}j([]),R()}catch(e){m.oR.error("Failed to ".concat(a," notifications"))}};return a?(0,s.jsx)(n.A,{title:"Notifications",children:(0,s.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})})}):(0,s.jsx)(n.A,{title:"Notifications",children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Notifications"}),(0,s.jsx)("p",{className:"text-gray-400 mt-2",children:v>0?"".concat(v," unread notifications"):"All caught up!"})]}),(0,s.jsx)("div",{className:"flex space-x-2",children:v>0&&(0,s.jsxs)(o.$n,{onClick:k,variant:"outline",children:[(0,s.jsx)(r.A3x,{className:"mr-2"}),"Mark All Read"]})})]}),(0,s.jsx)(d.Z,{className:"bg-gray-800 border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsx)(x.p,{placeholder:"Search notifications...",value:p.search||"",onChange:e=>A({...p,search:e.target.value}),leftIcon:(0,s.jsx)(r.CKj,{})}),(0,s.jsx)(u.l,{value:p.status||"",onChange:e=>A({...p,status:e.target.value||void 0}),options:[{value:"",label:"All Status"},{value:"UNREAD",label:"Unread"},{value:"READ",label:"Read"},{value:"ARCHIVED",label:"Archived"}]}),(0,s.jsx)(u.l,{value:p.type||"",onChange:e=>A({...p,type:e.target.value||void 0}),options:[{value:"",label:"All Types"},{value:"DEPOSIT",label:"Deposits"},{value:"WITHDRAWAL",label:"Withdrawals"},{value:"CONTRIBUTION",label:"Contributions"},{value:"SYSTEM",label:"System"},{value:"SECURITY",label:"Security"}]}),(0,s.jsx)(u.l,{value:p.priority||"",onChange:e=>A({...p,priority:e.target.value||void 0}),options:[{value:"",label:"All Priorities"},{value:"LOW",label:"Low"},{value:"MEDIUM",label:"Medium"},{value:"HIGH",label:"High"},{value:"URGENT",label:"Urgent"}]})]})}),b.length>0&&(0,s.jsx)(d.Z,{className:"bg-gray-800 border-gray-700 p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("p",{className:"text-white",children:[b.length," notification",b.length>1?"s":""," selected"]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(o.$n,{size:"sm",variant:"outline",onClick:()=>T("read"),children:[(0,s.jsx)(r.YrT,{className:"mr-1"}),"Mark Read"]}),(0,s.jsxs)(o.$n,{size:"sm",variant:"outline",onClick:()=>T("archive"),children:[(0,s.jsx)(r.OZ2,{className:"mr-1"}),"Archive"]}),(0,s.jsxs)(o.$n,{size:"sm",variant:"outline",onClick:()=>T("delete"),className:"text-red-400 hover:text-red-300",children:[(0,s.jsx)(r.IXo,{className:"mr-1"}),"Delete"]})]})]})}),(0,s.jsxs)("div",{className:"space-y-3",children:[e.length>0&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 px-4",children:[(0,s.jsx)("input",{type:"checkbox",checked:b.length===e.length,onChange:()=>{b.length===e.length?j([]):j(e.map(e=>e.id))},className:"rounded border-gray-600 bg-gray-700 text-green-600"}),(0,s.jsx)("span",{className:"text-sm text-gray-400",children:"Select all"})]}),e.map(e=>{let t;return(0,s.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"".concat("UNREAD"===e.status?"bg-gray-800 border-green-500":"bg-gray-800 border-gray-700"," border rounded-lg p-4 hover:border-green-500 transition-colors"),children:(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("input",{type:"checkbox",checked:b.includes(e.id),onChange:()=>{var t;return t=e.id,void j(e=>e.includes(t)?e.filter(e=>e!==t):[...e,t])},className:"mt-1 rounded border-gray-600 bg-gray-700 text-green-600"}),(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center text-lg",children:g[e.type]||"\uD83D\uDCE2"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("h3",{className:"text-white font-medium",children:e.title}),(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(f[e.priority]||"bg-gray-500")}),"UNREAD"===e.status&&(0,s.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]}),(0,s.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:h.wy.truncateMessage(e.message,120)}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-xs text-gray-500",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)(r.Ohp,{className:"mr-1"}),(t=e.createdAt,h.wy.formatTimeAgo(t))]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[e.channels.includes("EMAIL")&&(0,s.jsx)(r.pHD,{}),e.channels.includes("SMS")&&(0,s.jsx)(r.PCV,{}),e.channels.includes("PUSH")&&(0,s.jsx)(r.ufi,{})]}),(0,s.jsx)(c.E,{variant:"URGENT"===e.priority?"error":"default",children:e.priority})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["UNREAD"===e.status&&(0,s.jsx)(o.$n,{size:"sm",variant:"outline",onClick:()=>w(e.id),children:(0,s.jsx)(r.YrT,{})}),(0,s.jsx)(o.$n,{size:"sm",variant:"outline",onClick:()=>C(e.id),children:(0,s.jsx)(r.OZ2,{})}),(0,s.jsx)(o.$n,{size:"sm",variant:"outline",onClick:()=>S(e.id),className:"text-red-400 hover:text-red-300",children:(0,s.jsx)(r.IXo,{})})]})]}),e.actionRequired&&e.actionUrl&&(0,s.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-700",children:(0,s.jsx)(o.$n,{size:"sm",className:"bg-green-600 hover:bg-green-700",onClick:()=>window.open(e.actionUrl,"_blank"),children:e.actionText||"Take Action"})})]})]})},e.id)}),0===e.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)(r.zd,{className:"mx-auto text-6xl text-gray-600 mb-4"}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Notifications"}),(0,s.jsx)("p",{className:"text-gray-400",children:"You're all caught up! Check back later for updates."})]})]}),e.length>=(p.limit||20)&&(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)(o.$n,{variant:"outline",onClick:()=>A({...p,limit:(p.limit||20)+20}),children:"Load More Notifications"})})]})})}},50990:(e,t,a)=>{Promise.resolve().then(a.bind(a,44361))},52814:(e,t,a)=>{"use strict";a.d(t,{A:()=>l,E:()=>i});var s=a(95155);function i(e){let{children:t,variant:a="default",size:i="md",className:l=""}=e,r="\n    ".concat("\n    inline-flex items-center justify-center font-medium rounded-full\n    transition-all duration-200\n  "," \n    ").concat({default:"bg-gray-700 text-gray-300 border border-gray-600",success:"bg-green-500/20 text-green-400 border border-green-500/30",warning:"bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",error:"bg-red-500/20 text-red-400 border border-red-500/30",info:"bg-blue-500/20 text-blue-400 border border-blue-500/30"}[a]," \n    ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[i]," \n    ").concat(l,"\n  ");return(0,s.jsx)("span",{className:r,children:t})}a(12115);let l=i}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,1846,411,8441,5964,7358],()=>e(e.s=50990)),_N_E=e.O()}]);