(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9653],{6115:(e,t,n)=>{"use strict";var r=n(12115),o=n(49033),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=o.useSyncExternalStore,l=r.useRef,s=r.useEffect,c=r.useMemo,u=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=a(e,(f=c(function(){function e(e){if(!s){if(s=!0,a=e,e=r(e),void 0!==o&&d.hasValue){var t=d.value;if(o(t,e))return l=t}return l=e}if(t=l,i(a,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(a=e,t):(a=e,l=n)}var a,l,s=!1,c=void 0===n?null:n;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,n,r,o]))[0],f[1]);return s(function(){d.hasValue=!0,d.value=p},[p]),u(p),p}},14915:function(e){e.exports=function(){var e=[function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},i=(r(n(1)),n(6)),a=r(i),l=r(n(7)),s=r(n(8)),c=r(n(9)),u=r(n(10)),f=r(n(11)),d=r(n(14)),p=[],m=!1,v={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,startEvent:"DOMContentLoaded",throttleDelay:99,debounceDelay:50,disableMutationObserver:!1},b=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(e&&(m=!0),m)return p=(0,f.default)(p,v),(0,u.default)(p,v.once),p},h=function(){p=(0,d.default)(),b()},g=function(){p.forEach(function(e,t){e.node.removeAttribute("data-aos"),e.node.removeAttribute("data-aos-easing"),e.node.removeAttribute("data-aos-duration"),e.node.removeAttribute("data-aos-delay")})};e.exports={init:function(e){v=o(v,e),p=(0,d.default)();var t,n=document.all&&!window.atob;return!0===(t=v.disable)||"mobile"===t&&c.default.mobile()||"phone"===t&&c.default.phone()||"tablet"===t&&c.default.tablet()||"function"==typeof t&&!0===t()||n?g():(v.disableMutationObserver||s.default.isSupported()||(console.info('\n      aos: MutationObserver is not supported on this browser,\n      code mutations observing has been disabled.\n      You may have to call "refreshHard()" by yourself.\n    '),v.disableMutationObserver=!0),document.querySelector("body").setAttribute("data-aos-easing",v.easing),document.querySelector("body").setAttribute("data-aos-duration",v.duration),document.querySelector("body").setAttribute("data-aos-delay",v.delay),"DOMContentLoaded"===v.startEvent&&["complete","interactive"].indexOf(document.readyState)>-1?b(!0):"load"===v.startEvent?window.addEventListener(v.startEvent,function(){b(!0)}):document.addEventListener(v.startEvent,function(){b(!0)}),window.addEventListener("resize",(0,l.default)(b,v.debounceDelay,!0)),window.addEventListener("orientationchange",(0,l.default)(b,v.debounceDelay,!0)),window.addEventListener("scroll",(0,a.default)(function(){(0,u.default)(p,v.once)},v.throttleDelay)),v.disableMutationObserver||s.default.ready("[data-aos]",h),p)},refresh:b,refreshHard:h}},function(e,t){},,,,,function(e,t){(function(t){"use strict";function n(e){var t=void 0===e?"undefined":o(e);return!!e&&("object"==t||"function"==t)}function r(e){if("number"==typeof e)return e;if("symbol"==(void 0===(t=e)?"undefined":o(t))||t&&"object"==(void 0===t?"undefined":o(t))&&b.call(t)==l)return a;if(n(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=n(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;var i=u.test(e=e.replace(s,""));return i||f.test(e)?d(e.slice(2),i?2:8):c.test(e)?a:+e}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i="Expected a function",a=NaN,l="[object Symbol]",s=/^\s+|\s+$/g,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,f=/^0o[0-7]+$/i,d=parseInt,p="object"==(void 0===t?"undefined":o(t))&&t&&t.Object===Object&&t,m="object"==("undefined"==typeof self?"undefined":o(self))&&self&&self.Object===Object&&self,v=p||m||Function("return this")(),b=Object.prototype.toString,h=Math.max,g=Math.min,y=function(){return v.Date.now()};e.exports=function(e,t,o){var a=!0,l=!0;if("function"!=typeof e)throw TypeError(i);return n(o)&&(a="leading"in o?!!o.leading:a,l="trailing"in o?!!o.trailing:l),function(e,t,o){function a(t){var n=f,r=d;return f=d=void 0,w=t,m=e.apply(r,n)}function l(e){var n=e-b,r=e-w;return void 0===b||n>=t||n<0||x&&r>=p}function s(){var e,n,r,o=y();return l(o)?c(o):void(v=setTimeout(s,(e=o-b,n=o-w,r=t-e,x?g(r,p-n):r)))}function c(e){return v=void 0,E&&f?a(e):(f=d=void 0,m)}function u(){var e,n=y(),r=l(n);if(f=arguments,d=this,b=n,r){if(void 0===v)return w=e=b,v=setTimeout(s,t),j?a(e):m;if(x)return v=setTimeout(s,t),a(b)}return void 0===v&&(v=setTimeout(s,t)),m}var f,d,p,m,v,b,w=0,j=!1,x=!1,E=!0;if("function"!=typeof e)throw TypeError(i);return t=r(t)||0,n(o)&&(j=!!o.leading,p=(x="maxWait"in o)?h(r(o.maxWait)||0,t):p,E="trailing"in o?!!o.trailing:E),u.cancel=function(){void 0!==v&&clearTimeout(v),w=0,f=b=d=v=void 0},u.flush=function(){return void 0===v?m:c(y())},u}(e,t,{leading:a,maxWait:t,trailing:l})}}).call(t,function(){return this}())},function(e,t){(function(t){"use strict";function n(e){var t=void 0===e?"undefined":o(e);return!!e&&("object"==t||"function"==t)}function r(e){if("number"==typeof e)return e;if("symbol"==(void 0===(t=e)?"undefined":o(t))||t&&"object"==(void 0===t?"undefined":o(t))&&v.call(t)==a)return i;if(n(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=n(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;var d=c.test(e=e.replace(l,""));return d||u.test(e)?f(e.slice(2),d?2:8):s.test(e)?i:+e}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},i=NaN,a="[object Symbol]",l=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,u=/^0o[0-7]+$/i,f=parseInt,d="object"==(void 0===t?"undefined":o(t))&&t&&t.Object===Object&&t,p="object"==("undefined"==typeof self?"undefined":o(self))&&self&&self.Object===Object&&self,m=d||p||Function("return this")(),v=Object.prototype.toString,b=Math.max,h=Math.min,g=function(){return m.Date.now()};e.exports=function(e,t,o){function i(t){var n=u,r=f;return u=f=void 0,y=t,p=e.apply(r,n)}function a(e){var n=e-v,r=e-y;return void 0===v||n>=t||n<0||j&&r>=d}function l(){var e,n,r,o=g();return a(o)?s(o):void(m=setTimeout(l,(e=o-v,n=o-y,r=t-e,j?h(r,d-n):r)))}function s(e){return m=void 0,x&&u?i(e):(u=f=void 0,p)}function c(){var e,n=g(),r=a(n);if(u=arguments,f=this,v=n,r){if(void 0===m)return y=e=v,m=setTimeout(l,t),w?i(e):p;if(j)return m=setTimeout(l,t),i(v)}return void 0===m&&(m=setTimeout(l,t)),p}var u,f,d,p,m,v,y=0,w=!1,j=!1,x=!0;if("function"!=typeof e)throw TypeError("Expected a function");return t=r(t)||0,n(o)&&(w=!!o.leading,d=(j="maxWait"in o)?b(r(o.maxWait)||0,t):d,x="trailing"in o?!!o.trailing:x),c.cancel=function(){void 0!==m&&clearTimeout(m),y=0,u=v=f=m=void 0},c.flush=function(){return void 0===m?p:s(g())},c}}).call(t,function(){return this}())},function(e,t){"use strict";function n(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function r(e){e&&e.forEach(function(e){var t=Array.prototype.slice.call(e.addedNodes),n=Array.prototype.slice.call(e.removedNodes);if(function e(t){var n=void 0,r=void 0;for(n=0;n<t.length;n+=1)if((r=t[n]).dataset&&r.dataset.aos||r.children&&e(r.children))return!0;return!1}(t.concat(n)))return o()})}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){};t.default={isSupported:function(){return!!n()},ready:function(e,t){var i=window.document,a=new(n())(r);o=t,a.observe(i.documentElement,{childList:!0,subtree:!0,removedNodes:!0})}}},function(e,t){"use strict";function n(){return navigator.userAgent||navigator.vendor||window.opera||""}Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),o=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,i=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,a=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,l=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i;t.default=new(function(){function e(){if(!(this instanceof e))throw TypeError("Cannot call a class as a function")}return r(e,[{key:"phone",value:function(){var e=n();return!(!o.test(e)&&!i.test(e.substr(0,4)))}},{key:"mobile",value:function(){var e=n();return!(!a.test(e)&&!l.test(e.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}}]),e}())},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(e,t,n){var r=e.node.getAttribute("data-aos-once");t>e.position?e.node.classList.add("aos-animate"):void 0===r||"false"!==r&&(n||"true"===r)||e.node.classList.remove("aos-animate")};t.default=function(e,t){var r=window.pageYOffset,o=window.innerHeight;e.forEach(function(e,i){n(e,o+r,t)})}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=(r=n(12))&&r.__esModule?r:{default:r};t.default=function(e,t){return e.forEach(function(e,n){e.node.classList.add("aos-init"),e.position=(0,o.default)(e.node,t.offset)}),e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=(r=n(13))&&r.__esModule?r:{default:r};t.default=function(e,t){var n=0,r=0,i=window.innerHeight,a={offset:e.getAttribute("data-aos-offset"),anchor:e.getAttribute("data-aos-anchor"),anchorPlacement:e.getAttribute("data-aos-anchor-placement")};switch(a.offset&&!isNaN(a.offset)&&(r=parseInt(a.offset)),a.anchor&&document.querySelectorAll(a.anchor)&&(e=document.querySelectorAll(a.anchor)[0]),n=(0,o.default)(e).top,a.anchorPlacement){case"top-bottom":break;case"center-bottom":n+=e.offsetHeight/2;break;case"bottom-bottom":n+=e.offsetHeight;break;case"top-center":n+=i/2;break;case"bottom-center":n+=i/2+e.offsetHeight;break;case"center-center":n+=i/2+e.offsetHeight/2;break;case"top-top":n+=i;break;case"bottom-top":n+=e.offsetHeight+i;break;case"center-top":n+=e.offsetHeight/2+i}return a.anchorPlacement||a.offset||isNaN(t)||(r=t),n+r}},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){for(var t=0,n=0;e&&!isNaN(e.offsetLeft)&&!isNaN(e.offsetTop);)t+=e.offsetLeft-("BODY"!=e.tagName?e.scrollLeft:0),n+=e.offsetTop-("BODY"!=e.tagName?e.scrollTop:0),e=e.offsetParent;return{top:n,left:t}}},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return e=e||document.querySelectorAll("[data-aos]"),Array.prototype.map.call(e,function(e){return{node:e}})}}];function t(r){if(n[r])return n[r].exports;var o=n[r]={exports:{},id:r,loaded:!1};return e[r].call(o.exports,o,o.exports,t),o.loaded=!0,o.exports}var n={};return t.m=e,t.c=n,t.p="dist/",t(0)}()},16500:(e,t)=>{"use strict";t.ConcurrentRoot=1,t.ContinuousEventPriority=8,t.DefaultEventPriority=32,t.DiscreteEventPriority=2},16892:(e,t)=>{"use strict";function n(e,t){var n=e.length;for(e.push(t);0<n;){var r=n-1>>>1,o=e[r];if(0<i(o,t))e[r]=t,e[n]=o,n=r;else break}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;for(var r=0,o=e.length,a=o>>>1;r<a;){var l=2*(r+1)-1,s=e[l],c=l+1,u=e[c];if(0>i(s,n))c<o&&0>i(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[l]=n,r=l);else if(c<o&&0>i(u,n))e[r]=u,e[c]=n,r=c;else break}}return t}function i(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var a,l=performance;t.unstable_now=function(){return l.now()}}else{var s=Date,c=s.now();t.unstable_now=function(){return s.now()-c}}var u=[],f=[],d=1,p=null,m=3,v=!1,b=!1,h=!1,g="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function j(e){for(var t=r(f);null!==t;){if(null===t.callback)o(f);else if(t.startTime<=e)o(f),t.sortIndex=t.expirationTime,n(u,t);else break;t=r(f)}}function x(e){if(h=!1,j(e),!b)if(null!==r(u))b=!0,A();else{var t=r(f);null!==t&&T(x,t.startTime-e)}}var E=!1,S=-1,k=5,C=-1;function M(){return!(t.unstable_now()-C<k)}function P(){if(E){var e=t.unstable_now();C=e;var n=!0;try{e:{b=!1,h&&(h=!1,y(S),S=-1),v=!0;var i=m;try{t:{for(j(e),p=r(u);null!==p&&!(p.expirationTime>e&&M());){var l=p.callback;if("function"==typeof l){p.callback=null,m=p.priorityLevel;var s=l(p.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof s){p.callback=s,j(e),n=!0;break t}p===r(u)&&o(u),j(e)}else o(u);p=r(u)}if(null!==p)n=!0;else{var c=r(f);null!==c&&T(x,c.startTime-e),n=!1}}break e}finally{p=null,m=i,v=!1}}}finally{n?a():E=!1}}}if("function"==typeof w)a=function(){w(P)};else if("undefined"!=typeof MessageChannel){var O=new MessageChannel,_=O.port2;O.port1.onmessage=P,a=function(){_.postMessage(null)}}else a=function(){g(P,0)};function A(){E||(E=!0,a())}function T(e,n){S=g(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){b||v||(b=!0,A())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):k=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return m},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},t.unstable_scheduleCallback=function(e,o,i){var a=t.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,e){case 1:var l=-1;break;case 2:l=250;break;case 5:l=0x3fffffff;break;case 4:l=1e4;break;default:l=5e3}return l=i+l,e={id:d++,callback:o,priorityLevel:e,startTime:i,expirationTime:l,sortIndex:-1},i>a?(e.sortIndex=i,n(f,e),null===r(u)&&e===r(f)&&(h?(y(S),S=-1):h=!0,T(x,i-a))):(e.sortIndex=l,n(u,e),b||v||(b=!0,A())),e},t.unstable_shouldYield=M,t.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}},22436:(e,t,n)=>{"use strict";var r=n(12115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,s=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,u=r[1];return l(function(){o.value=n,o.getSnapshot=t,c(o)&&u({inst:o})},[e,n,t]),a(function(){return c(o)&&u({inst:o}),e(function(){c(o)&&u({inst:o})})},[e]),s(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:u},26354:(e,t,n)=>{"use strict";n.d(t,{Af:()=>l,Nz:()=>o,u5:()=>s,y3:()=>f});var r=n(12115);function o(e,t,n){if(!e)return;if(!0===n(e))return e;let r=t?e.return:e.child;for(;r;){let e=o(r,t,n);if(e)return e;r=t?null:r.sibling}}function i(e){try{return Object.defineProperties(e,{_currentRenderer:{get:()=>null,set(){}},_currentRenderer2:{get:()=>null,set(){}}})}catch(t){return e}}(()=>{var e,t;return"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative")})()?r.useLayoutEffect:r.useEffect;let a=i(r.createContext(null));class l extends r.Component{render(){return r.createElement(a.Provider,{value:this._reactInternals},this.props.children)}}function s(){let e=r.useContext(a);if(null===e)throw Error("its-fine: useFiber must be called within a <FiberProvider />!");let t=r.useId();return r.useMemo(()=>{for(let n of[e,null==e?void 0:e.alternate]){if(!n)continue;let e=o(n,!1,e=>{let n=e.memoizedState;for(;n;){if(n.memoizedState===t)return!0;n=n.next}});if(e)return e}},[e,t])}let c=Symbol.for("react.context"),u=e=>null!==e&&"object"==typeof e&&"$$typeof"in e&&e.$$typeof===c;function f(){let e=function(){let e=s(),[t]=r.useState(()=>new Map);t.clear();let n=e;for(;n;){let e=n.type;u(e)&&e!==a&&!t.has(e)&&t.set(e,r.use(i(e))),n=n.return}return t}();return r.useMemo(()=>Array.from(e.keys()).reduce((t,n)=>o=>r.createElement(t,null,r.createElement(n.Provider,{...o,value:e.get(n)})),e=>r.createElement(l,{...e})),[e])}},45220:(e,t,n)=>{"use strict";e.exports=n(21724)},45643:(e,t,n)=>{"use strict";e.exports=n(6115)},49033:(e,t,n)=>{"use strict";e.exports=n(22436)},49537:()=>{},61933:(e,t,n)=>{"use strict";e.exports=n(16500)},67558:(e,t,n)=>{"use strict";n.d(t,{Hl:()=>f});var r=n(71949),o=n(12115),i=n(97431);function a(e,t){let n;return(...r)=>{window.clearTimeout(n),n=window.setTimeout(()=>e(...r),t)}}let l=["x","y","top","bottom","left","right","width","height"];var s=n(26354),c=n(95155);function u({ref:e,children:t,fallback:n,resize:s,style:u,gl:f,events:d=r.f,eventSource:p,eventPrefix:m,shadows:v,linear:b,flat:h,legacy:g,orthographic:y,frameloop:w,dpr:j,performance:x,raycaster:E,camera:S,scene:k,onPointerMissed:C,onCreated:M,...P}){o.useMemo(()=>(0,r.e)(i),[]);let O=(0,r.u)(),[_,A]=function({debounce:e,scroll:t,polyfill:n,offsetSize:r}={debounce:0,scroll:!1,offsetSize:!1}){var i,s,c;let u=n||("undefined"==typeof window?class{}:window.ResizeObserver);if(!u)throw Error("This browser does not support ResizeObserver out of the box. See: https://github.com/react-spring/react-use-measure/#resize-observer-polyfills");let[f,d]=(0,o.useState)({left:0,top:0,width:0,height:0,bottom:0,right:0,x:0,y:0}),p=(0,o.useRef)({element:null,scrollContainers:null,resizeObserver:null,lastBounds:f,orientationHandler:null}),m=e?"number"==typeof e?e:e.scroll:null,v=e?"number"==typeof e?e:e.resize:null,b=(0,o.useRef)(!1);(0,o.useEffect)(()=>(b.current=!0,()=>void(b.current=!1)));let[h,g,y]=(0,o.useMemo)(()=>{let e=()=>{let e,t;if(!p.current.element)return;let{left:n,top:o,width:i,height:a,bottom:s,right:c,x:u,y:f}=p.current.element.getBoundingClientRect(),m={left:n,top:o,width:i,height:a,bottom:s,right:c,x:u,y:f};p.current.element instanceof HTMLElement&&r&&(m.height=p.current.element.offsetHeight,m.width=p.current.element.offsetWidth),Object.freeze(m),b.current&&(e=p.current.lastBounds,t=m,!l.every(n=>e[n]===t[n]))&&d(p.current.lastBounds=m)};return[e,v?a(e,v):e,m?a(e,m):e]},[d,r,m,v]);function w(){p.current.scrollContainers&&(p.current.scrollContainers.forEach(e=>e.removeEventListener("scroll",y,!0)),p.current.scrollContainers=null),p.current.resizeObserver&&(p.current.resizeObserver.disconnect(),p.current.resizeObserver=null),p.current.orientationHandler&&("orientation"in screen&&"removeEventListener"in screen.orientation?screen.orientation.removeEventListener("change",p.current.orientationHandler):"onorientationchange"in window&&window.removeEventListener("orientationchange",p.current.orientationHandler))}function j(){p.current.element&&(p.current.resizeObserver=new u(y),p.current.resizeObserver.observe(p.current.element),t&&p.current.scrollContainers&&p.current.scrollContainers.forEach(e=>e.addEventListener("scroll",y,{capture:!0,passive:!0})),p.current.orientationHandler=()=>{y()},"orientation"in screen&&"addEventListener"in screen.orientation?screen.orientation.addEventListener("change",p.current.orientationHandler):"onorientationchange"in window&&window.addEventListener("orientationchange",p.current.orientationHandler))}return i=y,s=!!t,(0,o.useEffect)(()=>{if(s)return window.addEventListener("scroll",i,{capture:!0,passive:!0}),()=>void window.removeEventListener("scroll",i,!0)},[i,s]),c=g,(0,o.useEffect)(()=>(window.addEventListener("resize",c),()=>void window.removeEventListener("resize",c)),[c]),(0,o.useEffect)(()=>{w(),j()},[t,y,g]),(0,o.useEffect)(()=>w,[]),[e=>{e&&e!==p.current.element&&(w(),p.current.element=e,p.current.scrollContainers=function e(t){let n=[];if(!t||t===document.body)return n;let{overflow:r,overflowX:o,overflowY:i}=window.getComputedStyle(t);return[r,o,i].some(e=>"auto"===e||"scroll"===e)&&n.push(t),[...n,...e(t.parentElement)]}(e),j())},f,h]}({scroll:!0,debounce:{scroll:50,resize:0},...s}),T=o.useRef(null),z=o.useRef(null);o.useImperativeHandle(e,()=>T.current);let L=(0,r.a)(C),[I,D]=o.useState(!1),[R,H]=o.useState(!1);if(I)throw I;if(R)throw R;let F=o.useRef(null);(0,r.b)(()=>{let e=T.current;A.width>0&&A.height>0&&e&&(F.current||(F.current=(0,r.c)(e)),async function(){await F.current.configure({gl:f,scene:k,events:d,shadows:v,linear:b,flat:h,legacy:g,orthographic:y,frameloop:w,dpr:j,performance:x,raycaster:E,camera:S,size:A,onPointerMissed:(...e)=>null==L.current?void 0:L.current(...e),onCreated:e=>{null==e.events.connect||e.events.connect(p?(0,r.i)(p)?p.current:p:z.current),m&&e.setEvents({compute:(e,t)=>{let n=e[m+"X"],r=e[m+"Y"];t.pointer.set(n/t.size.width*2-1,-(2*(r/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)}}),null==M||M(e)}}),F.current.render((0,c.jsx)(O,{children:(0,c.jsx)(r.E,{set:H,children:(0,c.jsx)(o.Suspense,{fallback:(0,c.jsx)(r.B,{set:D}),children:null!=t?t:null})})}))}())}),o.useEffect(()=>{let e=T.current;if(e)return()=>(0,r.d)(e)},[]);let q=p?"none":"auto";return(0,c.jsx)("div",{ref:z,style:{position:"relative",width:"100%",height:"100%",overflow:"hidden",pointerEvents:q,...u},...P,children:(0,c.jsx)("div",{ref:_,style:{width:"100%",height:"100%"},children:(0,c.jsx)("canvas",{ref:T,style:{display:"block"},children:n})})})}function f(e){return(0,c.jsx)(s.Af,{children:(0,c.jsx)(u,{...e})})}n(61933),n(45220),n(72407)},71949:(e,t,n)=>{"use strict";let r,o,i,a,l;n.d(t,{B:()=>_,C:()=>Q,D:()=>ee,E:()=>A,a:()=>P,b:()=>M,c:()=>eE,d:()=>ek,e:()=>es,f:()=>eH,i:()=>k,u:()=>O});var s=n(97431),c=n(12115),u=n.t(c,2),f=n(61933),d=n(45643);let p=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},{useSyncExternalStoreWithSelector:m}=d,v=(e,t)=>{let n=(e=>e?p(e):p)(e),r=(e,r=t)=>(function(e,t=e=>e,n){let r=m(e.subscribe,e.getState,e.getInitialState,t,n);return c.useDebugValue(r),r})(n,e,r);return Object.assign(r,n),r};var b=n(45220),h=n.n(b),g=n(72407);let y=[];function w(e,t,n=(e,t)=>e===t){if(e===t)return!0;if(!e||!t)return!1;let r=e.length;if(t.length!==r)return!1;for(let o=0;o<r;o++)if(!n(e[o],t[o]))return!1;return!0}function j(e,t=null,n=!1,r={}){for(let o of(null===t&&(t=[e]),y))if(w(t,o.keys,o.equal)){if(n)return;if(Object.prototype.hasOwnProperty.call(o,"error"))throw o.error;if(Object.prototype.hasOwnProperty.call(o,"response"))return r.lifespan&&r.lifespan>0&&(o.timeout&&clearTimeout(o.timeout),o.timeout=setTimeout(o.remove,r.lifespan)),o.response;if(!n)throw o.promise}let o={keys:t,equal:r.equal,remove:()=>{let e=y.indexOf(o);-1!==e&&y.splice(e,1)},promise:("object"==typeof e&&"function"==typeof e.then?e:e(...t)).then(e=>{o.response=e,r.lifespan&&r.lifespan>0&&(o.timeout=setTimeout(o.remove,r.lifespan))}).catch(e=>o.error=e)};if(y.push(o),!n)throw o.promise}var x=n(95155),E=n(26354);function S(e){let t=e.root;for(;t.getState().previousRoot;)t=t.getState().previousRoot;return t}n(49509),u.act;let k=e=>e&&e.hasOwnProperty("current"),C=e=>null!=e&&("string"==typeof e||"number"==typeof e||e.isColor),M=((e,t)=>"undefined"!=typeof window&&((null==(e=window.document)?void 0:e.createElement)||(null==(t=window.navigator)?void 0:t.product)==="ReactNative"))()?c.useLayoutEffect:c.useEffect;function P(e){let t=c.useRef(e);return M(()=>void(t.current=e),[e]),t}function O(){let e=(0,E.u5)(),t=(0,E.y3)();return c.useMemo(()=>({children:n})=>{let r=(0,E.Nz)(e,!0,e=>e.type===c.StrictMode)?c.StrictMode:c.Fragment;return(0,x.jsx)(r,{children:(0,x.jsx)(t,{children:n})})},[e,t])}function _({set:e}){return M(()=>(e(new Promise(()=>null)),()=>e(!1)),[e]),null}let A=(e=>((e=class extends c.Component{constructor(...e){super(...e),this.state={error:!1}}componentDidCatch(e){this.props.set(e)}render(){return this.state.error?null:this.props.children}}).getDerivedStateFromError=()=>({error:!0}),e))();function T(e){var t;let n="undefined"!=typeof window?null!=(t=window.devicePixelRatio)?t:2:1;return Array.isArray(e)?Math.min(Math.max(e[0],n),e[1]):e}function z(e){var t;return null==(t=e.__r3f)?void 0:t.root.getState()}let L={obj:e=>e===Object(e)&&!L.arr(e)&&"function"!=typeof e,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,boo:e=>"boolean"==typeof e,und:e=>void 0===e,nul:e=>null===e,arr:e=>Array.isArray(e),equ(e,t,{arrays:n="shallow",objects:r="reference",strict:o=!0}={}){let i;if(typeof e!=typeof t||!!e!=!!t)return!1;if(L.str(e)||L.num(e)||L.boo(e))return e===t;let a=L.obj(e);if(a&&"reference"===r)return e===t;let l=L.arr(e);if(l&&"reference"===n)return e===t;if((l||a)&&e===t)return!0;for(i in e)if(!(i in t))return!1;if(a&&"shallow"===n&&"shallow"===r){for(i in o?t:e)if(!L.equ(e[i],t[i],{strict:o,objects:"reference"}))return!1}else for(i in o?t:e)if(e[i]!==t[i])return!1;if(L.und(i)){if(l&&0===e.length&&0===t.length||a&&0===Object.keys(e).length&&0===Object.keys(t).length)return!0;if(e!==t)return!1}return!0}},I=["children","key","ref"];function D(e,t,n,r){let o=null==e?void 0:e.__r3f;return!o&&(o={root:t,type:n,parent:null,children:[],props:function(e){let t={};for(let n in e)I.includes(n)||(t[n]=e[n]);return t}(r),object:e,eventCount:0,handlers:{},isHidden:!1},e&&(e.__r3f=o)),o}function R(e,t){let n=e[t];if(!t.includes("-"))return{root:e,key:t,target:n};for(let o of(n=e,t.split("-"))){var r;t=o,e=n,n=null==(r=n)?void 0:r[t]}return{root:e,key:t,target:n}}let H=/-\d+$/;function F(e,t){if(L.str(t.props.attach)){if(H.test(t.props.attach)){let n=t.props.attach.replace(H,""),{root:r,key:o}=R(e.object,n);Array.isArray(r[o])||(r[o]=[])}let{root:n,key:r}=R(e.object,t.props.attach);t.previousAttach=n[r],n[r]=t.object}else L.fun(t.props.attach)&&(t.previousAttach=t.props.attach(e.object,t.object))}function q(e,t){if(L.str(t.props.attach)){let{root:n,key:r}=R(e.object,t.props.attach),o=t.previousAttach;void 0===o?delete n[r]:n[r]=o}else null==t.previousAttach||t.previousAttach(e.object,t.object);delete t.previousAttach}let N=[...I,"args","dispose","attach","object","onUpdate","dispose"],U=new Map,$=["map","emissiveMap","sheenColorMap","specularColorMap","envMap"],B=/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/;function W(e,t){var n,r;let o=e.__r3f,i=o&&S(o).getState(),a=null==o?void 0:o.eventCount;for(let n in t){let a=t[n];if(N.includes(n))continue;if(o&&B.test(n)){"function"==typeof a?o.handlers[n]=a:delete o.handlers[n],o.eventCount=Object.keys(o.handlers).length;continue}if(void 0===a)continue;let{root:l,key:c,target:u}=R(e,n);u instanceof s.Layers&&a instanceof s.Layers?u.mask=a.mask:u instanceof s.Color&&C(a)?u.set(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"function"==typeof u.copy&&null!=a&&a.constructor&&u.constructor===a.constructor?u.copy(a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&Array.isArray(a)?"function"==typeof u.fromArray?u.fromArray(a):u.set(...a):null!==u&&"object"==typeof u&&"function"==typeof u.set&&"number"==typeof a?"function"==typeof u.setScalar?u.setScalar(a):u.set(a):(l[c]=a,i&&!i.linear&&$.includes(c)&&null!=(r=l[c])&&r.isTexture&&l[c].format===s.RGBAFormat&&l[c].type===s.UnsignedByteType&&(l[c].colorSpace=s.SRGBColorSpace))}if(null!=o&&o.parent&&null!=i&&i.internal&&null!=(n=o.object)&&n.isObject3D&&a!==o.eventCount){let e=o.object,t=i.internal.interaction.indexOf(e);t>-1&&i.internal.interaction.splice(t,1),o.eventCount&&null!==e.raycast&&i.internal.interaction.push(e)}return o&&void 0===o.props.attach&&(o.object.isBufferGeometry?o.props.attach="geometry":o.object.isMaterial&&(o.props.attach="material")),o&&V(o),e}function V(e){var t;if(!e.parent)return;null==e.props.onUpdate||e.props.onUpdate(e.object);let n=null==(t=e.root)||null==t.getState?void 0:t.getState();n&&0===n.internal.frames&&n.invalidate()}let G=e=>null==e?void 0:e.isObject3D;function Y(e){return(e.eventObject||e.object).uuid+"/"+e.index+e.instanceId}function X(e,t,n,r){let o=n.get(t);o&&(n.delete(t),0===n.size&&(e.delete(r),o.target.releasePointerCapture(r)))}let K=e=>!!(null!=e&&e.render),Z=c.createContext(null);function J(){let e=c.useContext(Z);if(!e)throw Error("R3F: Hooks can only be used within the Canvas component!");return e}function Q(e=e=>e,t){return J()(e,t)}function ee(e,t=0){let n=J(),r=n.getState().internal.subscribe,o=P(e);return M(()=>r(o,t,n),[t,r,n]),null}let et=new WeakMap;function en(e,t){return function(n,...r){var o;let i;return"function"==typeof n&&(null==n||null==(o=n.prototype)?void 0:o.constructor)===n?(i=et.get(n))||(i=new n,et.set(n,i)):i=n,e&&e(i),Promise.all(r.map(e=>new Promise((n,r)=>i.load(e,e=>{G(null==e?void 0:e.scene)&&Object.assign(e,function(e){let t={nodes:{},materials:{},meshes:{}};return e&&e.traverse(e=>{e.name&&(t.nodes[e.name]=e),e.material&&!t.materials[e.material.name]&&(t.materials[e.material.name]=e.material),e.isMesh&&!t.meshes[e.name]&&(t.meshes[e.name]=e)}),t}(e.scene)),n(e)},t,t=>r(Error(`Could not load ${e}: ${null==t?void 0:t.message}`))))))}}function er(e,t,n,r){let o=Array.isArray(t)?t:[t],i=j(en(n,r),[e,...o],!1,{equal:L.equ});return Array.isArray(t)?i:i[0]}er.preload=function(e,t,n){let r,o=Array.isArray(t)?t:[t];j(en(n),[e,...o],!0,r)},er.clear=function(e,t){var n=[e,...Array.isArray(t)?t:[t]];if(void 0===n||0===n.length)y.splice(0,y.length);else{let e=y.find(e=>w(n,e.keys,e.equal));e&&e.remove()}};let eo={},ei=/^three(?=[A-Z])/,ea=e=>`${e[0].toUpperCase()}${e.slice(1)}`,el=0;function es(e){if("function"==typeof e){let t=`${el++}`;return eo[t]=e,t}Object.assign(eo,e)}function ec(e,t){let n=ea(e),r=eo[n];if("primitive"!==e&&!r)throw Error(`R3F: ${n} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);if("primitive"===e&&!t.object)throw Error("R3F: Primitives without 'object' are invalid!");if(void 0!==t.args&&!Array.isArray(t.args))throw Error("R3F: The args prop must be an array!")}function eu(e){if(e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?F(e.parent,e):G(e.object)&&!1!==e.props.visible&&(e.object.visible=!0),e.isHidden=!1,V(e)}}function ef(e,t,n){let r=t.root.getState();if(e.parent||e.object===r.scene){if(!t.object){var o,i;let e=eo[ea(t.type)];t.object=null!=(o=t.props.object)?o:new e(...null!=(i=t.props.args)?i:[]),t.object.__r3f=t}if(W(t.object,t.props),t.props.attach)F(e,t);else if(G(t.object)&&G(e.object)){let r=e.object.children.indexOf(null==n?void 0:n.object);if(n&&-1!==r){let n=e.object.children.indexOf(t.object);-1!==n?(e.object.children.splice(n,1),e.object.children.splice(n<r?r-1:r,0,t.object)):(t.object.parent=e.object,e.object.children.splice(r,0,t.object),t.object.dispatchEvent({type:"added"}),e.object.dispatchEvent({type:"childadded",child:t.object}))}else e.object.add(t.object)}for(let e of t.children)ef(t,e);V(t)}}function ed(e,t){t&&(t.parent=e,e.children.push(t),ef(e,t))}function ep(e,t,n){if(!t||!n)return;t.parent=e;let r=e.children.indexOf(n);-1!==r?e.children.splice(r,0,t):e.children.push(t),ef(e,t,n)}function em(e){if("function"==typeof e.dispose){let t=()=>{try{e.dispose()}catch{}};"undefined"!=typeof IS_REACT_ACT_ENVIRONMENT?t():(0,g.unstable_scheduleCallback)(g.unstable_IdlePriority,t)}}function ev(e,t,n){if(!t)return;t.parent=null;let r=e.children.indexOf(t);-1!==r&&e.children.splice(r,1),t.props.attach?q(e,t):G(t.object)&&G(e.object)&&(e.object.remove(t.object),function(e,t){let{internal:n}=e.getState();n.interaction=n.interaction.filter(e=>e!==t),n.initialHits=n.initialHits.filter(e=>e!==t),n.hovered.forEach((e,r)=>{(e.eventObject===t||e.object===t)&&n.hovered.delete(r)}),n.capturedMap.forEach((e,r)=>{X(n.capturedMap,t,e,r)})}(S(t),t.object));let o=null!==t.props.dispose&&!1!==n;for(let e=t.children.length-1;e>=0;e--){let n=t.children[e];ev(t,n,o)}t.children.length=0,delete t.object.__r3f,o&&"primitive"!==t.type&&"Scene"!==t.object.type&&em(t.object),void 0===n&&V(t)}let eb=[],eh=()=>{},eg={},ey=0,ew=function(e){let t=h()(e);return t.injectIntoDevTools({bundleType:0,rendererPackageName:"@react-three/fiber",version:c.version}),t}({isPrimaryRenderer:!1,warnsIfNotActing:!1,supportsMutation:!0,supportsPersistence:!1,supportsHydration:!1,createInstance:function(e,t,n){var r;return ec(e=ea(e)in eo?e:e.replace(ei,""),t),"primitive"===e&&null!=(r=t.object)&&r.__r3f&&delete t.object.__r3f,D(t.object,n,e,t)},removeChild:ev,appendChild:ed,appendInitialChild:ed,insertBefore:ep,appendChildToContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&ed(n,t)},removeChildFromContainer(e,t){let n=e.getState().scene.__r3f;t&&n&&ev(n,t)},insertInContainerBefore(e,t,n){let r=e.getState().scene.__r3f;t&&n&&r&&ep(r,t,n)},getRootHostContext:()=>eg,getChildHostContext:()=>eg,commitUpdate(e,t,n,r,o){var i,a,l;ec(t,r);let s=!1;if("primitive"===e.type&&n.object!==r.object||(null==(i=r.args)?void 0:i.length)!==(null==(a=n.args)?void 0:a.length)?s=!0:null!=(l=r.args)&&l.some((e,t)=>{var r;return e!==(null==(r=n.args)?void 0:r[t])})&&(s=!0),s)eb.push([e,{...r},o]);else{let t=function(e,t){let n={};for(let r in t)if(!N.includes(r)&&!L.equ(t[r],e.props[r]))for(let e in n[r]=t[r],t)e.startsWith(`${r}-`)&&(n[e]=t[e]);for(let r in e.props){if(N.includes(r)||t.hasOwnProperty(r))continue;let{root:o,key:i}=R(e.object,r);if(o.constructor&&0===o.constructor.length){let e=function(e){let t=U.get(e.constructor);try{t||(t=new e.constructor,U.set(e.constructor,t))}catch(e){}return t}(o);L.und(e)||(n[i]=e[i])}else n[i]=0}return n}(e,r);Object.keys(t).length&&(Object.assign(e.props,t),W(e.object,t))}(null===o.sibling||(4&o.flags)==0)&&function(){for(let[e]of eb){let t=e.parent;if(t)for(let n of(e.props.attach?q(t,e):G(e.object)&&G(t.object)&&t.object.remove(e.object),e.children))n.props.attach?q(e,n):G(n.object)&&G(e.object)&&e.object.remove(n.object);e.isHidden&&eu(e),e.object.__r3f&&delete e.object.__r3f,"primitive"!==e.type&&em(e.object)}for(let[r,o,i]of eb){r.props=o;let a=r.parent;if(a){let o=eo[ea(r.type)];r.object=null!=(e=r.props.object)?e:new o(...null!=(t=r.props.args)?t:[]),r.object.__r3f=r;var e,t,n=r.object;for(let e of[i,i.alternate])if(null!==e)if("function"==typeof e.ref){null==e.refCleanup||e.refCleanup();let t=e.ref(n);"function"==typeof t&&(e.refCleanup=t)}else e.ref&&(e.ref.current=n);for(let e of(W(r.object,r.props),r.props.attach?F(a,r):G(r.object)&&G(a.object)&&a.object.add(r.object),r.children))e.props.attach?F(r,e):G(e.object)&&G(r.object)&&r.object.add(e.object);V(r)}}eb.length=0}()},finalizeInitialChildren:()=>!1,commitMount(){},getPublicInstance:e=>null==e?void 0:e.object,prepareForCommit:()=>null,preparePortalMount:e=>D(e.getState().scene,e,"",{}),resetAfterCommit:()=>{},shouldSetTextContent:()=>!1,clearContainer:()=>!1,hideInstance:function(e){if(!e.isHidden){var t;e.props.attach&&null!=(t=e.parent)&&t.object?q(e.parent,e):G(e.object)&&(e.object.visible=!1),e.isHidden=!0,V(e)}},unhideInstance:eu,createTextInstance:eh,hideTextInstance:eh,unhideTextInstance:eh,scheduleTimeout:"function"==typeof setTimeout?setTimeout:void 0,cancelTimeout:"function"==typeof clearTimeout?clearTimeout:void 0,noTimeout:-1,getInstanceFromNode:()=>null,beforeActiveInstanceBlur(){},afterActiveInstanceBlur(){},detachDeletedInstance(){},prepareScopeUpdate(){},getInstanceFromScope:()=>null,shouldAttemptEagerTransition:()=>!1,trackSchedulerEvent:()=>{},resolveEventType:()=>null,resolveEventTimeStamp:()=>-1.1,requestPostPaintCallback(){},maySuspendCommit:()=>!1,preloadInstance:()=>!0,startSuspendingCommit(){},suspendInstance(){},waitForCommitToBeReady:()=>null,NotPendingTransition:null,HostTransitionContext:c.createContext(null),setCurrentUpdatePriority(e){ey=e},getCurrentUpdatePriority:()=>ey,resolveUpdatePriority(){var e;if(0!==ey)return ey;switch("undefined"!=typeof window&&(null==(e=window.event)?void 0:e.type)){case"click":case"contextmenu":case"dblclick":case"pointercancel":case"pointerdown":case"pointerup":return f.DiscreteEventPriority;case"pointermove":case"pointerout":case"pointerover":case"pointerenter":case"pointerleave":case"wheel":return f.ContinuousEventPriority;default:return f.DefaultEventPriority}},resetFormInstance(){}}),ej=new Map,ex={objects:"shallow",strict:!1};function eE(e){let t,n,r=ej.get(e),o=null==r?void 0:r.fiber,i=null==r?void 0:r.store;r&&console.warn("R3F.createRoot should only be called once!");let a="function"==typeof reportError?reportError:console.error,l=i||((e,t)=>{let n,r,o=(n=(n,r)=>{let o,i=new s.Vector3,a=new s.Vector3,l=new s.Vector3;function u(e=r().camera,t=a,n=r().size){let{width:o,height:s,top:c,left:f}=n,d=o/s;t.isVector3?l.copy(t):l.set(...t);let p=e.getWorldPosition(i).distanceTo(l);if(e&&e.isOrthographicCamera)return{width:o/e.zoom,height:s/e.zoom,top:c,left:f,factor:1,distance:p,aspect:d};{let t=2*Math.tan(e.fov*Math.PI/180/2)*p,n=o/s*t;return{width:n,height:t,top:c,left:f,factor:o/n,distance:p,aspect:d}}}let f=e=>n(t=>({performance:{...t.performance,current:e}})),d=new s.Vector2;return{set:n,get:r,gl:null,camera:null,raycaster:null,events:{priority:1,enabled:!0,connected:!1},scene:null,xr:null,invalidate:(t=1)=>e(r(),t),advance:(e,n)=>t(e,n,r()),legacy:!1,linear:!1,flat:!1,controls:null,clock:new s.Clock,pointer:d,mouse:d,frameloop:"always",onPointerMissed:void 0,performance:{current:1,min:.5,max:1,debounce:200,regress:()=>{let e=r();o&&clearTimeout(o),e.performance.current!==e.performance.min&&f(e.performance.min),o=setTimeout(()=>f(r().performance.max),e.performance.debounce)}},size:{width:0,height:0,top:0,left:0},viewport:{initialDpr:0,dpr:0,width:0,height:0,top:0,left:0,aspect:0,distance:0,factor:0,getCurrentViewport:u},setEvents:e=>n(t=>({...t,events:{...t.events,...e}})),setSize:(e,t,o=0,i=0)=>{let l=r().camera,s={width:e,height:t,top:o,left:i};n(e=>({size:s,viewport:{...e.viewport,...u(l,a,s)}}))},setDpr:e=>n(t=>{let n=T(e);return{viewport:{...t.viewport,dpr:n,initialDpr:t.viewport.initialDpr||n}}}),setFrameloop:(e="always")=>{let t=r().clock;t.stop(),t.elapsedTime=0,"never"!==e&&(t.start(),t.elapsedTime=0),n(()=>({frameloop:e}))},previousRoot:void 0,internal:{interaction:[],hovered:new Map,subscribers:[],initialClick:[0,0],initialHits:[],capturedMap:new Map,lastEvent:c.createRef(),active:!1,frames:0,priority:0,subscribe:(e,t,n)=>{let o=r().internal;return o.priority=o.priority+ +(t>0),o.subscribers.push({ref:e,priority:t,store:n}),o.subscribers=o.subscribers.sort((e,t)=>e.priority-t.priority),()=>{let n=r().internal;null!=n&&n.subscribers&&(n.priority=n.priority-(t>0),n.subscribers=n.subscribers.filter(t=>t.ref!==e))}}}}})?v(n,r):v,i=o.getState(),a=i.size,l=i.viewport.dpr,u=i.camera;return o.subscribe(()=>{let{camera:e,size:t,viewport:n,gl:r,set:i}=o.getState();if(t.width!==a.width||t.height!==a.height||n.dpr!==l){a=t,l=n.dpr,function(e,t){!e.manual&&(e&&e.isOrthographicCamera?(e.left=-(t.width/2),e.right=t.width/2,e.top=t.height/2,e.bottom=-(t.height/2)):e.aspect=t.width/t.height,e.updateProjectionMatrix())}(e,t),n.dpr>0&&r.setPixelRatio(n.dpr);let o="undefined"!=typeof HTMLCanvasElement&&r.domElement instanceof HTMLCanvasElement;r.setSize(t.width,t.height,o)}e!==u&&(u=e,i(t=>({viewport:{...t.viewport,...t.viewport.getCurrentViewport(e)}})))}),o.subscribe(t=>e(t)),o})(eI,eD),u=o||ew.createContainer(l,f.ConcurrentRoot,null,!1,null,"",a,a,a,null);r||ej.set(e,{fiber:u,store:l});let d=!1,p=null;return{async configure(r={}){var o,i;let a;p=new Promise(e=>a=e);let{gl:c,size:u,scene:f,events:m,onCreated:v,shadows:b=!1,linear:h=!1,flat:g=!1,legacy:y=!1,orthographic:w=!1,frameloop:j="always",dpr:x=[1,2],performance:E,raycaster:S,camera:k,onPointerMissed:C}=r,M=l.getState(),P=M.gl;if(!M.gl){let t={canvas:e,powerPreference:"high-performance",antialias:!0,alpha:!0},n="function"==typeof c?await c(t):c;P=K(n)?n:new s.WebGLRenderer({...t,...c}),M.set({gl:P})}let O=M.raycaster;O||M.set({raycaster:O=new s.Raycaster});let{params:_,...A}=S||{};if(L.equ(A,O,ex)||W(O,{...A}),L.equ(_,O.params,ex)||W(O,{params:{...O.params,..._}}),!M.camera||M.camera===n&&!L.equ(n,k,ex)){n=k;let e=null==k?void 0:k.isCamera,t=e?k:w?new s.OrthographicCamera(0,0,0,0,.1,1e3):new s.PerspectiveCamera(75,0,.1,1e3);!e&&(t.position.z=5,k&&(W(t,k),!t.manual&&("aspect"in k||"left"in k||"right"in k||"bottom"in k||"top"in k)&&(t.manual=!0,t.updateProjectionMatrix())),M.camera||null!=k&&k.rotation||t.lookAt(0,0,0)),M.set({camera:t}),O.camera=t}if(!M.scene){let e;null!=f&&f.isScene?D(e=f,l,"",{}):(D(e=new s.Scene,l,"",{}),f&&W(e,f)),M.set({scene:e})}m&&!M.events.handlers&&M.set({events:m(l)});let z=function(e,t){if(!t&&"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&e.parentElement){let{width:t,height:n,top:r,left:o}=e.parentElement.getBoundingClientRect();return{width:t,height:n,top:r,left:o}}return!t&&"undefined"!=typeof OffscreenCanvas&&e instanceof OffscreenCanvas?{width:e.width,height:e.height,top:0,left:0}:{width:0,height:0,top:0,left:0,...t}}(e,u);if(L.equ(z,M.size,ex)||M.setSize(z.width,z.height,z.top,z.left),x&&M.viewport.dpr!==T(x)&&M.setDpr(x),M.frameloop!==j&&M.setFrameloop(j),M.onPointerMissed||M.set({onPointerMissed:C}),E&&!L.equ(E,M.performance,ex)&&M.set(e=>({performance:{...e.performance,...E}})),!M.xr){let e=(e,t)=>{let n=l.getState();"never"!==n.frameloop&&eD(e,!0,n,t)},t=()=>{let t=l.getState();t.gl.xr.enabled=t.gl.xr.isPresenting,t.gl.xr.setAnimationLoop(t.gl.xr.isPresenting?e:null),t.gl.xr.isPresenting||eI(t)},n={connect(){let e=l.getState().gl;e.xr.addEventListener("sessionstart",t),e.xr.addEventListener("sessionend",t)},disconnect(){let e=l.getState().gl;e.xr.removeEventListener("sessionstart",t),e.xr.removeEventListener("sessionend",t)}};"function"==typeof(null==(o=P.xr)?void 0:o.addEventListener)&&n.connect(),M.set({xr:n})}if(P.shadowMap){let e=P.shadowMap.enabled,t=P.shadowMap.type;if(P.shadowMap.enabled=!!b,L.boo(b))P.shadowMap.type=s.PCFSoftShadowMap;else if(L.str(b)){let e={basic:s.BasicShadowMap,percentage:s.PCFShadowMap,soft:s.PCFSoftShadowMap,variance:s.VSMShadowMap};P.shadowMap.type=null!=(i=e[b])?i:s.PCFSoftShadowMap}else L.obj(b)&&Object.assign(P.shadowMap,b);(e!==P.shadowMap.enabled||t!==P.shadowMap.type)&&(P.shadowMap.needsUpdate=!0)}return s.ColorManagement.enabled=!y,d||(P.outputColorSpace=h?s.LinearSRGBColorSpace:s.SRGBColorSpace,P.toneMapping=g?s.NoToneMapping:s.ACESFilmicToneMapping),M.legacy!==y&&M.set(()=>({legacy:y})),M.linear!==h&&M.set(()=>({linear:h})),M.flat!==g&&M.set(()=>({flat:g})),!c||L.fun(c)||K(c)||L.equ(c,P,ex)||W(P,c),t=v,d=!0,a(),this},render(n){return d||p||this.configure(),p.then(()=>{ew.updateContainer((0,x.jsx)(eS,{store:l,children:n,onCreated:t,rootElement:e}),u,null,()=>void 0)}),l},unmount(){ek(e)}}}function eS({store:e,children:t,onCreated:n,rootElement:r}){return M(()=>{let t=e.getState();t.set(e=>({internal:{...e.internal,active:!0}})),n&&n(t),e.getState().events.connected||null==t.events.connect||t.events.connect(r)},[]),(0,x.jsx)(Z.Provider,{value:e,children:t})}function ek(e,t){let n=ej.get(e),r=null==n?void 0:n.fiber;if(r){let o=null==n?void 0:n.store.getState();o&&(o.internal.active=!1),ew.updateContainer(null,r,null,()=>{o&&setTimeout(()=>{try{null==o.events.disconnect||o.events.disconnect(),null==(n=o.gl)||null==(r=n.renderLists)||null==r.dispose||r.dispose(),null==(i=o.gl)||null==i.forceContextLoss||i.forceContextLoss(),null!=(a=o.gl)&&a.xr&&o.xr.disconnect();var n,r,i,a,l=o.scene;for(let e in"Scene"!==l.type&&(null==l.dispose||l.dispose()),l){let t=l[e];(null==t?void 0:t.type)!=="Scene"&&(null==t||null==t.dispose||t.dispose())}ej.delete(e),t&&t(e)}catch(e){}},500)})}}let eC=new Set,eM=new Set,eP=new Set;function eO(e,t){if(e.size)for(let{callback:n}of e.values())n(t)}function e_(e,t){switch(e){case"before":return eO(eC,t);case"after":return eO(eM,t);case"tail":return eO(eP,t)}}function eA(e,t,n){let i=t.clock.getDelta();"never"===t.frameloop&&"number"==typeof e&&(i=e-t.clock.elapsedTime,t.clock.oldTime=t.clock.elapsedTime,t.clock.elapsedTime=e),r=t.internal.subscribers;for(let e=0;e<r.length;e++)(o=r[e]).ref.current(o.store.getState(),i,n);return!t.internal.priority&&t.gl.render&&t.gl.render(t.scene,t.camera),t.internal.frames=Math.max(0,t.internal.frames-1),"always"===t.frameloop?1:t.internal.frames}let eT=!1,ez=!1;function eL(e){for(let n of(a=requestAnimationFrame(eL),eT=!0,i=0,e_("before",e),ez=!0,ej.values())){var t;(l=n.store.getState()).internal.active&&("always"===l.frameloop||l.internal.frames>0)&&!(null!=(t=l.gl.xr)&&t.isPresenting)&&(i+=eA(e,l))}if(ez=!1,e_("after",e),0===i)return e_("tail",e),eT=!1,cancelAnimationFrame(a)}function eI(e,t=1){var n;if(!e)return ej.forEach(e=>eI(e.store.getState(),t));(null==(n=e.gl.xr)||!n.isPresenting)&&e.internal.active&&"never"!==e.frameloop&&(t>1?e.internal.frames=Math.min(60,e.internal.frames+t):ez?e.internal.frames=2:e.internal.frames=1,eT||(eT=!0,requestAnimationFrame(eL)))}function eD(e,t=!0,n,r){if(t&&e_("before",e),n)eA(e,n,r);else for(let t of ej.values())eA(e,t.store.getState());t&&e_("after",e)}let eR={onClick:["click",!1],onContextMenu:["contextmenu",!1],onDoubleClick:["dblclick",!1],onWheel:["wheel",!0],onPointerDown:["pointerdown",!0],onPointerUp:["pointerup",!0],onPointerLeave:["pointerleave",!0],onPointerMove:["pointermove",!0],onPointerCancel:["pointercancel",!0],onLostPointerCapture:["lostpointercapture",!0]};function eH(e){let{handlePointer:t}=function(e){function t(e){return e.filter(e=>["Move","Over","Enter","Out","Leave"].some(t=>{var n;return null==(n=e.__r3f)?void 0:n.handlers["onPointer"+t]}))}function n(t){let{internal:n}=e.getState();for(let e of n.hovered.values())if(!t.length||!t.find(t=>t.object===e.object&&t.index===e.index&&t.instanceId===e.instanceId)){let r=e.eventObject.__r3f;if(n.hovered.delete(Y(e)),null!=r&&r.eventCount){let n=r.handlers,o={...e,intersections:t};null==n.onPointerOut||n.onPointerOut(o),null==n.onPointerLeave||n.onPointerLeave(o)}}}function r(e,t){for(let n=0;n<t.length;n++){let r=t[n].__r3f;null==r||null==r.handlers.onPointerMissed||r.handlers.onPointerMissed(e)}}return{handlePointer:function(o){switch(o){case"onPointerLeave":case"onPointerCancel":return()=>n([]);case"onLostPointerCapture":return t=>{let{internal:r}=e.getState();"pointerId"in t&&r.capturedMap.has(t.pointerId)&&requestAnimationFrame(()=>{r.capturedMap.has(t.pointerId)&&(r.capturedMap.delete(t.pointerId),n([]))})}}return function(i){let{onPointerMissed:a,internal:l}=e.getState();l.lastEvent.current=i;let c="onPointerMove"===o,u="onClick"===o||"onContextMenu"===o||"onDoubleClick"===o,f=function(t,n){let r=e.getState(),o=new Set,i=[],a=n?n(r.internal.interaction):r.internal.interaction;for(let e=0;e<a.length;e++){let t=z(a[e]);t&&(t.raycaster.camera=void 0)}r.previousRoot||null==r.events.compute||r.events.compute(t,r);let l=a.flatMap(function(e){let n=z(e);if(!n||!n.events.enabled||null===n.raycaster.camera)return[];if(void 0===n.raycaster.camera){var r;null==n.events.compute||n.events.compute(t,n,null==(r=n.previousRoot)?void 0:r.getState()),void 0===n.raycaster.camera&&(n.raycaster.camera=null)}return n.raycaster.camera?n.raycaster.intersectObject(e,!0):[]}).sort((e,t)=>{let n=z(e.object),r=z(t.object);return n&&r&&r.events.priority-n.events.priority||e.distance-t.distance}).filter(e=>{let t=Y(e);return!o.has(t)&&(o.add(t),!0)});for(let e of(r.events.filter&&(l=r.events.filter(l,r)),l)){let t=e.object;for(;t;){var s;null!=(s=t.__r3f)&&s.eventCount&&i.push({...e,eventObject:t}),t=t.parent}}if("pointerId"in t&&r.internal.capturedMap.has(t.pointerId))for(let e of r.internal.capturedMap.get(t.pointerId).values())o.has(Y(e.intersection))||i.push(e.intersection);return i}(i,c?t:void 0),d=u?function(t){let{internal:n}=e.getState(),r=t.offsetX-n.initialClick[0],o=t.offsetY-n.initialClick[1];return Math.round(Math.sqrt(r*r+o*o))}(i):0;"onPointerDown"===o&&(l.initialClick=[i.offsetX,i.offsetY],l.initialHits=f.map(e=>e.eventObject)),u&&!f.length&&d<=2&&(r(i,l.interaction),a&&a(i)),c&&n(f),!function(e,t,r,o){if(e.length){let i={stopped:!1};for(let a of e){let l=z(a.object);if(l||a.object.traverseAncestors(e=>{let t=z(e);if(t)return l=t,!1}),l){let{raycaster:c,pointer:u,camera:f,internal:d}=l,p=new s.Vector3(u.x,u.y,0).unproject(f),m=e=>{var t,n;return null!=(t=null==(n=d.capturedMap.get(e))?void 0:n.has(a.eventObject))&&t},v=e=>{let n={intersection:a,target:t.target};d.capturedMap.has(e)?d.capturedMap.get(e).set(a.eventObject,n):d.capturedMap.set(e,new Map([[a.eventObject,n]])),t.target.setPointerCapture(e)},b=e=>{let t=d.capturedMap.get(e);t&&X(d.capturedMap,a.eventObject,t,e)},h={};for(let e in t){let n=t[e];"function"!=typeof n&&(h[e]=n)}let g={...a,...h,pointer:u,intersections:e,stopped:i.stopped,delta:r,unprojectedPoint:p,ray:c.ray,camera:f,stopPropagation(){let r="pointerId"in t&&d.capturedMap.get(t.pointerId);(!r||r.has(a.eventObject))&&(g.stopped=i.stopped=!0,d.hovered.size&&Array.from(d.hovered.values()).find(e=>e.eventObject===a.eventObject)&&n([...e.slice(0,e.indexOf(a)),a]))},target:{hasPointerCapture:m,setPointerCapture:v,releasePointerCapture:b},currentTarget:{hasPointerCapture:m,setPointerCapture:v,releasePointerCapture:b},nativeEvent:t};if(o(g),!0===i.stopped)break}}}}(f,i,d,function(e){let t=e.eventObject,n=t.__r3f;if(!(null!=n&&n.eventCount))return;let a=n.handlers;if(c){if(a.onPointerOver||a.onPointerEnter||a.onPointerOut||a.onPointerLeave){let t=Y(e),n=l.hovered.get(t);n?n.stopped&&e.stopPropagation():(l.hovered.set(t,e),null==a.onPointerOver||a.onPointerOver(e),null==a.onPointerEnter||a.onPointerEnter(e))}null==a.onPointerMove||a.onPointerMove(e)}else{let n=a[o];n?(!u||l.initialHits.includes(t))&&(r(i,l.interaction.filter(e=>!l.initialHits.includes(e))),n(e)):u&&l.initialHits.includes(t)&&r(i,l.interaction.filter(e=>!l.initialHits.includes(e)))}})}}}}(e);return{priority:1,enabled:!0,compute(e,t,n){t.pointer.set(e.offsetX/t.size.width*2-1,-(2*(e.offsetY/t.size.height))+1),t.raycaster.setFromCamera(t.pointer,t.camera)},connected:void 0,handlers:Object.keys(eR).reduce((e,n)=>({...e,[n]:t(n)}),{}),update:()=>{var t;let{events:n,internal:r}=e.getState();null!=(t=r.lastEvent)&&t.current&&n.handlers&&n.handlers.onPointerMove(r.lastEvent.current)},connect:t=>{let{set:n,events:r}=e.getState();if(null==r.disconnect||r.disconnect(),n(e=>({events:{...e.events,connected:t}})),r.handlers)for(let e in r.handlers){let n=r.handlers[e],[o,i]=eR[e];t.addEventListener(o,n,{passive:i})}},disconnect:()=>{let{set:t,events:n}=e.getState();if(n.connected){if(n.handlers)for(let e in n.handlers){let t=n.handlers[e],[r]=eR[e];n.connected.removeEventListener(r,t)}t(e=>({events:{...e.events,connected:void 0}}))}}}}},72407:(e,t,n)=>{"use strict";e.exports=n(16892)},75830:(e,t,n)=>{"use strict";let r,o;n.d(t,{ON:()=>m});var i=n(79630),a=n(97431),l=n(12115),s=n(71949);a.Group;let c=l.createContext(null),u=new a.Matrix4,f=new a.Vector3,d=l.forwardRef(({children:e,range:t,limit:n=1e3,...d},p)=>{let m=l.useRef(null);l.useImperativeHandle(p,()=>m.current,[]);let[v,b]=l.useState([]),[[h,g,y]]=l.useState(()=>[new Float32Array(3*n),Float32Array.from({length:3*n},()=>1),Float32Array.from({length:n},()=>1)]);l.useEffect(()=>{m.current.geometry.attributes.position.needsUpdate=!0}),(0,s.D)(()=>{for(m.current.updateMatrix(),m.current.updateMatrixWorld(),u.copy(m.current.matrixWorld).invert(),m.current.geometry.drawRange.count=Math.min(n,void 0!==t?t:n,v.length),r=0;r<v.length;r++)(o=v[r].current).getWorldPosition(f).applyMatrix4(u),f.toArray(h,3*r),m.current.geometry.attributes.position.needsUpdate=!0,o.matrixWorldNeedsUpdate=!0,o.color.toArray(g,3*r),m.current.geometry.attributes.color.needsUpdate=!0,y.set([o.size],r),m.current.geometry.attributes.size.needsUpdate=!0});let w=l.useMemo(()=>({getParent:()=>m,subscribe:e=>(b(t=>[...t,e]),()=>b(t=>t.filter(t=>t.current!==e.current)))}),[]);return l.createElement("points",(0,i.A)({userData:{instances:v},matrixAutoUpdate:!1,ref:m,raycast:()=>null},d),l.createElement("bufferGeometry",null,l.createElement("bufferAttribute",{attach:"attributes-position",args:[h,3],usage:a.DynamicDrawUsage}),l.createElement("bufferAttribute",{attach:"attributes-color",args:[g,3],usage:a.DynamicDrawUsage}),l.createElement("bufferAttribute",{attach:"attributes-size",args:[y,1],usage:a.DynamicDrawUsage})),l.createElement(c.Provider,{value:w},e))}),p=l.forwardRef(({children:e,positions:t,colors:n,sizes:r,stride:o=3,...c},u)=>{let f=l.useRef(null);return l.useImperativeHandle(u,()=>f.current,[]),(0,s.D)(()=>{let e=f.current.geometry.attributes;e.position.needsUpdate=!0,n&&(e.color.needsUpdate=!0),r&&(e.size.needsUpdate=!0)}),l.createElement("points",(0,i.A)({ref:f},c),l.createElement("bufferGeometry",null,l.createElement("bufferAttribute",{attach:"attributes-position",args:[t,o],usage:a.DynamicDrawUsage}),n&&l.createElement("bufferAttribute",{attach:"attributes-color",args:[n,o],count:n.length/o,usage:a.DynamicDrawUsage}),r&&l.createElement("bufferAttribute",{attach:"attributes-size",args:[r,1],count:r.length/o,usage:a.DynamicDrawUsage})),e)}),m=l.forwardRef((e,t)=>e.positions instanceof Float32Array?l.createElement(p,(0,i.A)({},e,{ref:t})):l.createElement(d,(0,i.A)({},e,{ref:t})))},79630:(e,t,n)=>{"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}n.d(t,{A:()=>r})},89107:(e,t,n)=>{"use strict";n.d(t,{q:()=>c});var r=n(79630),o=n(97431),i=n(12115);let a=parseInt(o.REVISION.replace(/\D+/g,"")),l=a>=154?"opaque_fragment":"output_fragment";class s extends o.PointsMaterial{constructor(e){super(e),this.onBeforeCompile=(e,t)=>{let{isWebGL2:n}=t.capabilities;e.fragmentShader=e.fragmentShader.replace(`#include <${l}>`,`
        ${!n?`#extension GL_OES_standard_derivatives : enable
#include <${l}>`:`#include <${l}>`}
      vec2 cxy = 2.0 * gl_PointCoord - 1.0;
      float r = dot(cxy, cxy);
      float delta = fwidth(r);     
      float mask = 1.0 - smoothstep(1.0 - delta, 1.0 + delta, r);
      gl_FragColor = vec4(gl_FragColor.rgb, mask * gl_FragColor.a );
      #include <tonemapping_fragment>
      #include <${a>=154?"colorspace_fragment":"encodings_fragment"}>
      `)}}}let c=i.forwardRef((e,t)=>{let[n]=i.useState(()=>new s(null));return i.createElement("primitive",(0,r.A)({},e,{object:n,ref:t,attach:"material"}))})}}]);