"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5236],{1335:(t,e,i)=>{i.d(e,{u:()=>s});var n=i(9064);let s={test:(0,i(55920).$)("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:n.B.transform}},4272:(t,e,i)=>{i.d(e,{y:()=>a});var n=i(1335),s=i(18476),r=i(9064);let a={test:t=>r.B.test(t)||n.u.test(t)||s.V.test(t),parse:t=>r.B.test(t)?r.B.parse(t):s.V.test(t)?s.V.parse(t):n.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?r.B.transform(t):s.V.transform(t),getAnimatableNone:t=>{let e=a.parse(t);return e.alpha=0,a.transform(e)}}},6775:(t,e,i)=>{i.d(e,{G:()=>u});var n=i(23387),s=i(19827),r=i(53191),a=i(54542),o=i(45818),l=i(53678),h=i(26087);function u(t,e,{clamp:i=!0,ease:d,mixer:c}={}){let p=t.length;if((0,a.V)(p===e.length,"Both input and output ranges must be the same length","range-length"),1===p)return()=>e[0];if(2===p&&e[0]===e[1])return()=>e[1];let m=t[0]===t[1];t[0]>t[p-1]&&(t=[...t].reverse(),e=[...e].reverse());let f=function(t,e,i){let a=[],o=i||n.W.mix||h.j,l=t.length-1;for(let i=0;i<l;i++){let n=o(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||s.l:e;n=(0,r.F)(t,n)}a.push(n)}return a}(e,d,c),v=f.length,y=i=>{if(m&&i<t[0])return e[0];let n=0;if(v>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=(0,o.q)(t[n],t[n+1],i);return f[n](s)};return i?e=>y((0,l.q)(t[0],t[p-1],e)):y}},6983:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},7712:(t,e,i)=>{i.d(e,{po:()=>r,tn:()=>o,yT:()=>a});var n=i(91765),s=i(54180);let r=t=>1-Math.sin(Math.acos(t)),a=(0,s.G)(r),o=(0,n.V)(r)},9064:(t,e,i)=>{i.d(e,{B:()=>l});var n=i(53678),s=i(57887),r=i(11557),a=i(55920);let o={...s.ai,transform:t=>Math.round((0,n.q)(0,255,t))},l={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+o.transform(t)+", "+o.transform(e)+", "+o.transform(i)+", "+(0,r.a)(s.X4.transform(n))+")"}},11557:(t,e,i)=>{i.d(e,{a:()=>n});let n=t=>Math.round(1e5*t)/1e5},18476:(t,e,i)=>{i.d(e,{V:()=>o});var n=i(57887),s=i(34158),r=i(11557),a=i(55920);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:a=1})=>"hsla("+Math.round(t)+", "+s.KN.transform((0,r.a)(e))+", "+s.KN.transform((0,r.a)(i))+", "+(0,r.a)(n.X4.transform(a))+")"}},19827:(t,e,i)=>{i.d(e,{l:()=>n});let n=t=>t},23387:(t,e,i)=>{i.d(e,{W:()=>n});let n={}},24744:(t,e,i)=>{i.d(e,{Q:()=>n});let n={value:null,addProjectionMetrics:null}},26087:(t,e,i)=>{i.d(e,{j:()=>S});var n=i(53191),s=i(54542),r=i(78606),a=i(4272),o=i(60010),l=i(1335),h=i(18476);function u(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var d=i(9064);function c(t,e){return i=>i>0?e:t}var p=i(33210);let m=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},f=[l.u,d.B,h.V];function v(t){let e=f.find(e=>e.test(t));if((0,s.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===h.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=u(o,n,t+1/3),r=u(o,n,t),a=u(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let y=(t,e)=>{let i=v(t),n=v(e);if(!i||!n)return c(t,e);let s={...i};return t=>(s.red=m(i.red,n.red,t),s.green=m(i.green,n.green,t),s.blue=m(i.blue,n.blue,t),s.alpha=(0,p.k)(i.alpha,n.alpha,t),d.B.transform(s))},g=new Set(["none","hidden"]);function x(t,e){return i=>(0,p.k)(t,e,i)}function w(t){return"number"==typeof t?x:"string"==typeof t?(0,r.p)(t)?c:a.y.test(t)?y:P:Array.isArray(t)?T:"object"==typeof t?a.y.test(t)?y:b:c}function T(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>w(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function b(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=w(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let P=(t,e)=>{let i=o.f.createTransformer(e),r=(0,o.V)(t),a=(0,o.V)(e);return r.indexes.var.length===a.indexes.var.length&&r.indexes.color.length===a.indexes.color.length&&r.indexes.number.length>=a.indexes.number.length?g.has(t)&&!a.values.length||g.has(e)&&!r.values.length?function(t,e){return g.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,n.F)(T(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(r,a),a.values),i):((0,s.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),c(t,e))};function S(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,p.k)(t,e,i):w(t)(t,e)}},27351:(t,e,i)=>{i.d(e,{s:()=>s});var n=i(6983);function s(t){return(0,n.G)(t)&&"offsetHeight"in t}},30532:(t,e,i)=>{i.d(e,{s:()=>g});var n=i(53191),s=i(53678),r=i(47215),a=i(74261),o=i(63704),l=i(26087),h=i(69515);let u=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>h.Gt.update(e,t),stop:()=>(0,h.WG)(e),now:()=>h.uv.isProcessing?h.uv.timestamp:a.k.now()}};var d=i(56330),c=i(63669),p=i(52458),m=i(76778),f=i(70144),v=i(63894);let y=t=>t/100;class g extends v.q{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==a.k.now()&&this.tick(a.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},o.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;(0,f.E)(t);let{type:e=c.i,repeat:i=0,repeatDelay:s=0,repeatType:r,velocity:a=0}=t,{keyframes:o}=t,h=e||c.i;h!==c.i&&"number"!=typeof o[0]&&(this.mixKeyframes=(0,n.F)(y,(0,l.j)(o[0],o[1])),o=[0,100]);let u=h({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=h({...t,keyframes:[...o].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=(0,p.t)(u));let{calculatedDuration:d}=u;this.calculatedDuration=d,this.resolvedDuration=d+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=u}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:h=0,keyframes:u,repeat:c,repeatType:p,repeatDelay:f,type:v,onUpdate:y,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let x=this.currentTime-h*(this.playbackSpeed>=0?1:-1),w=this.playbackSpeed>=0?x<0:x>n;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let T=this.currentTime,b=i;if(c){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,f&&(i-=f/o)):"mirror"===p&&(b=a)),T=(0,s.q)(0,1,i)*o}let P=w?{done:!1,value:u[0]}:b.next(T);r&&(P.value=r(P.value));let{done:S}=P;w||null===l||(S=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return A&&v!==d.B&&(P.value=(0,m.X)(u,this.options,g,this.speed)),y&&y(P.value),A&&this.finish(),P}then(t,e){return this.finished.then(t,e)}get duration(){return(0,r.X)(this.calculatedDuration)}get time(){return(0,r.X)(this.currentTime)}set time(t){t=(0,r.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(a.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,r.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=u,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(a.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,o.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}},30614:(t,e,i)=>{i.d(e,{S:()=>n});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},32082:(t,e,i)=>{i.d(e,{xQ:()=>r});var n=i(12115),s=i(80845);function r(t=!0){let e=(0,n.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return o(l)},[t]);let h=(0,n.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,h]:[!0]}},33210:(t,e,i)=>{i.d(e,{k:()=>n});let n=(t,e,i)=>t+(e-t)*i},33972:(t,e,i)=>{i.d(e,{Sz:()=>a,ZZ:()=>l,dg:()=>o});var n=i(62483),s=i(91765),r=i(54180);let a=(0,n.A)(.33,1.53,.69,.99),o=(0,r.G)(a),l=(0,s.V)(o)},34158:(t,e,i)=>{i.d(e,{KN:()=>r,gQ:()=>h,px:()=>a,uj:()=>s,vh:()=>o,vw:()=>l});let n=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),s=n("deg"),r=n("%"),a=n("px"),o=n("vh"),l=n("vw"),h={...r,parse:t=>r.parse(t)/100,transform:t=>r.transform(100*t)}},45818:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n}},46009:(t,e,i)=>{i.d(e,{b:()=>s});var n=i(33972);let s=t=>(t*=2)<1?.5*(0,n.dg)(t):.5*(2-Math.pow(2,-10*(t-1)))},47215:(t,e,i)=>{i.d(e,{X:()=>s,f:()=>n});let n=t=>1e3*t,s=t=>t/1e3},47705:(t,e,i)=>{i.d(e,{K:()=>n});let n=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`}},51508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},52458:(t,e,i)=>{i.d(e,{Y:()=>n,t:()=>s});let n=2e4;function s(t){let e=0,i=t.next(e);for(;!i.done&&e<n;)e+=50,i=t.next(e);return e>=n?1/0:e}},53191:(t,e,i)=>{i.d(e,{F:()=>s});let n=(t,e)=>i=>e(t(i)),s=(...t)=>t.reduce(n)},53678:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>i>e?e:i<t?t:i},54180:(t,e,i)=>{i.d(e,{G:()=>n});let n=t=>e=>1-t(1-e)},54542:(t,e,i)=>{i.d(e,{$:()=>n,V:()=>s});let n=()=>{},s=()=>{}},55920:(t,e,i)=>{i.d(e,{$:()=>r,q:()=>a});var n=i(30614);let s=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,r=(t,e)=>i=>!!("string"==typeof i&&s.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),a=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[r,a,o,l]=s.match(n.S);return{[t]:parseFloat(r),[e]:parseFloat(a),[i]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},56330:(t,e,i)=>{i.d(e,{B:()=>r});var n=i(82886),s=i(73945);function r({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:a=10,bounceStiffness:o=500,modifyTarget:l,min:h,max:u,restDelta:d=.5,restSpeed:c}){let p,m,f=t[0],v={done:!1,value:f},y=i*e,g=f+y,x=void 0===l?g:l(g);x!==g&&(y=x-f);let w=t=>-y*Math.exp(-t/r),T=t=>x+w(t),b=t=>{let e=w(t),i=T(t);v.done=Math.abs(e)<=d,v.value=v.done?x:i},P=t=>{let e;if(e=v.value,void 0!==h&&e<h||void 0!==u&&e>u){var i;p=t,m=(0,n.o)({keyframes:[v.value,(i=v.value,void 0===h?u:void 0===u||Math.abs(h-i)<Math.abs(u-i)?h:u)],velocity:(0,s.Y)(T,t,v.value),damping:a,stiffness:o,restDelta:d,restSpeed:c})}};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(m||void 0!==p||(e=!0,b(t),P(t)),void 0!==p&&t>=p)?m.next(t-p):(e||b(t),v)}}}},56668:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function s(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>s,Kq:()=>n})},57887:(t,e,i)=>{i.d(e,{X4:()=>r,ai:()=>s,hs:()=>a});var n=i(53678);let s={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},r={...s,transform:t=>(0,n.q)(0,1,t)},a={...s,default:1}},58437:(t,e,i)=>{i.d(e,{I:()=>a});var n=i(23387);let s=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var r=i(24744);function a(t,e){let i=!1,a=!0,o={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,h=s.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,a=!1,o=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},h=0;function u(e){o.has(e)&&(d.schedule(e),t()),h++,e(l)}let d={schedule:(t,e=!1,r=!1)=>{let a=r&&s?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(l=t,s){a=!0;return}s=!0,[i,n]=[n,i],i.forEach(u),e&&r.Q.value&&r.Q.value.frameloop[e].push(h),h=0,i.clear(),s=!1,a&&(a=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:u,read:d,resolveKeyframes:c,preUpdate:p,update:m,preRender:f,render:v,postRender:y}=h,g=()=>{let s=n.W.useManualTiming?o.timestamp:performance.now();i=!1,n.W.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(s-o.timestamp,40),1)),o.timestamp=s,o.isProcessing=!0,u.process(o),d.process(o),c.process(o),p.process(o),m.process(o),f.process(o),v.process(o),y.process(o),o.isProcessing=!1,i&&e&&(a=!1,t(g))};return{schedule:s.reduce((e,n)=>{let s=h[n];return e[n]=(e,n=!1,r=!1)=>(!i&&(i=!0,a=!0,o.isProcessing||t(g)),s.schedule(e,n,r)),e},{}),cancel:t=>{for(let e=0;e<s.length;e++)h[s[e]].cancel(t)},state:o,steps:h}}},60010:(t,e,i)=>{i.d(e,{V:()=>u,f:()=>m});var n=i(4272);let s=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var r=i(30614),a=i(11557);let o="number",l="color",h=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function u(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},r=[],a=0,u=e.replace(h,t=>(n.y.test(t)?(s.color.push(a),r.push(l),i.push(n.y.parse(t))):t.startsWith("var(")?(s.var.push(a),r.push("var"),i.push(t)):(s.number.push(a),r.push(o),i.push(parseFloat(t))),++a,"${}")).split("${}");return{values:i,split:u,indexes:s,types:r}}function d(t){return u(t).values}function c(t){let{split:e,types:i}=u(t),s=e.length;return t=>{let r="";for(let h=0;h<s;h++)if(r+=e[h],void 0!==t[h]){let e=i[h];e===o?r+=(0,a.a)(t[h]):e===l?r+=n.y.transform(t[h]):r+=t[h]}return r}}let p=t=>"number"==typeof t?0:n.y.test(t)?n.y.getAnimatableNone(t):t,m={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(r.S)?.length||0)+(t.match(s)?.length||0)>0},parse:d,createTransformer:c,getAnimatableNone:function(t){let e=d(t);return c(t)(e.map(p))}}},60098:(t,e,i)=>{i.d(e,{OQ:()=>h,bt:()=>o});var n=i(75626),s=i(62923),r=i(74261),a=i(69515);let o={current:void 0};class l{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=r.k.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=r.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return o.current&&o.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=r.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,s.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new l(t,e)}},62039:(t,e,i)=>{i.d(e,{a6:()=>s,am:()=>a,vT:()=>r});var n=i(62483);let s=(0,n.A)(.42,0,1,1),r=(0,n.A)(0,0,.58,1),a=(0,n.A)(.42,0,.58,1)},62483:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(19827);let s=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function r(t,e,i,r){return t===e&&i===r?n.l:n=>0===n||1===n?n:s(function(t,e,i,n,r){let a,o,l=0;do(a=s(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(a)>1e-7&&++l<12);return o}(n,0,1,t,i),e,r)}},62923:(t,e,i)=>{i.d(e,{f:()=>n});function n(t,e){return e?1e3/e*t:0}},63669:(t,e,i)=>{i.d(e,{i:()=>v});var n=i(62039),s=i(54542),r=i(19827),a=i(46009),o=i(33972),l=i(7712),h=i(62483),u=i(68589);let d={linear:r.l,easeIn:n.a6,easeInOut:n.am,easeOut:n.vT,circIn:l.po,circInOut:l.tn,circOut:l.yT,backIn:o.dg,backInOut:o.ZZ,backOut:o.Sz,anticipate:a.b},c=t=>{if((0,u.D)(t)){(0,s.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,n,r]=t;return(0,h.A)(e,i,n,r)}return"string"==typeof t?((0,s.V)(void 0!==d[t],`Invalid easing type '${t}'`,"invalid-easing-type"),d[t]):t};var p=i(6775),m=i(45818),f=i(33210);function v({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var r;let a=Array.isArray(s)&&"number"!=typeof s[0]?s.map(c):c(s),o={done:!1,value:e[0]},l=(r=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let s=(0,m.q)(0,e,n);t.push((0,f.k)(i,1,s))}}(e,t.length-1),e}(e),r.map(e=>e*t)),h=(0,p.G)(l,e,{ease:Array.isArray(a)?a:e.map(()=>a||n.am).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=h(e),o.done=e>=t,o)}}},63704:(t,e,i)=>{i.d(e,{q:()=>n});let n={layout:0,mainThread:0,waapi:0}},63894:(t,e,i)=>{i.d(e,{q:()=>n});class n{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}},64803:(t,e,i)=>{i.d(e,{S:()=>n});let n=t=>!!(t&&t.getVelocity)},68289:(t,e,i)=>{i.d(e,{P:()=>n7});var n=i(12115);let s=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],r=new Set(s),a=t=>180*t/Math.PI,o=t=>h(a(Math.atan2(t[1],t[0]))),l={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:o,rotateZ:o,skewX:t=>a(Math.atan(t[1])),skewY:t=>a(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},h=t=>((t%=360)<0&&(t+=360),t),u=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),d=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),c={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:u,scaleY:d,scale:t=>(u(t)+d(t))/2,rotateX:t=>h(a(Math.atan2(t[6],t[5]))),rotateY:t=>h(a(Math.atan2(-t[2],t[0]))),rotateZ:o,rotate:o,skewX:t=>a(Math.atan(t[4])),skewY:t=>a(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function p(t){return+!!t.includes("scale")}function m(t,e){let i,n;if(!t||"none"===t)return p(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=c,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=l,n=e}if(!n)return p(e);let r=i[e],a=n[1].split(",").map(f);return"function"==typeof r?r(a):a[r]}function f(t){return parseFloat(t.trim())}var v=i(78606);function y({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}var g=i(33210);function x(t){return void 0===t||1===t}function w({scale:t,scaleX:e,scaleY:i}){return!x(t)||!x(e)||!x(i)}function T(t){return w(t)||b(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function b(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function P(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function S(t,e=0,i=1,n,s){t.min=P(t.min,e,i,n,s),t.max=P(t.max,e,i,n,s)}function A(t,{x:e,y:i}){S(t.x,e.translate,e.scale,e.originPoint),S(t.y,i.translate,i.scale,i.originPoint)}function V(t,e){t.min=t.min+e,t.max=t.max+e}function M(t,e,i,n,s=.5){let r=(0,g.k)(t.min,t.max,s);S(t,e,i,r,n)}function E(t,e){M(t.x,e.x,e.scaleX,e.scale,e.originX),M(t.y,e.y,e.scaleY,e.scale,e.originY)}function k(t,e){return y(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let D=new Set(["width","height","top","left","right","bottom",...s]);var C=i(57887),j=i(34158);let R=t=>e=>e.test(t),L=[C.ai,j.px,j.KN,j.uj,j.vw,j.vh,{test:t=>"auto"===t,parse:t=>t}],O=t=>L.find(R(t));var F=i(54542);let B=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),I=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,N=t=>t===C.ai||t===j.px,U=new Set(["x","y","z"]),W=s.filter(t=>!U.has(t)),$={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>m(e,"x"),y:(t,{transform:e})=>m(e,"y")};$.translateX=$.x,$.translateY=$.y;var G=i(69515);let q=new Set,X=!1,Y=!1,K=!1;function z(){if(Y){let t=Array.from(q).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return W.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Y=!1,X=!1,q.forEach(t=>t.complete(K)),q.clear()}function H(){q.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Y=!0)})}class Q{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(q.add(this),X||(X=!0,G.Gt.read(H),G.Gt.resolveKeyframes(z))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),q.delete(this)}cancel(){"scheduled"===this.state&&(q.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let Z=t=>/^0[^.\s]+$/u.test(t);var _=i(60010),J=i(30614);let tt=new Set(["brightness","contrast","saturate","opacity"]);function te(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(J.S)||[];if(!n)return t;let s=i.replace(n,""),r=+!!tt.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let ti=/\b([a-z-]*)\(.*?\)/gu,tn={..._.f,getAnimatableNone:t=>{let e=t.match(ti);return e?e.map(te).join(" "):t}};var ts=i(4272);let tr={...C.ai,transform:Math.round},ta={rotate:j.uj,rotateX:j.uj,rotateY:j.uj,rotateZ:j.uj,scale:C.hs,scaleX:C.hs,scaleY:C.hs,scaleZ:C.hs,skew:j.uj,skewX:j.uj,skewY:j.uj,distance:j.px,translateX:j.px,translateY:j.px,translateZ:j.px,x:j.px,y:j.px,z:j.px,perspective:j.px,transformPerspective:j.px,opacity:C.X4,originX:j.gQ,originY:j.gQ,originZ:j.px},to={borderWidth:j.px,borderTopWidth:j.px,borderRightWidth:j.px,borderBottomWidth:j.px,borderLeftWidth:j.px,borderRadius:j.px,radius:j.px,borderTopLeftRadius:j.px,borderTopRightRadius:j.px,borderBottomRightRadius:j.px,borderBottomLeftRadius:j.px,width:j.px,maxWidth:j.px,height:j.px,maxHeight:j.px,top:j.px,right:j.px,bottom:j.px,left:j.px,padding:j.px,paddingTop:j.px,paddingRight:j.px,paddingBottom:j.px,paddingLeft:j.px,margin:j.px,marginTop:j.px,marginRight:j.px,marginBottom:j.px,marginLeft:j.px,backgroundPositionX:j.px,backgroundPositionY:j.px,...ta,zIndex:tr,fillOpacity:C.X4,strokeOpacity:C.X4,numOctaves:tr},tl={...to,color:ts.y,backgroundColor:ts.y,outlineColor:ts.y,fill:ts.y,stroke:ts.y,borderColor:ts.y,borderTopColor:ts.y,borderRightColor:ts.y,borderBottomColor:ts.y,borderLeftColor:ts.y,filter:tn,WebkitFilter:tn},th=t=>tl[t];function tu(t,e){let i=th(t);return i!==tn&&(i=_.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let td=new Set(["auto","none","0"]);class tc extends Q{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),(0,v.p)(n))){let s=function t(e,i,n=1){(0,F.V)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[s,r]=function(t){let e=I.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let a=window.getComputedStyle(i).getPropertyValue(s);if(a){let t=a.trim();return B(t)?parseFloat(t):t}return(0,v.p)(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!D.has(i)||2!==t.length)return;let[n,s]=t,r=O(n),a=O(s);if(r!==a)if(N(r)&&N(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else $[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||Z(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!td.has(e)&&(0,_.V)(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=tu(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=$[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=$[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}var tp=i(64803),tm=i(74261),tf=i(60098);let tv=[...L,ts.y,_.f],{schedule:ty}=(0,i(58437).I)(queueMicrotask,!1);var tg=i(75626);let tx={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},tw={};for(let t in tx)tw[t]={isEnabled:e=>tx[t].some(t=>!!e[t])};let tT=()=>({translate:0,scale:1,origin:0,originPoint:0}),tb=()=>({x:tT(),y:tT()}),tP=()=>({min:0,max:0}),tS=()=>({x:tP(),y:tP()});var tA=i(68972);let tV={current:null},tM={current:!1},tE=new WeakMap;function tk(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function tD(t){return"string"==typeof t||Array.isArray(t)}let tC=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tj=["initial",...tC];function tR(t){return tk(t.animate)||tj.some(e=>tD(t[e]))}function tL(t){return!!(tR(t)||t.variants)}function tO(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function tF(t,e,i,n){if("function"==typeof e){let[s,r]=tO(n);e=e(void 0!==i?i:t.custom,s,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,r]=tO(n);e=e(void 0!==i?i:t.custom,s,r)}return e}let tB=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class tI{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Q,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=tm.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,G.Gt.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=tR(e),this.isVariantNode=tL(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==o[t]&&(0,tp.S)(e)&&e.set(o[t],!1)}}mount(t){this.current=t,tE.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),tM.current||function(){if(tM.current=!0,tA.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>tV.current=t.matches;t.addEventListener("change",e),e()}else tV.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||tV.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,G.WG)(this.notifyUpdate),(0,G.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=r.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&G.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),a=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),a(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in tw){let e=tw[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):tS()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<tB.length;e++){let i=tB[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if((0,tp.S)(s))t.addValue(n,s);else if((0,tp.S)(r))t.addValue(n,(0,tf.OQ)(s,{owner:t}));else if(r!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,(0,tf.OQ)(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,tf.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=i){if("string"==typeof i&&(B(i)||Z(i)))i=parseFloat(i);else{let n;n=i,!tv.find(R(n))&&_.f.test(e)&&(i=tu(t,e))}this.setBaseTarget(t,(0,tp.S)(i)?i.get():i)}return(0,tp.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=tF(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||(0,tp.S)(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new tg.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){ty.render(this.render)}}class tN extends tI{constructor(){super(...arguments),this.KeyframeResolver=tc}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,tp.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let tU=(t,e)=>e&&"number"==typeof t?e.transform(t):t,tW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},t$=s.length;function tG(t,e,i){let{style:n,vars:a,transformOrigin:o}=t,l=!1,h=!1;for(let t in e){let i=e[t];if(r.has(t)){l=!0;continue}if((0,v.j)(t)){a[t]=i;continue}{let e=tU(i,to[t]);t.startsWith("origin")?(h=!0,o[t]=e):n[t]=e}}if(!e.transform&&(l||i?n.transform=function(t,e,i){let n="",r=!0;for(let a=0;a<t$;a++){let o=s[a],l=t[o];if(void 0===l)continue;let h=!0;if(!(h="number"==typeof l?l===+!!o.startsWith("scale"):0===parseFloat(l))||i){let t=tU(l,to[o]);if(!h){r=!1;let e=tW[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),h){let{originX:t="50%",originY:e="50%",originZ:i=0}=o;n.transformOrigin=`${t} ${e} ${i}`}}function tq(t,{style:e,vars:i},n,s){let r,a=t.style;for(r in e)a[r]=e[r];for(r in s?.applyProjectionStyles(a,n),i)a.setProperty(r,i[r])}let tX={};function tY(t,{layout:e,layoutId:i}){return r.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!tX[t]||"opacity"===t)}function tK(t,e,i){let{style:n}=t,s={};for(let r in n)((0,tp.S)(n[r])||e.style&&(0,tp.S)(e.style[r])||tY(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(s[r]=n[r]);return s}class tz extends tN{constructor(){super(...arguments),this.type="html",this.renderInstance=tq}readValueFromInstance(t,e){if(r.has(e))return this.projection?.isProjecting?p(e):((t,e)=>{let{transform:i="none"}=getComputedStyle(t);return m(i,e)})(t,e);{let i=window.getComputedStyle(t),n=((0,v.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return k(t,e)}build(t,e,i){tG(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return tK(t,e,i)}}let tH=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tQ={offset:"stroke-dashoffset",array:"stroke-dasharray"},tZ={offset:"strokeDashoffset",array:"strokeDasharray"};function t_(t,{attrX:e,attrY:i,attrScale:n,pathLength:s,pathSpacing:r=1,pathOffset:a=0,...o},l,h,u){if(tG(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==s&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;let r=s?tQ:tZ;t[r.offset]=j.px.transform(-n);let a=j.px.transform(e),o=j.px.transform(i);t[r.array]=`${a} ${o}`}(d,s,r,a,!1)}let tJ=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),t0=t=>"string"==typeof t&&"svg"===t.toLowerCase();function t1(t,e,i){let n=tK(t,e,i);for(let i in t)((0,tp.S)(t[i])||(0,tp.S)(e[i]))&&(n[-1!==s.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}class t5 extends tN{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=tS}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(r.has(e)){let t=th(e);return t&&t.default||0}return e=tJ.has(e)?e:tH(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return t1(t,e,i)}build(t,e,i){t_(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in tq(t,e,void 0,n),e.attrs)t.setAttribute(tJ.has(i)?i:tH(i),e.attrs[i])}mount(t){this.isSVGTag=t0(t.tagName),super.mount(t)}}let t2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function t3(t){if("string"!=typeof t||t.includes("-"));else if(t2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var t6=i(95155),t4=i(90869);let t8=(0,n.createContext)({strict:!1});var t7=i(51508);let t9=(0,n.createContext)({});function et(t){return Array.isArray(t)?t.join(" "):t}let ee=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ei(t,e,i){for(let n in e)(0,tp.S)(e[n])||tY(n,i)||(t[n]=e[n])}let en=()=>({...ee(),attrs:{}}),es=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function er(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||es.has(t)}let ea=t=>!er(t);try{!function(t){"function"==typeof t&&(ea=e=>e.startsWith("on")?!er(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}var eo=i(80845),el=i(82885);function eh(t){return(0,tp.S)(t)?t.get():t}let eu=t=>(e,i)=>{let s=(0,n.useContext)(t9),r=(0,n.useContext)(eo.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,s){return{latestValues:function(t,e,i,n){let s={},r=n(t,{});for(let t in r)s[t]=eh(r[t]);let{initial:a,animate:o}=t,l=tR(t),h=tL(t);e&&h&&!l&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===o&&(o=e.animate));let u=!!i&&!1===i.initial,d=(u=u||!1===a)?o:a;if(d&&"boolean"!=typeof d&&!tk(d)){let e=Array.isArray(d)?d:[d];for(let i=0;i<e.length;i++){let n=tF(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=u?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,n,s,t),renderState:e()}})(t,e,s,r);return i?a():(0,el.M)(a)},ed=eu({scrapeMotionValuesFromProps:tK,createRenderState:ee}),ec=eu({scrapeMotionValuesFromProps:t1,createRenderState:en}),ep=Symbol.for("motionComponentSymbol");function em(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let ef="data-"+tH("framerAppearId"),ev=(0,n.createContext)({});var ey=i(97494);function eg(t){var e,i;let{forwardMotionProps:s=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0;r&&function(t){for(let e in t)tw[e]={...tw[e],...t[e]}}(r);let o=t3(t)?ec:ed;function l(e,i){var r;let l,h={...(0,n.useContext)(t7.Q),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,n.useContext)(t4.L).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:u}=h,d=function(t){let{initial:e,animate:i}=function(t,e){if(tR(t)){let{initial:e,animate:i}=t;return{initial:!1===e||tD(e)?e:void 0,animate:tD(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,n.useContext)(t9));return(0,n.useMemo)(()=>({initial:e,animate:i}),[et(e),et(i)])}(e),c=o(e,u);if(!u&&tA.B){(0,n.useContext)(t8).strict;let e=function(t){let{drag:e,layout:i}=tw;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);l=e.MeasureLayout,d.visualElement=function(t,e,i,s,r){let{visualElement:a}=(0,n.useContext)(t9),o=(0,n.useContext)(t8),l=(0,n.useContext)(eo.t),h=(0,n.useContext)(t7.Q).reducedMotion,u=(0,n.useRef)(null);s=s||o.renderer,!u.current&&s&&(u.current=s(t,{visualState:e,parent:a,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:h}));let d=u.current,c=(0,n.useContext)(ev);d&&!d.projection&&r&&("html"===d.type||"svg"===d.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!a||o&&em(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,crossfade:u,layoutScroll:l,layoutRoot:h})}(u.current,i,r,c);let p=(0,n.useRef)(!1);(0,n.useInsertionEffect)(()=>{d&&p.current&&d.update(i,l)});let m=i[ef],f=(0,n.useRef)(!!m&&!window.MotionHandoffIsComplete?.(m)&&window.MotionHasOptimisedAnimation?.(m));return(0,ey.E)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),d.scheduleRenderMicrotask(),f.current&&d.animationState&&d.animationState.animateChanges())}),(0,n.useEffect)(()=>{d&&(!f.current&&d.animationState&&d.animationState.animateChanges(),f.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(m)}),f.current=!1))}),d}(t,c,h,a,e.ProjectionNode)}return(0,t6.jsxs)(t9.Provider,{value:d,children:[l&&d.visualElement?(0,t6.jsx)(l,{visualElement:d.visualElement,...h}):null,function(t,e,i,{latestValues:s},r,a=!1){let o=(t3(t)?function(t,e,i,s){let r=(0,n.useMemo)(()=>{let i=en();return t_(i,e,t0(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ei(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return ei(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,n.useMemo)(()=>{let i=ee();return tG(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(e,s,r,t),l=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(ea(s)||!0===i&&er(s)||!e&&!er(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(e,"string"==typeof t,a),h=t!==n.Fragment?{...l,...o,ref:i}:{},{children:u}=e,d=(0,n.useMemo)(()=>(0,tp.S)(u)?u.get():u,[u]);return(0,n.createElement)(t,{...h,children:d})}(t,e,(r=d.visualElement,(0,n.useCallback)(t=>{t&&c.onMount&&c.onMount(t),r&&(t?r.mount(t):r.unmount()),i&&("function"==typeof i?i(t):em(i)&&(i.current=t))},[r])),c,u,s)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let h=(0,n.forwardRef)(l);return h[ep]=t,h}function ex(t,e,i){let n=t.getProps();return tF(n,e,void 0!==i?i:n.custom,t)}function ew(t,e){return t?.[e]??t?.default??t}let eT=t=>Array.isArray(t);var eb=i(23387);function eP(t,e){let i=t.getValue("willChange");if((0,tp.S)(i)&&i.add)return i.add(e);if(!i&&eb.W.WillChange){let i=new eb.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}var eS=i(30532),eA=i(19827),eV=i(76778),eM=i(47215);function eE(t){let e;return()=>(void 0===e&&(e=t()),e)}let ek=eE(()=>void 0!==window.ScrollTimeline);var eD=i(63894),eC=i(63704),ej=i(24744),eR=i(68589);let eL={},eO=function(t,e){let i=eE(t);return()=>eL[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var eF=i(47705);let eB=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,eI={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eB([0,.65,.55,1]),circOut:eB([.55,0,1,.45]),backIn:eB([.31,.01,.66,-.59]),backOut:eB([.33,1.53,.69,.99])};function eN(t){return"function"==typeof t&&"applyToOptions"in t}class eU extends eD.q{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!s,this.allowFlatten=r,this.options=t,(0,F.V)("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return eN(t)&&eO()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eO()?(0,eF.K)(e,i):"ease-out":(0,eR.D)(e)?eB(e):Array.isArray(e)?e.map(e=>t(e,i)||eI.easeOut):eI[e]}(o,s);Array.isArray(d)&&(u.easing=d),ej.Q.value&&eC.q.waapi++;let c={delay:n,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return ej.Q.value&&p.finished.finally(()=>{eC.q.waapi--}),p}(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=(0,eV.X)(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,eM.X)(Number(t))}get time(){return(0,eM.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,eM.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&ek())?(this.animation.timeline=t,eA.l):e(this)}}var eW=i(70144),e$=i(46009),eG=i(33972),eq=i(7712);let eX={anticipate:e$.b,backInOut:eG.ZZ,circInOut:eq.tn};class eY extends eU{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eX&&(t.ease=eX[t.ease])}(t),(0,eW.E)(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:s,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new eS.s({...r,autoplay:!1}),o=(0,eM.f)(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let eK=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(_.f.test(t)||"0"===t)&&!t.startsWith("url(")),ez=new Set(["opacity","clipPath","filter","transform"]),eH=eE(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eQ extends eD.q{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=tm.k.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:l,element:h,...u},c=h?.KeyframeResolver||Q;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:h}=i;this.resolvedAt=tm.k.now(),!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=eK(s,e),o=eK(r,e);return(0,F.$)(a===o,`You are trying to animate ${e} from "${s}" to "${r}". "${a?r:s}" is not an animatable value.`,"value-not-animatable"),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eN(i))&&n)}(t,s,r,a)&&((eb.W.instantAnimations||!o)&&h?.((0,eV.X)(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let u={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return eH()&&i&&ez.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(u)?new eY({...u,element:u.motionValue.owner.current}):new eS.s(u);d.finished.then(()=>this.notifyFinished()).catch(eA.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),K=!0,H(),z(),K=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let eZ=t=>null!==t,e_={type:"spring",stiffness:500,damping:25,restSpeed:10},eJ={type:"keyframes",duration:.8},e0={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e1=(t,e,i,n={},s,a)=>o=>{let l=ew(n,t)||{},h=l.delay||n.delay||0,{elapsed:u=0}=n;u-=(0,eM.f)(h);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-u,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:a?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(l)&&Object.assign(d,((t,{keyframes:e})=>e.length>2?eJ:r.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:e_:e0)(t,d)),d.duration&&(d.duration=(0,eM.f)(d.duration)),d.repeatDelay&&(d.repeatDelay=(0,eM.f)(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let c=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0===d.delay&&(c=!0)),(eb.W.instantAnimations||eb.W.skipAnimations)&&(c=!0,d.duration=0,d.delay=0),d.allowFlatten=!l.type&&!l.ease,c&&!a&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(eZ),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[r]}(d.keyframes,l);if(void 0!==t)return void G.Gt.update(()=>{d.onUpdate(t),d.onComplete()})}return l.isSync?new eS.s(d):new eQ(d)};function e5(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...o}=e;n&&(r=n);let l=[],h=s&&t.animationState&&t.animationState.getState()[s];for(let e in o){let n=t.getValue(e,t.latestValues[e]??null),s=o[e];if(void 0===s||h&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(h,e))continue;let a={delay:i,...ew(r||{},e)},u=n.get();if(void 0!==u&&!n.isAnimating&&!Array.isArray(s)&&s===u&&!a.velocity)continue;let d=!1;if(window.MotionHandoffAnimation){let i=t.props[ef];if(i){let t=window.MotionHandoffAnimation(i,e,G.Gt);null!==t&&(a.startTime=t,d=!0)}}eP(t,e),n.start(e1(e,n,s,t.shouldReduceMotion&&D.has(e)?{type:!1}:a,t,d));let c=n.animation;c&&l.push(c)}return a&&Promise.all(l).then(()=>{G.Gt.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=ex(t,e)||{};for(let e in s={...s,...i}){var r;let i=eT(r=s[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,tf.OQ)(i))}}(t,a)})}),l}function e2(t,e,i={}){let n=ex(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let r=n?()=>Promise.all(e5(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,s=0,r=1,a){let o=[],l=t.variantChildren.size,h=(l-1)*s,u="function"==typeof n,d=u?t=>n(t,l):1===r?(t=0)=>t*s:(t=0)=>h-t*s;return Array.from(t.variantChildren).sort(e3).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(e2(t,e,{...a,delay:i+(u?0:n)+d(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n,r,a,o,i)}:()=>Promise.resolve(),{when:o}=s;if(!o)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===o?[r,a]:[a,r];return t().then(()=>e())}}function e3(t,e){return t.sortNodePosition(e)}function e6(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}let e4=tj.length,e8=[...tC].reverse(),e7=tC.length;function e9(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function it(){return{animate:e9(!0),whileInView:e9(),whileHover:e9(),whileTap:e9(),whileDrag:e9(),whileFocus:e9(),exit:e9()}}class ie{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ii extends ie{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>e2(t,e,i)));else if("string"==typeof e)n=e2(t,e,i);else{let s="function"==typeof e?ex(t,e,i.custom):e;n=Promise.all(e5(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=it(),n=!0,s=e=>(i,n)=>{let s=ex(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function r(r){let{props:a}=t,o=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<e4;t++){let n=tj[t],s=e.props[n];(tD(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},l=[],h=new Set,u={},d=1/0;for(let e=0;e<e7;e++){var c,p;let m=e8[e],f=i[m],v=void 0!==a[m]?a[m]:o[m],y=tD(v),g=m===r?f.isActive:null;!1===g&&(d=e);let x=v===o[m]&&v!==a[m]&&y;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),f.protectedKeys={...u},!f.isActive&&null===g||!v&&!f.prevProp||tk(v)||"boolean"==typeof v)continue;let w=(c=f.prevProp,"string"==typeof(p=v)?p!==c:!!Array.isArray(p)&&!e6(p,c)),T=w||m===r&&f.isActive&&!x&&y||e>d&&y,b=!1,P=Array.isArray(v)?v:[v],S=P.reduce(s(m),{});!1===g&&(S={});let{prevResolvedValues:A={}}=f,V={...A,...S},M=e=>{T=!0,h.has(e)&&(b=!0,h.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in V){let e=S[t],i=A[t];if(!u.hasOwnProperty(t))(eT(e)&&eT(i)?e6(e,i):e===i)?void 0!==e&&h.has(t)?M(t):f.protectedKeys[t]=!0:null!=e?M(t):h.add(t)}f.prevProp=v,f.prevResolvedValues=S,f.isActive&&(u={...u,...S}),n&&t.blockInitialAnimation&&(T=!1);let E=!(x&&w)||b;T&&E&&l.push(...P.map(t=>({animation:t,options:{type:m}})))}if(h.size){let e={};if("boolean"!=typeof a.initial){let i=ex(t,Array.isArray(a.initial)?a.initial[0]:a.initial);i&&i.transition&&(e.transition=i.transition)}h.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let m=!!l.length;return n&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),n=!1,m?e(l):Promise.resolve()}return{animateChanges:r,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=r(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=it(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();tk(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let is=0;class ir extends ie{constructor(){super(...arguments),this.id=is++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let ia={x:!1,y:!1};function io(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let il=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ih(t){return{point:{x:t.pageX,y:t.pageY}}}function iu(t,e,i,n){return io(t,e,t=>il(t)&&i(t,ih(t)),n)}function id(t){return t.max-t.min}function ic(t,e,i,n=.5){t.origin=n,t.originPoint=(0,g.k)(e.min,e.max,t.origin),t.scale=id(i)/id(e),t.translate=(0,g.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function ip(t,e,i,n){ic(t.x,e.x,i.x,n?n.originX:void 0),ic(t.y,e.y,i.y,n?n.originY:void 0)}function im(t,e,i){t.min=i.min+e.min,t.max=t.min+id(e)}function iv(t,e,i){t.min=e.min-i.min,t.max=t.min+id(e)}function iy(t,e,i){iv(t.x,e.x,i.x),iv(t.y,e.y,i.y)}function ig(t){return[t("x"),t("y")]}let ix=({current:t})=>t?t.ownerDocument.defaultView:null;var iw=i(53191);let iT=(t,e)=>Math.abs(t-e);class ib{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:s=!1,distanceThreshold:r=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iA(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iT(t.x,e.x)**2+iT(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=G.uv;this.history.push({...n,timestamp:s});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iP(e,this.transformPagePoint),G.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=iA("pointercancel"===t.type?this.lastMoveEventInfo:iP(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!il(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=r,this.contextWindow=n||window;let a=iP(ih(t),this.transformPagePoint),{point:o}=a,{timestamp:l}=G.uv;this.history=[{...o,timestamp:l}];let{onSessionStart:h}=e;h&&h(t,iA(a,this.history)),this.removeListeners=(0,iw.F)(iu(this.contextWindow,"pointermove",this.handlePointerMove),iu(this.contextWindow,"pointerup",this.handlePointerUp),iu(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,G.WG)(this.updatePoint)}}function iP(t,e){return e?{point:e(t.point)}:t}function iS(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iA({point:t},e){return{point:t,delta:iS(t,iV(e)),offset:iS(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=iV(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>(0,eM.f)(.1)));)i--;if(!n)return{x:0,y:0};let r=(0,eM.X)(s.timestamp-n.timestamp);if(0===r)return{x:0,y:0};let a={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function iV(t){return t[t.length-1]}var iM=i(45818),iE=i(53678);function ik(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iD(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function iC(t,e,i){return{min:ij(t,e),max:ij(t,i)}}function ij(t,e){return"number"==typeof t?t:t[e]||0}let iR=new WeakMap;class iL{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=tS(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new ib(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ih(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(ia[t])return null;else return ia[t]=!0,()=>{ia[t]=!1};return ia.x||ia.y?null:(ia.x=ia.y=!0,()=>{ia.x=ia.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ig(t=>{let e=this.getAxisMotionValue(t).get()||0;if(j.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=id(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&G.Gt.postRender(()=>s(t,e)),eP(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},resumeAnimation:()=>ig(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,distanceThreshold:i,contextWindow:ix(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,s=this.isDragging;if(this.cancel(),!s||!n||!i)return;let{velocity:r}=n;this.startAnimation(r);let{onDragEnd:a}=this.getProps();a&&G.Gt.postRender(()=>a(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!iO(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?(0,g.k)(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?(0,g.k)(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&em(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:ik(t.x,i,s),y:ik(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iC(t,"left","right"),y:iC(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ig(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!em(e))return!1;let n=e.current;(0,F.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=function(t,e,i){let n=k(t,i),{scroll:s}=e;return s&&(V(n.x,s.offset.x),V(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),a=(t=s.layout.layoutBox,{x:iD(t.x,r.x),y:iD(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=y(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(ig(a=>{if(!iO(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return eP(this.visualElement,t),i.start(e1(t,i,0,e,this.visualElement,!1))}stopAnimation(){ig(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ig(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){ig(e=>{let{drag:i}=this.getProps();if(!iO(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-(0,g.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!em(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};ig(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=id(t),s=id(e);return s>n?i=(0,iM.q)(e.min,e.max-n,t.min):n>s&&(i=(0,iM.q)(t.min,t.max-s,e.min)),(0,iE.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ig(e=>{if(!iO(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set((0,g.k)(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;iR.set(this.visualElement,this);let t=iu(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();em(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),G.Gt.read(e);let s=io(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ig(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:a}}}function iO(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class iF extends ie{constructor(t){super(t),this.removeGroupControls=eA.l,this.removeListeners=eA.l,this.controls=new iL(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||eA.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let iB=t=>(e,i)=>{t&&G.Gt.postRender(()=>t(e,i))};class iI extends ie{constructor(){super(...arguments),this.removePointerDownListener=eA.l}onPointerDown(t){this.session=new ib(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ix(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:iB(t),onStart:iB(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&G.Gt.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iu(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var iN=i(32082);let iU={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iW(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let i$={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!j.px.test(t))return t;else t=parseFloat(t);let i=iW(t,e.target.x),n=iW(t,e.target.y);return`${i}% ${n}%`}},iG=!1;class iq extends n.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;for(let t in iY)tX[t]=iY[t],(0,v.j)(t)&&(tX[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),iG&&s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),iU.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:r}=i;return r&&(r.isPresent=s,iG=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?r.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?r.promote():r.relegate()||G.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ty.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iX(t){let[e,i]=(0,iN.xQ)(),s=(0,n.useContext)(t4.L);return(0,t6.jsx)(iq,{...t,layoutGroup:s,switchLayoutGroup:(0,n.useContext)(ev),isPresent:e,safeToRemove:i})}let iY={borderRadius:{...i$,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:i$,borderTopRightRadius:i$,borderBottomLeftRadius:i$,borderBottomRightRadius:i$,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=_.f.parse(t);if(n.length>5)return t;let s=_.f.createTransformer(t),r=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=o;let l=(0,g.k)(a,o,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}};var iK=i(6983);function iz(t){return(0,iK.G)(t)&&"ownerSVGElement"in t}var iH=i(56668);let iQ=(t,e)=>t.depth-e.depth;class iZ{constructor(){this.children=[],this.isDirty=!1}add(t){(0,iH.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,iH.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(iQ),this.isDirty=!1,this.children.forEach(t)}}let i_=["TopLeft","TopRight","BottomLeft","BottomRight"],iJ=i_.length,i0=t=>"string"==typeof t?parseFloat(t):t,i1=t=>"number"==typeof t||j.px.test(t);function i5(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let i2=i6(0,.5,eq.yT),i3=i6(.5,.95,eA.l);function i6(t,e,i){return n=>n<t?0:n>e?1:i((0,iM.q)(t,e,n))}function i4(t,e){t.min=e.min,t.max=e.max}function i8(t,e){i4(t.x,e.x),i4(t.y,e.y)}function i7(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function i9(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function nt(t,e,[i,n,s],r,a){!function(t,e=0,i=1,n=.5,s,r=t,a=t){if(j.KN.test(e)&&(e=parseFloat(e),e=(0,g.k)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=(0,g.k)(r.min,r.max,n);t===r&&(o-=e),t.min=i9(t.min,e,i,o,s),t.max=i9(t.max,e,i,o,s)}(t,e[i],e[n],e[s],e.scale,r,a)}let ne=["x","scaleX","originX"],ni=["y","scaleY","originY"];function nn(t,e,i,n){nt(t.x,e,ne,i?i.x:void 0,n?n.x:void 0),nt(t.y,e,ni,i?i.y:void 0,n?n.y:void 0)}function ns(t){return 0===t.translate&&1===t.scale}function nr(t){return ns(t.x)&&ns(t.y)}function na(t,e){return t.min===e.min&&t.max===e.max}function no(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nl(t,e){return no(t.x,e.x)&&no(t.y,e.y)}function nh(t){return id(t.x)/id(t.y)}function nu(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nd{constructor(){this.members=[]}add(t){(0,iH.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,iH.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nc={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},np=["","X","Y","Z"],nm=0;function nf(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function nv({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=nm++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ej.Q.value&&(nc.nodes=nc.calculatedTargetDeltas=nc.calculatedProjections=0),this.nodes.forEach(nx),this.nodes.forEach(nV),this.nodes.forEach(nM),this.nodes.forEach(nw),ej.Q.addProjectionMetrics&&ej.Q.addProjectionMetrics(nc)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new iZ)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new tg.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=iz(e)&&!(iz(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=0,s=()=>this.root.updateBlockedByResize=!1;G.Gt.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=tm.k.now(),n=({timestamp:e})=>{let s=e-i;s>=250&&((0,G.WG)(n),t(s-250))};return G.Gt.setup(n,!0),()=>(0,G.WG)(n)}(s,250),iU.hasAnimatedSinceResize&&(iU.hasAnimatedSinceResize=!1,this.nodes.forEach(nA)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||nR,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!nl(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...ew(r,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||nA(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,G.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nE),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[ef];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",G.Gt,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nb);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(nP);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(nS),this.nodes.forEach(ny),this.nodes.forEach(ng)):this.nodes.forEach(nP),this.clearAllSnapshots();let t=tm.k.now();G.uv.delta=(0,iE.q)(0,1e3/60,t-G.uv.timestamp),G.uv.timestamp=t,G.uv.isProcessing=!0,G.PP.update.process(G.uv),G.PP.preRender.process(G.uv),G.PP.render.process(G.uv),G.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ty.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(nT),this.sharedNodes.forEach(nk)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,G.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){G.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||id(this.snapshot.measuredBox.x)||id(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=tS(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nr(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||T(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),nF((e=n).x),nF(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return tS();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nI))){let{scroll:t}=this.root;t&&(V(e.x,t.offset.x),V(e.y,t.offset.y))}return e}removeElementScroll(t){let e=tS();if(i8(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&i8(e,t),V(e.x,s.offset.x),V(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=tS();i8(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&E(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),T(n.latestValues)&&E(i,n.latestValues)}return T(this.latestValues)&&E(i,this.latestValues),i}removeTransform(t){let e=tS();i8(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!T(i.latestValues))continue;w(i.latestValues)&&i.updateSnapshot();let n=tS();i8(n,i.measurePageBox()),nn(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return T(this.latestValues)&&nn(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==G.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=G.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tS(),this.relativeTargetOrigin=tS(),iy(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),i8(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=tS(),this.targetWithTransforms=tS()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,im(r.x,a.x,o.x),im(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):i8(this.target,this.layout.layoutBox),A(this.target,this.targetDelta)):i8(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=tS(),this.relativeTargetOrigin=tS(),iy(this.relativeTargetOrigin,this.target,t.target),i8(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ej.Q.value&&nc.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||w(this.parent.latestValues)||b(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===G.uv.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;i8(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let s,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(s=i[o]).projectionDelta;let{visualElement:a}=s.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&E(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,A(t,r)),n&&T(s.latestValues)&&E(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=tS());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(i7(this.prevProjectionDelta.x,this.projectionDelta.x),i7(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),ip(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&nu(this.projectionDelta.x,this.prevProjectionDelta.x)&&nu(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),ej.Q.value&&nc.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tb(),this.projectionDelta=tb(),this.projectionDeltaWithTransform=tb()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},a=tb();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=tS(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(nj));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(nD(a.x,t.x,n),nD(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,v;iy(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,v=n,nC(p.x,m.x,f.x,v),nC(p.y,m.y,f.y,v),i&&(h=this.relativeTarget,c=i,na(h.x,c.x)&&na(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=tS()),i8(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=(0,g.k)(0,i.opacity??1,i2(n)),t.opacityExit=(0,g.k)(e.opacity??1,0,i3(n))):r&&(t.opacity=(0,g.k)(e.opacity??1,i.opacity??1,n));for(let s=0;s<iJ;s++){let r=`border${i_[s]}Radius`,a=i5(e,r),o=i5(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||i1(a)===i1(o)?(t[r]=Math.max((0,g.k)(i0(a),i0(o),n),0),(j.KN.test(o)||j.KN.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=(0,g.k)(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,G.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=G.Gt.update(()=>{iU.hasAnimatedSinceResize=!0,eC.q.layout++,this.motionValue||(this.motionValue=(0,tf.OQ)(0)),this.currentAnimation=function(t,e,i){let n=(0,tp.S)(t)?t:(0,tf.OQ)(t);return n.start(e1("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{eC.q.layout--},onComplete:()=>{eC.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&nB(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||tS();let e=id(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=id(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}i8(e,i),E(e,s),ip(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nd),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&nf("z",t,n,this.animationValues);for(let e=0;e<np.length;e++)nf(`rotate${np[e]}`,t,n,this.animationValues),nf(`skew${np[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=eh(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=eh(e?.pointerEvents)||""),this.hasProjected&&!T(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let s=n.animationValues||n.latestValues;this.applyTransformsToTarget();let r=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((s||r||a)&&(n=`translate3d(${s}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s);i&&(r=i(s,r)),t.transform=r;let{x:a,y:o}=this.projectionDelta;for(let e in t.transformOrigin=`${100*a.origin}% ${100*o.origin}% 0`,n.animationValues?t.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:t.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,tX){if(void 0===s[e])continue;let{correct:i,applyTo:a,isCSSVariable:o}=tX[e],l="none"===r?s[e]:i(s[e],n);if(a){let e=a.length;for(let i=0;i<e;i++)t[a[i]]=l}else o?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?eh(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(nb),this.root.sharedNodes.clear()}}}function ny(t){t.updateLayout()}function ng(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?ig(t=>{let n=r?e.measuredBox[t]:e.layoutBox[t],s=id(n);n.min=i[t].min,n.max=n.min+s}):nB(s,e.layoutBox,i)&&ig(n=>{let s=r?e.measuredBox[n]:e.layoutBox[n],a=id(i[n]);s.max=s.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=tb();ip(a,i,e.layoutBox);let o=tb();r?ip(o,t.applyTransform(n,!0),e.measuredBox):ip(o,i,e.layoutBox);let l=!nr(a),h=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let a=tS();iy(a,e.layoutBox,s.layoutBox);let o=tS();iy(o,i,r.layoutBox),nl(a,o)||(h=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function nx(t){ej.Q.value&&nc.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nw(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function nT(t){t.clearSnapshot()}function nb(t){t.clearMeasurements()}function nP(t){t.isLayoutDirty=!1}function nS(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nA(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nV(t){t.resolveTargetDelta()}function nM(t){t.calcProjection()}function nE(t){t.resetSkewAndRotation()}function nk(t){t.removeLeadSnapshot()}function nD(t,e,i){t.translate=(0,g.k)(e.translate,0,i),t.scale=(0,g.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nC(t,e,i,n){t.min=(0,g.k)(e.min,i.min,n),t.max=(0,g.k)(e.max,i.max,n)}function nj(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nR={duration:.45,ease:[.4,0,.1,1]},nL=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nO=nL("applewebkit/")&&!nL("chrome/")?Math.round:eA.l;function nF(t){t.min=nO(t.min),t.max=nO(t.max)}function nB(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nh(e)-nh(i)))}function nI(t){return t!==t.root&&t.scroll?.wasRoot}let nN=nv({attachResizeListener:(t,e)=>io(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nU={current:void 0},nW=nv({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nU.current){let t=new nN({});t.mount(window),t.setOptions({layoutScroll:!0}),nU.current=t}return nU.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function n$(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function nG(t){return!("touch"===t.pointerType||ia.x||ia.y)}function nq(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&G.Gt.postRender(()=>s(e,ih(e)))}class nX extends ie{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=n$(t,i),a=t=>{if(!nG(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{nG(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}(t,(t,e)=>(nq(this.node,e,"Start"),t=>nq(this.node,t,"End"))))}unmount(){}}class nY extends ie{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,iw.F)(io(this.node.current,"focus",()=>this.onFocus()),io(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var nK=i(27351);let nz=(t,e)=>!!e&&(t===e||nz(t,e.parentElement)),nH=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nQ=new WeakSet;function nZ(t){return e=>{"Enter"===e.key&&t(e)}}function n_(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function nJ(t){return il(t)&&!(ia.x||ia.y)}function n0(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&G.Gt.postRender(()=>s(e,ih(e)))}class n1 extends ie{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=n$(t,i),a=t=>{let n=t.currentTarget;if(!nJ(t))return;nQ.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),nQ.has(n)&&nQ.delete(n),nJ(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||nz(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),(0,nK.s)(t))&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let n=nZ(()=>{if(nQ.has(i))return;n_(i,"down");let t=nZ(()=>{n_(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>n_(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)})(t,s)),nH.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(n0(this.node,e,"Start"),(t,{success:e})=>n0(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let n5=new WeakMap,n2=new WeakMap,n3=t=>{let e=n5.get(t.target);e&&e(t)},n6=t=>{t.forEach(n3)},n4={some:0,all:1};class n8 extends ie{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:n4[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;n2.has(i)||n2.set(i,{});let n=n2.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(n6,{root:t,...e})),n[s]}(e);return n5.set(t,i),n.observe(t),()=>{n5.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let n7=function(t,e){if("undefined"==typeof Proxy)return eg;let i=new Map,n=(i,n)=>eg(i,n,t,e);return new Proxy((t,e)=>n(t,e),{get:(s,r)=>"create"===r?n:(i.has(r)||i.set(r,eg(r,void 0,t,e)),i.get(r))})}({animation:{Feature:ii},exit:{Feature:ir},inView:{Feature:n8},tap:{Feature:n1},focus:{Feature:nY},hover:{Feature:nX},pan:{Feature:iI},drag:{Feature:iF,ProjectionNode:nW,MeasureLayout:iX},layout:{ProjectionNode:nW,MeasureLayout:iX}},(t,e)=>t3(t)?new t5(e):new tz(e,{allowProjection:t!==n.Fragment}))},68589:(t,e,i)=>{i.d(e,{D:()=>n});let n=t=>Array.isArray(t)&&"number"==typeof t[0]},68972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},69515:(t,e,i)=>{i.d(e,{Gt:()=>s,PP:()=>o,WG:()=>r,uv:()=>a});var n=i(19827);let{schedule:s,cancel:r,state:a,steps:o}=(0,i(58437).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},70144:(t,e,i)=>{i.d(e,{E:()=>o});var n=i(56330),s=i(63669),r=i(82886);let a={decay:n.B,inertia:n.B,tween:s.i,keyframes:s.i,spring:r.o};function o(t){"string"==typeof t.type&&(t.type=a[t.type])}},73945:(t,e,i)=>{i.d(e,{Y:()=>s});var n=i(62923);function s(t,e,i){let s=Math.max(e-5,0);return(0,n.f)(i-t(s),e-s)}},74261:(t,e,i)=>{let n;i.d(e,{k:()=>o});var s=i(23387),r=i(69515);function a(){n=void 0}let o={now:()=>(void 0===n&&o.set(r.uv.isProcessing||s.W.useManualTiming?r.uv.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(a)}}},74436:(t,e,i)=>{i.d(e,{k5:()=>u});var n=i(12115),s={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},r=n.createContext&&n.createContext(s),a=["attr","size","title"];function o(){return(o=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t}).apply(this,arguments)}function l(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)}return i}function h(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?l(Object(i),!0).forEach(function(e){var n,s,r;n=t,s=e,r=i[e],(s=function(t){var e=function(t,e){if("object"!=typeof t||!t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,e||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==typeof e?e:e+""}(s))in n?Object.defineProperty(n,s,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[s]=r}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function u(t){return e=>n.createElement(d,o({attr:h({},t.attr)},e),function t(e){return e&&e.map((e,i)=>n.createElement(e.tag,h({key:i},e.attr),t(e.child)))}(t.child))}function d(t){var e=e=>{var i,{attr:s,size:r,title:l}=t,u=function(t,e){if(null==t)return{};var i,n,s=function(t,e){if(null==t)return{};var i={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;i[n]=t[n]}return i}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(n=0;n<r.length;n++)i=r[n],!(e.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(t,i)&&(s[i]=t[i])}return s}(t,a),d=r||e.size||"1em";return e.className&&(i=e.className),t.className&&(i=(i?i+" ":"")+t.className),n.createElement("svg",o({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},e.attr,s,u,{className:i,style:h(h({color:t.color||e.color},e.style),t.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),l&&n.createElement("title",null,l),t.children)};return void 0!==r?n.createElement(r.Consumer,null,t=>e(t)):e(s)}},75626:(t,e,i)=>{i.d(e,{v:()=>s});var n=i(56668);class s{constructor(){this.subscriptions=[]}add(t){return(0,n.Kq)(this.subscriptions,t),()=>(0,n.Ai)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},76778:(t,e,i)=>{i.d(e,{X:()=>s});let n=t=>null!==t;function s(t,{repeat:e,repeatType:i="loop"},r,a=1){let o=t.filter(n),l=a<0||e&&"loop"!==i&&e%2==1?0:o.length-1;return l&&void 0!==r?r:o[l]}},78606:(t,e,i)=>{i.d(e,{j:()=>s,p:()=>a});let n=t=>e=>"string"==typeof e&&e.startsWith(t),s=n("--"),r=n("var(--"),a=t=>!!r(t)&&o.test(t.split("/*")[0].trim()),o=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},80845:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(12115).createContext)(null)},82885:(t,e,i)=>{i.d(e,{M:()=>s});var n=i(12115);function s(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},82886:(t,e,i)=>{i.d(e,{o:()=>m});var n=i(53678),s=i(47215),r=i(47705),a=i(52458),o=i(73945);let l={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var h=i(54542);function u(t,e){return t*Math.sqrt(1-e*e)}let d=["duration","bounce"],c=["stiffness","damping","mass"];function p(t,e){return e.some(e=>void 0!==t[e])}function m(t=l.visualDuration,e=l.bounce){let i,f="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:v,restDelta:y}=f,g=f.keyframes[0],x=f.keyframes[f.keyframes.length-1],w={done:!1,value:g},{stiffness:T,damping:b,mass:P,duration:S,velocity:A,isResolvedFromDuration:V}=function(t){let e={velocity:l.velocity,stiffness:l.stiffness,damping:l.damping,mass:l.mass,isResolvedFromDuration:!1,...t};if(!p(t,c)&&p(t,d))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,r=2*(0,n.q)(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:l.mass,stiffness:s,damping:r}}else{let i=function({duration:t=l.duration,bounce:e=l.bounce,velocity:i=l.velocity,mass:r=l.mass}){let a,o;(0,h.$)(t<=(0,s.f)(l.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let d=1-e;d=(0,n.q)(l.minDamping,l.maxDamping,d),t=(0,n.q)(l.minDuration,l.maxDuration,(0,s.X)(t)),d<1?(a=e=>{let n=e*d,s=n*t;return .001-(n-i)/u(e,d)*Math.exp(-s)},o=e=>{let n=e*d*t,s=Math.pow(d,2)*Math.pow(e,2)*t,r=Math.exp(-n),o=u(Math.pow(e,2),d);return(n*i+i-s)*r*(-a(e)+.001>0?-1:1)/o}):(a=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),o=e=>t*t*(i-e)*Math.exp(-e*t));let c=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(a,o,5/t);if(t=(0,s.f)(t),isNaN(c))return{stiffness:l.stiffness,damping:l.damping,duration:t};{let e=Math.pow(c,2)*r;return{stiffness:e,damping:2*d*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:l.mass}).isResolvedFromDuration=!0}return e}({...f,velocity:-(0,s.X)(f.velocity||0)}),M=A||0,E=b/(2*Math.sqrt(T*P)),k=x-g,D=(0,s.X)(Math.sqrt(T/P)),C=5>Math.abs(k);if(v||(v=C?l.restSpeed.granular:l.restSpeed.default),y||(y=C?l.restDelta.granular:l.restDelta.default),E<1){let t=u(D,E);i=e=>x-Math.exp(-E*D*e)*((M+E*D*k)/t*Math.sin(t*e)+k*Math.cos(t*e))}else if(1===E)i=t=>x-Math.exp(-D*t)*(k+(M+D*k)*t);else{let t=D*Math.sqrt(E*E-1);i=e=>{let i=Math.exp(-E*D*e),n=Math.min(t*e,300);return x-i*((M+E*D*k)*Math.sinh(n)+t*k*Math.cosh(n))/t}}let j={calculatedDuration:V&&S||null,next:t=>{let e=i(t);if(V)w.done=t>=S;else{let n=0===t?M:0;E<1&&(n=0===t?(0,s.f)(M):(0,o.Y)(i,t,e));let r=Math.abs(x-e)<=y;w.done=Math.abs(n)<=v&&r}return w.value=w.done?x:e,w},toString:()=>{let t=Math.min((0,a.t)(j),a.Y),e=(0,r.K)(e=>j.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return j}m.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min((0,a.t)(n),a.Y);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:(0,s.X)(r)}}(t,100,m);return t.ease=e.ease,t.duration=(0,s.f)(e.duration),t.type="keyframes",t}},90869:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(12115).createContext)({})},91765:(t,e,i)=>{i.d(e,{V:()=>n});let n=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},97494:(t,e,i)=>{i.d(e,{E:()=>s});var n=i(12115);let s=i(68972).B?n.useLayoutEffect:n.useEffect}}]);