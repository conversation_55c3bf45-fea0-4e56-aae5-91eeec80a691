(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7968],{37969:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(95155),r=t(12115),l=t(10351),n=t(26071),i=t(52814),c=t(13741),d=t(17703),o=t(93915),x=t(66440),m=t(30353),h=t(75399),u=t(41659),p=t(64198),y=t(24630);function g(){var e,s,t,g,j;let[N,f]=(0,r.useState)("deposits"),[b,v]=(0,r.useState)([]),[w,A]=(0,r.useState)([]),[k,P]=(0,r.useState)(!0),[C,D]=(0,r.useState)(null),[E,S]=(0,r.useState)(!1),[R,F]=(0,r.useState)(!1),[T,W]=(0,r.useState)("approve"),[I,O]=(0,r.useState)(""),[L,G]=(0,r.useState)({search:"",status:void 0,dateFrom:"",dateTo:"",page:1,limit:20}),[M,z]=(0,r.useState)({totalDeposits:0,totalWithdrawals:0,pendingDeposits:0,pendingWithdrawals:0,depositVolume:0,withdrawalVolume:0,successRate:0});(0,r.useEffect)(()=>{V(),_()},[N,L]);let V=async()=>{try{P(!0);let e={...L,type:"deposits"===N?"deposit":"withdrawal"};["PENDING","PROCESSING","COMPLETED","FAILED","CANCELLED"].includes(L.status)?e.status=L.status:delete e.status,console.log("[AdminPaymentsPage] Fetching transactions with filters:",e);let s=await y.lD.getAllTransactions(e);console.log("[AdminPaymentsPage] Full backend response:",s);let t=Array.isArray(s.transactions)?s.transactions:Array.isArray(s)?s:[];console.log("[AdminPaymentsPage] Transactions array for mapping:",t),t.length>0?console.log("[AdminPaymentsPage] First transaction object:",t[0]):console.log("[AdminPaymentsPage] No transactions found in backend response.");let a=t.map(e=>e?{...e,key:e._id||e.id||"",userId:e.userId||{},amount:"number"!=typeof e.amount||isNaN(e.amount)?Number(e.amount)||0:e.amount,description:e.description||"",reference:e.reference||"",type:e.type||"",date:e.date||e.createdAt||"",status:e.status||"",createdAt:e.createdAt||e.date||"",method:e.method||e.paymentMethod||"",paymentMethod:e.paymentMethod||e.method||"",fees:"number"!=typeof e.fees||isNaN(e.fees)?Number(e.fees)||0:e.fees,netAmount:"number"!=typeof e.netAmount||isNaN(e.netAmount)?"number"==typeof e.amount&&"number"==typeof e.fees?e.amount-e.fees:0:e.netAmount}:{key:"",userId:{},amount:0,description:"",reference:"",type:"",date:"",status:"",createdAt:"",method:"",paymentMethod:"",fees:0,netAmount:0});"deposits"===N?v(a):A(a)}catch(e){console.error("[AdminPaymentsPage] Failed to load",N,e),p.oR.error("Failed to load ".concat(N))}finally{P(!1)}},_=async()=>{try{console.log("[AdminPaymentsPage] Fetching stats...");let[e,s]=await Promise.all([y.Pd.getDepositStats(),y.uX.getWithdrawalStats()]);console.log("[AdminPaymentsPage] Deposit stats:",e),console.log("[AdminPaymentsPage] Withdrawal stats:",s),z({totalDeposits:e.totalDeposits,totalWithdrawals:s.totalWithdrawals,pendingDeposits:e.pendingDeposits,pendingWithdrawals:s.pendingWithdrawals,depositVolume:e.totalAmount,withdrawalVolume:s.totalAmount,successRate:0})}catch(e){console.error("[AdminPaymentsPage] Failed to load payment stats:",e)}},J=async()=>{if(C&&"withdrawals"===N)try{"approve"===T?(await y.uX.approveWithdrawal(C.id),p.oR.success("Withdrawal approved successfully")):(await y.uX.rejectWithdrawal(C.id),p.oR.success("Withdrawal rejected successfully")),F(!1),O(""),V()}catch(e){p.oR.error("Failed to ".concat(T," withdrawal"))}},U=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e),X=e=>new Date(e).toLocaleDateString("en-NG",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),K=e=>{switch(e){case"COMPLETED":case"APPROVED":return"success";case"PENDING":return"warning";case"FAILED":case"REJECTED":return"error";case"PROCESSING":return"info";default:return"default"}},Z=[{key:"user",title:"User",render:e=>{let s=e&&(e.userId||e.user)||{},t=s&&s.firstName?s.firstName:"",r=s&&s.lastName?s.lastName:"",l=s&&(s._id||s.id)?s._id||s.id:"string"==typeof s?s:"N/A";return(0,a.jsx)("div",{children:t||r?(0,a.jsxs)("p",{className:"font-medium text-white",children:[t," ",r]}):(0,a.jsxs)("p",{className:"text-gray-400",children:["ID: ",l]})})}},{key:"amount",title:"Amount",render:e=>(0,a.jsx)("span",{className:"font-semibold text-white",children:U(Number(e.amount)||0)})},{key:"description",title:"Description",render:e=>(0,a.jsx)("span",{className:"text-gray-300",children:e.description||""})},{key:"reference",title:"Reference",render:e=>(0,a.jsx)("span",{className:"text-gray-400",children:e.reference||""})},{key:"type",title:"Type",render:e=>(0,a.jsx)("span",{className:"text-gray-400",children:e.type||""})},{key:"date",title:"Date",render:e=>(0,a.jsx)("span",{className:"text-gray-400",children:e.date?X(e.date):e.createdAt?X(e.createdAt):""})},{key:"actions",title:"Actions",render:e=>(0,a.jsx)(c.Ay,{size:"sm",variant:"outline",onClick:()=>{D(e),S(!0)},children:(0,a.jsx)(l.Vap,{})})}],Y=[{key:"user",title:"User",render:e=>{var s,t,r;return(0,a.jsx)("div",{children:(null==(s=e.user)?void 0:s.firstName)||(null==(t=e.user)?void 0:t.lastName)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("p",{className:"font-medium text-white",children:[e.user.firstName," ",e.user.lastName]}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:e.user.email})]}):(0,a.jsx)("p",{className:"text-gray-400",children:(null==(r=e.user)?void 0:r.id)||"N/A"})})}},{key:"amount",title:"Amount",render:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-semibold text-white",children:"number"!=typeof e.amount||isNaN(e.amount)?"₦0.00":U(e.amount)}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:["Fee: ","number"!=typeof e.fees||isNaN(e.fees)?"₦0.00":U(e.fees)]})]})},{key:"method",title:"Method",render:e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.lZI,{className:"text-blue-500"}),(0,a.jsx)("span",{className:"text-gray-300",children:e.method||e.paymentMethod||"N/A"})]})},{key:"status",title:"Status",render:e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(e=>{switch(e){case"COMPLETED":case"APPROVED":return(0,a.jsx)(l.YrT,{className:"text-green-500"});case"PENDING":return(0,a.jsx)(l.Ohp,{className:"text-yellow-500"});case"FAILED":case"REJECTED":return(0,a.jsx)(l.yGN,{className:"text-red-500"});case"PROCESSING":return(0,a.jsx)(l.jTZ,{className:"text-blue-500 animate-spin"});default:return(0,a.jsx)(l.Ohp,{className:"text-gray-500"})}})(e.status),(0,a.jsx)(i.A,{variant:K(e.status),children:e.status||"N/A"})]})},{key:"date",title:"Date",render:e=>(0,a.jsx)("span",{className:"text-gray-400",children:e.createdAt?X(e.createdAt):e.date?X(e.date):"N/A"})},{key:"actions",title:"Actions",render:e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.Ay,{size:"sm",variant:"outline",onClick:()=>{D(e),S(!0)},children:(0,a.jsx)(l.Vap,{})}),"PENDING"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.Ay,{size:"sm",variant:"outline",onClick:()=>{D(e),W("approve"),F(!0)},className:"text-green-400 hover:text-green-300",children:(0,a.jsx)(l.YrT,{})}),(0,a.jsx)(c.Ay,{size:"sm",variant:"outline",onClick:()=>{D(e),W("reject"),F(!0)},className:"text-red-400 hover:text-red-300",children:(0,a.jsx)(l.yGN,{})})]})]})}];return(0,a.jsxs)(n.A,{title:"Payment Management",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Payment Management"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Monitor and manage deposits and withdrawals"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(c.Ay,{variant:"outline",onClick:V,children:[(0,a.jsx)(l.jTZ,{className:"mr-2"}),"Refresh"]}),(0,a.jsxs)(c.Ay,{variant:"outline",children:[(0,a.jsx)(l.a4x,{className:"mr-2"}),"Export"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:M.totalDeposits.toLocaleString()}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:U(M.depositVolume)})]}),(0,a.jsx)(l.ARf,{className:"text-green-500 text-2xl"})]})}),(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Withdrawals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:M.totalWithdrawals.toLocaleString()}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:U(M.withdrawalVolume)})]}),(0,a.jsx)(l.JW4,{className:"text-blue-500 text-2xl"})]})}),(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Pending Approvals"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:M.pendingDeposits+M.pendingWithdrawals}),(0,a.jsxs)("p",{className:"text-sm text-gray-400",children:[M.pendingDeposits," deposits, ",M.pendingWithdrawals," withdrawals"]})]}),(0,a.jsx)(l.Ohp,{className:"text-yellow-500 text-2xl"})]})}),(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Success Rate"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-white",children:[M.successRate.toFixed(1),"%"]}),(0,a.jsx)("p",{className:"text-sm text-green-400",children:"Last 30 days"})]}),(0,a.jsx)(l.z8N,{className:"text-purple-500 text-2xl"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-800 rounded-lg p-1",children:[(0,a.jsx)("button",{onClick:()=>f("deposits"),className:"flex-1 py-2 px-4 rounded-md transition-colors ".concat("deposits"===N?"bg-green-600 text-white":"text-gray-400 hover:text-white"),children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(l.ARf,{}),(0,a.jsxs)("span",{children:["Deposits (",M.totalDeposits,")"]})]})}),(0,a.jsx)("button",{onClick:()=>f("withdrawals"),className:"flex-1 py-2 px-4 rounded-md transition-colors ".concat("withdrawals"===N?"bg-green-600 text-white":"text-gray-400 hover:text-white"),children:(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,a.jsx)(l.JW4,{}),(0,a.jsxs)("span",{children:["Withdrawals (",M.totalWithdrawals,")"]})]})})]}),(0,a.jsx)(d.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsx)(o.A,{placeholder:"Search by user or reference...",value:L.search,onChange:e=>G({...L,search:e.target.value})}),(0,a.jsx)(m.A,{value:L.status||"",onChange:e=>{let s=e.target.value;G({...L,status:""===s?void 0:s})},options:[{value:"",label:"All Status"},{value:"PENDING",label:"Pending"},{value:"PROCESSING",label:"Processing"},{value:"COMPLETED",label:"Completed"},{value:"APPROVED",label:"Approved"},{value:"REJECTED",label:"Rejected"},{value:"FAILED",label:"Failed"},{value:"CANCELLED",label:"Cancelled"}]}),(0,a.jsx)(o.A,{type:"date",value:L.dateFrom,onChange:e=>G({...L,dateFrom:e.target.value}),placeholder:"From Date"}),(0,a.jsx)(o.A,{type:"date",value:L.dateTo,onChange:e=>G({...L,dateTo:e.target.value}),placeholder:"To Date"}),(0,a.jsx)(c.Ay,{variant:"outline",onClick:()=>G({search:"",status:void 0,dateFrom:"",dateTo:"",page:1,limit:20}),children:"Clear Filters"})]})}),(0,a.jsxs)(d.A,{className:"bg-gray-800 border-gray-700",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"deposits"===N?"Deposits":"Withdrawals"})}),(0,a.jsx)(h.Ay,{columns:"deposits"===N?Z:Y,data:"deposits"===N?b:w,loading:k,emptyMessage:"No ".concat(N," found")})]})]}),(0,a.jsx)(x.Ay,{isOpen:E,onClose:()=>S(!1),title:"Payment Details",size:"lg",children:C&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-white mb-2",children:"Payment Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Reference:"}),(0,a.jsx)("span",{className:"text-white font-mono",children:C.reference})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Amount:"}),(0,a.jsx)("span",{className:"text-white",children:U(C.amount)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Fees:"}),(0,a.jsx)("span",{className:"text-white",children:U(C.fees||0)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Net Amount:"}),(0,a.jsx)("span",{className:"text-white",children:U(C.netAmount||C.amount)})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-white mb-2",children:"User Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Name:"}),(0,a.jsxs)("span",{className:"text-white",children:[null==(e=C.user)?void 0:e.firstName," ",null==(s=C.user)?void 0:s.lastName]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Email:"}),(0,a.jsx)("span",{className:"text-white",children:null==(t=C.user)?void 0:t.email})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Status:"}),(0,a.jsx)(i.A,{variant:K(C.status),children:C.status})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-white mb-4",children:"Timeline"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white text-sm",children:"Payment initiated"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:X(C.createdAt)})]})]}),C.processedAt&&(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white text-sm",children:"Payment processed"}),(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:X(C.processedAt)})]})]})]})]}),C.notes&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-white mb-2",children:"Notes"}),(0,a.jsx)("p",{className:"text-gray-300 text-sm bg-gray-700 p-3 rounded-lg",children:C.notes})]})]})}),(0,a.jsx)(x.Ay,{isOpen:R,onClose:()=>F(!1),title:"".concat("approve"===T?"Approve":"Reject"," Withdrawal"),children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("p",{className:"text-gray-300",children:["Are you sure you want to ",T," this withdrawal?"]}),C&&(0,a.jsx)("div",{className:"p-4 bg-gray-700 rounded-lg",children:(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"User:"}),(0,a.jsxs)("span",{className:"text-white",children:[null==(g=C.user)?void 0:g.firstName," ",null==(j=C.user)?void 0:j.lastName]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Amount:"}),(0,a.jsx)("span",{className:"text-white",children:U(C.amount)})]})]})}),(0,a.jsx)(u.A,{label:"Notes",value:I,onChange:e=>O(e.target.value),placeholder:"Add notes for this ".concat(T,"..."),rows:3}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(c.Ay,{variant:"outline",onClick:()=>F(!1),children:"Cancel"}),(0,a.jsxs)(c.Ay,{variant:"approve"===T?"primary":"danger",onClick:J,children:["approve"===T?"Approve":"Reject"," Withdrawal"]})]})]})})]})}},41659:(e,s,t)=>{"use strict";t.d(s,{A:()=>l,T:()=>r});var a=t(95155);let r=(0,t(12115).forwardRef)((e,s)=>{let{label:t,error:r,helperText:l,variant:n="default",resize:i="vertical",className:c="",...d}=e,o="\n    ".concat("\n    w-full px-4 py-3 rounded-lg transition-all duration-200\n    focus:outline-none focus:ring-2 focus:ring-green-500/50\n    disabled:opacity-50 disabled:cursor-not-allowed\n    min-h-[100px]\n  "," \n    ").concat({default:"\n      bg-gray-800 border border-gray-700 text-white\n      placeholder-gray-400 hover:border-gray-600\n      focus:border-green-500\n    ",filled:"\n      bg-gray-700 border-0 text-white\n      placeholder-gray-400 hover:bg-gray-600\n    ",outline:"\n      bg-transparent border-2 border-gray-600 text-white\n      placeholder-gray-400 hover:border-gray-500\n      focus:border-green-500\n    "}[n]," \n    ").concat({none:"resize-none",vertical:"resize-y",horizontal:"resize-x",both:"resize"}[i]," \n    ").concat(c," \n    ").concat(r?"border-red-500 focus:border-red-500 focus:ring-red-500/50":"","\n  ");return(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:t}),(0,a.jsx)("textarea",{ref:s,className:o,...d}),r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:r}),l&&!r&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:l})]})});r.displayName="Textarea";let l=r},66440:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>o,aF:()=>c,k5:()=>d});var a=t(95155),r=t(60760),l=t(68289);t(12115);var n=t(10351);let i={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"};function c(e){let{isOpen:s,onClose:t,title:c,children:d,size:o="md",showCloseButton:x=!0}=e;return(0,a.jsx)(r.N,{children:s&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:t}),(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"\n                relative w-full ".concat(i[o]," \n                bg-gray-900 border border-gray-800 rounded-lg shadow-xl\n              "),onClick:e=>e.stopPropagation(),children:[(c||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800",children:[c&&(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:c}),x&&(0,a.jsx)("button",{onClick:t,className:"p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,a.jsx)(n.yGN,{className:"w-5 h-5 text-gray-400"})})]}),(0,a.jsx)("div",{className:"p-6",children:d})]})})]})})}function d(e){let{isOpen:s,onClose:t,title:r,children:l,onSubmit:n,submitText:i="Submit",isLoading:d=!1,size:o="md"}=e;return(0,a.jsx)(c,{isOpen:s,onClose:t,title:r,size:o,children:(0,a.jsxs)("form",{onSubmit:n,className:"space-y-6",children:[l,(0,a.jsxs)("div",{className:"flex space-x-3 justify-end pt-4 border-t border-gray-800",children:[(0,a.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-300 hover:text-white transition-colors",disabled:d,children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",disabled:d,className:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2",children:[d&&(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,a.jsx)("span",{children:i})]})]})]})})}let o=c},75399:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>o,Wh:()=>c,XI:()=>i,rA:()=>d});var a=t(95155),r=t(68289),l=t(12115),n=t(10351);function i(e){let{data:s,columns:t,loading:i=!1,searchable:c=!1,searchPlaceholder:d="Search...",onSearch:o,emptyMessage:x="No data available",className:m=""}=e,[h,u]=l.useState({key:null,direction:"asc"}),[p,y]=l.useState(""),g=l.useMemo(()=>h.key?[...s].sort((e,s)=>{let t=e[h.key],a=s[h.key];return t<a?"asc"===h.direction?-1:1:t>a?"asc"===h.direction?1:-1:0}):s,[s,h]);return i?(0,a.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg ".concat(m),children:[c&&(0,a.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",placeholder:d,className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500",disabled:!0})]})}),(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Loading..."})]})]}):(0,a.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg overflow-hidden ".concat(m),children:[c&&(0,a.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",placeholder:d,value:p,onChange:e=>{var s;y(s=e.target.value),null==o||o(s)},className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"})]})}),0===g.length?(0,a.jsx)("div",{className:"p-8 text-center",children:(0,a.jsx)("p",{className:"text-gray-400",children:x})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-800/50",children:(0,a.jsx)("tr",{children:t.map(e=>(0,a.jsx)("th",{className:"\n                      px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\n                      ".concat(e.sortable?"cursor-pointer hover:text-white":"","\n                      ").concat(e.width?e.width:"","\n                    "),onClick:()=>{var s;let t;return e.sortable&&(s=e.key,t="asc",void(h.key===s&&"asc"===h.direction&&(t="desc"),u({key:s,direction:t})))},children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:e.title}),e.sortable&&(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(n.wAb,{className:"w-3 h-3 ".concat(h.key===e.key&&"asc"===h.direction?"text-green-400":"text-gray-500")}),(0,a.jsx)(n.fK4,{className:"w-3 h-3 -mt-1 ".concat(h.key===e.key&&"desc"===h.direction?"text-green-400":"text-gray-500")})]})]})},String(e.key)))})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-800",children:g.map((e,s)=>(0,a.jsx)(r.P.tr,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.05*s},className:"hover:bg-gray-800/30 transition-colors",children:t.map(s=>(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:s.render?s.render(e[s.key],e):String(e[s.key]||"-")},String(s.key)))},s))})]})})]})}function c(e){let{status:s,variant:t="default"}=e;return(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat({default:"bg-gray-500/20 text-gray-400",success:"bg-green-500/20 text-green-400",warning:"bg-yellow-500/20 text-yellow-400",danger:"bg-red-500/20 text-red-400",info:"bg-blue-500/20 text-blue-400"}[t]),children:s})}function d(e){let{onClick:s,children:t,variant:r="default",size:l="sm"}=e;return(0,a.jsx)("button",{onClick:s,className:"rounded transition-colors ".concat({default:"text-gray-400 hover:text-white",primary:"text-green-400 hover:text-green-300",danger:"text-red-400 hover:text-red-300"}[r]," ").concat({sm:"p-1",md:"p-2"}[l]),children:t})}let o=i},88788:(e,s,t)=>{Promise.resolve().then(t.bind(t,37969))}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,411,6071,8441,5964,7358],()=>e(e.s=88788)),_N_E=e.O()}]);