(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4029],{20354:(e,t,s)=>{Promise.resolve().then(s.bind(s,56404))},48016:(e,t,s)=>{"use strict";s.d(t,{Qh:()=>o,UU:()=>c});var i=s(95155),a=s(68289),n=s(66766);let r={sm:{container:"h-8",text:"text-lg",icon:"w-6 h-6"},md:{container:"h-10",text:"text-xl",icon:"w-8 h-8"},lg:{container:"h-12",text:"text-2xl",icon:"w-10 h-10"},xl:{container:"h-16",text:"text-3xl",icon:"w-12 h-12"}},l={light:"text-white",dark:"text-gray-900",gradient:"bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"};function c(e){let{size:t="md",variant:s="gradient",showIcon:c=!0,className:o=""}=e,d=r[t],x=l[s];return(0,i.jsxs)(a.P.div,{className:"flex items-center space-x-2 ".concat(d.container," ").concat(o),whileHover:{scale:1.05},transition:{type:"spring",stiffness:400,damping:10},children:[c&&(0,i.jsx)(a.P.div,{className:"".concat(d.icon," flex items-center justify-center relative"),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,i.jsx)(n.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(d.icon.split(" ")[0].replace("w-","")),height:4*parseInt(d.icon.split(" ")[1].replace("h-","")),className:"".concat(d.icon," object-contain"),priority:!0})}),(0,i.jsxs)("div",{className:"flex flex-col",children:[(0,i.jsxs)(a.P.h1,{className:"font-inter font-bold leading-tight ".concat(d.text," ").concat(x," relative"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},style:{letterSpacing:"0.02em",textShadow:"0 0 20px rgba(255, 255, 255, 0.1)"},children:[(0,i.jsx)("span",{className:"text-white relative",style:{textShadow:"0 0 15px rgba(255, 255, 255, 0.2)",fontWeight:"700"},children:"Better"}),(0,i.jsx)("span",{className:"text-green-400 ml-1 relative",style:{textShadow:"0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2)",fontWeight:"800",letterSpacing:"0.05em"},children:"Interest"})]}),"lg"===t||"xl"===t?(0,i.jsx)(a.P.p,{className:"text-xs text-gray-400 -mt-1",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.2},children:"Smart Savings Platform"}):null]})]})}function o(e){let{size:t="md",className:s=""}=e,l=r[t];return(0,i.jsx)(a.P.div,{className:"".concat(l.icon," flex items-center justify-center ").concat(s),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,i.jsx)(n.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(l.icon.split(" ")[0].replace("w-","")),height:4*parseInt(l.icon.split(" ")[1].replace("h-","")),className:"".concat(l.icon," object-contain"),priority:!0})})}},56404:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var i=s(95155),a=s(68289),n=s(6874),r=s.n(n),l=s(10351),c=s(48016);function o(){return(0,i.jsx)("div",{className:"min-h-screen bg-black text-white p-6",children:(0,i.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)(c.UU,{size:"lg",variant:"gradient",showIcon:!0}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-2xl font-bold",children:"Savings Platform Test"}),(0,i.jsx)("p",{className:"text-gray-400",children:"Test the savings functionality"})]})]}),(0,i.jsxs)(r(),{href:"/",className:"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors",children:[(0,i.jsx)(l.kRp,{}),(0,i.jsx)("span",{children:"Back to Home"})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,i.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,i.jsx)(l.x_j,{className:"text-green-400 text-3xl mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"User Dashboard"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Test the user dashboard with savings overview and quick actions"}),(0,i.jsx)(r(),{href:"/dashboard",className:"inline-block bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg text-sm transition-colors",children:"View Dashboard"})]}),(0,i.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,i.jsx)(l.z8N,{className:"text-blue-400 text-3xl mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Savings Plans"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Create and manage individual savings plans with goals and targets"}),(0,i.jsx)(r(),{href:"/dashboard/savings-plans",className:"inline-block bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg text-sm transition-colors",children:"Manage Plans"})]}),(0,i.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,i.jsx)(l.cfS,{className:"text-purple-400 text-3xl mb-4"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Authentication"}),(0,i.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Test user signup, login, and admin authentication"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(r(),{href:"/signup",className:"block bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg text-sm transition-colors text-center",children:"User Signup"}),(0,i.jsx)(r(),{href:"/auth/login",className:"block bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg text-sm transition-colors text-center",children:"User Login"}),(0,i.jsx)(r(),{href:"/admin/login",className:"block bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-sm transition-colors text-center",children:"Admin Login"})]})]})]}),(0,i.jsxs)("div",{className:"mt-12 bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,i.jsx)("h2",{className:"text-xl font-bold mb-4",children:"Implementation Status"}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-green-400 font-semibold mb-2",children:"✅ Completed Features"}),(0,i.jsxs)("ul",{className:"space-y-1 text-sm text-gray-300",children:[(0,i.jsx)("li",{children:"• JWT-based Authentication System"}),(0,i.jsx)("li",{children:"• User Signup with Validation"}),(0,i.jsx)("li",{children:"• Admin Login Interface"}),(0,i.jsx)("li",{children:"• Private Route Protection"}),(0,i.jsx)("li",{children:"• User Dashboard with Stats"}),(0,i.jsx)("li",{children:"• Savings Plans Management"}),(0,i.jsx)("li",{children:"• Savings Plan Creation Form"}),(0,i.jsx)("li",{children:"• Savings Goal Modal"}),(0,i.jsx)("li",{children:"• Target Savings Calculator"}),(0,i.jsx)("li",{children:"• User Navigation & Layout"}),(0,i.jsx)("li",{children:"• Interest Calculation Service"}),(0,i.jsx)("li",{children:"• Responsive Design"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-yellow-400 font-semibold mb-2",children:"\uD83D\uDEA7 Next Features"}),(0,i.jsxs)("ul",{className:"space-y-1 text-sm text-gray-300",children:[(0,i.jsx)("li",{children:"• Group Savings System"}),(0,i.jsx)("li",{children:"• Rotational Savings"}),(0,i.jsx)("li",{children:"• Payment Integration"}),(0,i.jsx)("li",{children:"• KYC Verification"}),(0,i.jsx)("li",{children:"• Analytics Dashboard"}),(0,i.jsx)("li",{children:"• Notification System"}),(0,i.jsx)("li",{children:"• Admin Management"}),(0,i.jsx)("li",{children:"• Settings & Profile"}),(0,i.jsx)("li",{children:"• Transaction History"}),(0,i.jsx)("li",{children:"• Real-time Updates"})]})]})]})]}),(0,i.jsxs)("div",{className:"mt-8 bg-blue-500/10 border border-blue-500/20 rounded-lg p-6",children:[(0,i.jsx)("h3",{className:"text-blue-400 font-semibold mb-2",children:"Technical Implementation"}),(0,i.jsxs)("div",{className:"text-sm text-gray-300 space-y-2",children:[(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Frontend:"})," Next.js 14 with TypeScript, Framer Motion animations, Tailwind CSS styling"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Authentication:"})," JWT tokens with refresh mechanism, role-based access control"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"State Management:"})," React Context API for global auth state"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"Forms:"})," Comprehensive validation with real-time error handling"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"API Integration:"})," Service layer architecture with error handling"]}),(0,i.jsxs)("p",{children:[(0,i.jsx)("strong",{children:"UI/UX:"})," Professional design with Koja Save branding and responsive layout"]})]})]})]})})}}},e=>{e.O(0,[844,5236,6874,6766,8441,5964,7358],()=>e(e.s=20354)),_N_E=e.O()}]);