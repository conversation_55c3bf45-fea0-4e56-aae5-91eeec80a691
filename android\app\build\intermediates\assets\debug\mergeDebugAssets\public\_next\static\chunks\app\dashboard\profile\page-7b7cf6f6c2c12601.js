(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[399],{14195:(e,t,s)=>{Promise.resolve().then(s.bind(s,56916))},17703:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,Z:()=>n});var r=s(95155);s(12115);var a=s(68289);function n(e){let{children:t,className:s="",hover:n=!1,onClick:l,variant:i="default"}=e,c="\n    rounded-xl border transition-all duration-300\n    ".concat(n?"hover:shadow-lg hover:scale-[1.02] cursor-pointer":"","\n    ").concat(l?"cursor-pointer":"","\n  "),o="".concat(c," ").concat({default:"bg-gray-800 border-gray-700",gradient:"bg-gradient-to-br from-gray-800 to-gray-900 border-gray-700",glass:"bg-gray-800/50 backdrop-blur-lg border-gray-700/50"}[i]," ").concat(s);return n||l?(0,r.jsx)(a.P.div,{className:o,onClick:l,whileHover:n?{scale:1.02}:void 0,whileTap:l?{scale:.98}:void 0,transition:{type:"spring",stiffness:400,damping:10},children:t}):(0,r.jsx)("div",{className:o,children:t})}let l=n},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},52814:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,E:()=>a});var r=s(95155);function a(e){let{children:t,variant:s="default",size:a="md",className:n=""}=e,l="\n    ".concat("\n    inline-flex items-center justify-center font-medium rounded-full\n    transition-all duration-200\n  "," \n    ").concat({default:"bg-gray-700 text-gray-300 border border-gray-600",success:"bg-green-500/20 text-green-400 border border-green-500/30",warning:"bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",error:"bg-red-500/20 text-red-400 border border-red-500/30",info:"bg-blue-500/20 text-blue-400 border border-blue-500/30"}[s]," \n    ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[a]," \n    ").concat(n,"\n  ");return(0,r.jsx)("span",{className:l,children:t})}s(12115);let n=a},56916:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>h});var r=s(95155),a=s(12115),n=s(10351),l=s(11846),i=s(24630),c=s(40108),o=s(17703),d=s(93915),m=s(52814),x=s(66440),u=s(64198);function h(){let[e,t]=(0,a.useState)(null),[s,h]=(0,a.useState)([]),[p,g]=(0,a.useState)(!0),[f,b]=(0,a.useState)(!1),[y,j]=(0,a.useState)(!1),[N,v]=(0,a.useState)({}),[w,k]=(0,a.useState)({bankName:"",accountNumber:"",accountName:"",bankCode:""});(0,a.useEffect)(()=>{F(),C()},[]);let F=async()=>{try{let e=await i.Dv.getCurrentUserProfile();t(e),v({firstName:e.firstName,lastName:e.lastName,phoneNumber:e.phoneNumber,dateOfBirth:e.dateOfBirth,address:e.address,city:e.city,state:e.state,country:e.country})}catch(e){u.oR.error("Failed to load profile")}finally{g(!1)}},C=async()=>{try{let e=await i.Dv.getBankAccounts();h(e)}catch(e){console.error("Failed to load bank accounts:",e)}},E=async()=>{try{let e=await i.Dv.updateCurrentUserProfile(N);t(e),b(!1),u.oR.success("Profile updated successfully")}catch(e){u.oR.error("Failed to update profile")}},A=async()=>{try{await i.Dv.addBankAccount(w),k({bankName:"",accountNumber:"",accountName:"",bankCode:""}),j(!1),C(),u.oR.success("Bank account added successfully")}catch(e){u.oR.error("Failed to add bank account")}},B=async e=>{try{await i.Dv.deleteBankAccount(e),C(),u.oR.success("Bank account removed successfully")}catch(e){u.oR.error("Failed to remove bank account")}},P=async e=>{try{await i.Dv.setDefaultBankAccount(e),C(),u.oR.success("Default bank account updated")}catch(e){u.oR.error("Failed to update default account")}};return p?(0,r.jsx)(l.A,{title:"Profile",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})})}):e?(0,r.jsxs)(l.A,{title:"Profile",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Profile"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Manage your account information"})]}),f?(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(c.rp,{onClick:()=>{b(!1),v({firstName:e.firstName,lastName:e.lastName,phoneNumber:e.phoneNumber,dateOfBirth:e.dateOfBirth,address:e.address,city:e.city,state:e.state,country:e.country})},children:[(0,r.jsx)(n.yGN,{className:"mr-2"}),"Cancel"]}),(0,r.jsxs)(c.jn,{onClick:E,className:"bg-green-600 hover:bg-green-700",children:[(0,r.jsx)(n.Bc_,{className:"mr-2"}),"Save Changes"]})]}):(0,r.jsxs)(c.jn,{onClick:()=>b(!0),className:"bg-green-600 hover:bg-green-700",children:[(0,r.jsx)(n.Pj4,{className:"mr-2"}),"Edit Profile"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsxs)("div",{className:"relative inline-block",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:e.profileImage?(0,r.jsx)("img",{src:e.profileImage,alt:"Profile",className:"w-24 h-24 rounded-full object-cover"}):i.Dv.getInitials(e.firstName,e.lastName)}),(0,r.jsx)("button",{className:"absolute bottom-0 right-0 bg-green-600 hover:bg-green-700 rounded-full p-2 text-white",children:(0,r.jsx)(n.PoE,{className:"w-4 h-4"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"text-xl font-semibold text-white",children:[e.firstName," ",e.lastName]}),(0,r.jsx)("p",{className:"text-gray-400",children:e.email})]}),(0,r.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,r.jsx)(m.E,{variant:e.isVerified?"success":"warning",children:e.isVerified?"Verified":"Unverified"}),(0,r.jsxs)(m.E,{variant:(e=>{switch(e){case"APPROVED":return"success";case"PENDING":return"warning";case"REJECTED":return"error";default:return"default"}})(e.kycStatus),children:["KYC ",e.kycStatus]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 pt-4 border-t border-gray-700",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Balance"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-white",children:i.Dv.formatCurrency(e.balance)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Savings"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-white",children:i.Dv.formatCurrency(e.totalSavings)})]})]})]})}),(0,r.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6 lg:col-span-2",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,r.jsx)(n.JXP,{className:"mr-2"}),"Personal Information"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"First Name"}),f?(0,r.jsx)(d.p,{value:N.firstName||"",onChange:e=>v({...N,firstName:e.target.value})}):(0,r.jsx)("p",{className:"text-white",children:e.firstName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Last Name"}),f?(0,r.jsx)(d.p,{value:N.lastName||"",onChange:e=>v({...N,lastName:e.target.value})}):(0,r.jsx)("p",{className:"text-white",children:e.lastName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Email"}),(0,r.jsxs)("p",{className:"text-white flex items-center",children:[(0,r.jsx)(n.pHD,{className:"mr-2 text-gray-400"}),e.email]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Phone Number"}),f?(0,r.jsx)(d.p,{value:N.phoneNumber||"",onChange:e=>v({...N,phoneNumber:e.target.value})}):(0,r.jsxs)("p",{className:"text-white flex items-center",children:[(0,r.jsx)(n.QFc,{className:"mr-2 text-gray-400"}),e.phoneNumber||"Not provided"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Date of Birth"}),f?(0,r.jsx)(d.p,{type:"date",value:N.dateOfBirth||"",onChange:e=>v({...N,dateOfBirth:e.target.value})}):(0,r.jsxs)("p",{className:"text-white flex items-center",children:[(0,r.jsx)(n.wIk,{className:"mr-2 text-gray-400"}),e.dateOfBirth||"Not provided"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Address"}),f?(0,r.jsx)(d.p,{value:N.address||"",onChange:e=>v({...N,address:e.target.value})}):(0,r.jsxs)("p",{className:"text-white flex items-center",children:[(0,r.jsx)(n.HzC,{className:"mr-2 text-gray-400"}),e.address||"Not provided"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"City"}),f?(0,r.jsx)(d.p,{value:N.city||"",onChange:e=>v({...N,city:e.target.value})}):(0,r.jsx)("p",{className:"text-white",children:e.city||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"State"}),f?(0,r.jsx)(d.p,{value:N.state||"",onChange:e=>v({...N,state:e.target.value})}):(0,r.jsx)("p",{className:"text-white",children:e.state||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-1",children:"Country"}),f?(0,r.jsx)(d.p,{value:N.country||"",onChange:e=>v({...N,country:e.target.value})}):(0,r.jsx)("p",{className:"text-white",children:e.country||"Not provided"})]})]})]})]}),(0,r.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-semibold text-white flex items-center",children:[(0,r.jsx)(n.lZI,{className:"mr-2"}),"Bank Accounts"]}),(0,r.jsxs)(c.jn,{onClick:()=>j(!0),className:"bg-green-600 hover:bg-green-700",children:[(0,r.jsx)(n.GGD,{className:"mr-2"}),"Add Account"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[s.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-700 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(n.lZI,{className:"text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-semibold text-white",children:e.bankName}),(0,r.jsx)("p",{className:"text-gray-400",children:e.accountNumber}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:e.accountName})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.isDefault&&(0,r.jsx)(m.E,{variant:"success",children:"Default"}),e.isVerified?(0,r.jsx)(m.E,{variant:"success",children:"Verified"}):(0,r.jsx)(m.E,{variant:"warning",children:"Pending"}),(0,r.jsxs)("div",{className:"flex space-x-1",children:[!e.isDefault&&(0,r.jsx)(c.rp,{size:"sm",onClick:()=>P(e.id),children:"Set Default"}),(0,r.jsx)(c.rp,{size:"sm",onClick:()=>B(e.id),className:"text-red-400 hover:text-red-300",children:(0,r.jsx)(n.IXo,{})})]})]})]},e.id)),0===s.length&&(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)(n.lZI,{className:"mx-auto text-4xl text-gray-600 mb-2"}),(0,r.jsx)("p",{className:"text-gray-400",children:"No bank accounts added yet"})]})]})]})]}),(0,r.jsx)(x.aF,{isOpen:y,onClose:()=>j(!1),title:"Add Bank Account",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(d.p,{label:"Bank Name",value:w.bankName,onChange:e=>k({...w,bankName:e.target.value}),placeholder:"e.g., First Bank"}),(0,r.jsx)(d.p,{label:"Account Number",value:w.accountNumber,onChange:e=>k({...w,accountNumber:e.target.value}),placeholder:"**********"}),(0,r.jsx)(d.p,{label:"Account Name",value:w.accountName,onChange:e=>k({...w,accountName:e.target.value}),placeholder:"John Doe"}),(0,r.jsx)(d.p,{label:"Bank Code",value:w.bankCode,onChange:e=>k({...w,bankCode:e.target.value}),placeholder:"011"}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,r.jsx)(c.rp,{onClick:()=>j(!1),children:"Cancel"}),(0,r.jsx)(c.jn,{onClick:A,className:"bg-green-600 hover:bg-green-700",children:"Add Account"})]})]})})]}):(0,r.jsx)(l.A,{title:"Profile",children:(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-400",children:"Failed to load profile"})})})}},60760:(e,t,s)=>{"use strict";s.d(t,{N:()=>b});var r=s(95155),a=s(12115),n=s(90869),l=s(82885),i=s(97494),c=s(80845),o=s(27351),d=s(51508);class m extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,s=(0,o.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=s-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function x(e){let{children:t,isPresent:s,anchorX:n,root:l}=e,i=(0,a.useId)(),c=(0,a.useRef)(null),o=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:x}=(0,a.useContext)(d.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:a,right:d}=o.current;if(s||!c.current||!e||!t)return;c.current.dataset.motionPopId=i;let m=document.createElement("style");x&&(m.nonce=x);let u=null!=l?l:document.head;return u.appendChild(m),m.sheet&&m.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===n?"left: ".concat(a):"right: ".concat(d),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{u.removeChild(m),u.contains(m)&&u.removeChild(m)}},[s]),(0,r.jsx)(m,{isPresent:s,childRef:c,sizeRef:o,children:a.cloneElement(t,{ref:c})})}let u=e=>{let{children:t,initial:s,isPresent:n,onExitComplete:i,custom:o,presenceAffectsLayout:d,mode:m,anchorX:u,root:p}=e,g=(0,l.M)(h),f=(0,a.useId)(),b=!0,y=(0,a.useMemo)(()=>(b=!1,{id:f,initial:s,isPresent:n,custom:o,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;i&&i()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[n,g,i]);return d&&b&&(y={...y}),(0,a.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[n]),a.useEffect(()=>{n||g.size||!i||i()},[n]),"popLayout"===m&&(t=(0,r.jsx)(x,{isPresent:n,anchorX:u,root:p,children:t})),(0,r.jsx)(c.t.Provider,{value:y,children:t})};function h(){return new Map}var p=s(32082);let g=e=>e.key||"";function f(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let b=e=>{let{children:t,custom:s,initial:c=!0,onExitComplete:o,presenceAffectsLayout:d=!0,mode:m="sync",propagate:x=!1,anchorX:h="left",root:b}=e,[y,j]=(0,p.xQ)(x),N=(0,a.useMemo)(()=>f(t),[t]),v=x&&!y?[]:N.map(g),w=(0,a.useRef)(!0),k=(0,a.useRef)(N),F=(0,l.M)(()=>new Map),[C,E]=(0,a.useState)(N),[A,B]=(0,a.useState)(N);(0,i.E)(()=>{w.current=!1,k.current=N;for(let e=0;e<A.length;e++){let t=g(A[e]);v.includes(t)?F.delete(t):!0!==F.get(t)&&F.set(t,!1)}},[A,v.length,v.join("-")]);let P=[];if(N!==C){let e=[...N];for(let t=0;t<A.length;t++){let s=A[t],r=g(s);v.includes(r)||(e.splice(t,0,s),P.push(s))}return"wait"===m&&P.length&&(e=P),B(f(e)),E(N),null}let{forceRender:D}=(0,a.useContext)(n.L);return(0,r.jsx)(r.Fragment,{children:A.map(e=>{let t=g(e),a=(!x||!!y)&&(N===A||v.includes(t));return(0,r.jsx)(u,{isPresent:a,initial:(!w.current||!!c)&&void 0,custom:s,presenceAffectsLayout:d,mode:m,root:b,onExitComplete:a?void 0:()=>{if(!F.has(t))return;F.set(t,!0);let e=!0;F.forEach(t=>{t||(e=!1)}),e&&(null==D||D(),B(k.current),x&&(null==j||j()),o&&o())},anchorX:h,children:e},t)})})}},64198:(e,t,s)=>{"use strict";s.d(t,{CustomToaster:()=>c,P0:()=>i,oR:()=>n.Ay});var r=s(95155),a=s(68289),n=s(13568),l=s(10351);let i={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,t)=>n.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function c(){return(0,r.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,r.jsx)(n.bv,{toast:e,children:t=>{let{icon:s,message:i}=t;return(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:s}),(0,r.jsx)("div",{className:"flex-1",children:i}),"loading"!==e.type&&(0,r.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,r.jsx)(l.yGN,{className:"w-4 h-4"})})]})}})})}},66440:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>d,aF:()=>c,k5:()=>o});var r=s(95155),a=s(60760),n=s(68289);s(12115);var l=s(10351);let i={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"};function c(e){let{isOpen:t,onClose:s,title:c,children:o,size:d="md",showCloseButton:m=!0}=e;return(0,r.jsx)(a.N,{children:t&&(0,r.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,r.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:s}),(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"\n                relative w-full ".concat(i[d]," \n                bg-gray-900 border border-gray-800 rounded-lg shadow-xl\n              "),onClick:e=>e.stopPropagation(),children:[(c||m)&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800",children:[c&&(0,r.jsx)("h2",{className:"text-xl font-semibold text-white",children:c}),m&&(0,r.jsx)("button",{onClick:s,className:"p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,r.jsx)(l.yGN,{className:"w-5 h-5 text-gray-400"})})]}),(0,r.jsx)("div",{className:"p-6",children:o})]})})]})})}function o(e){let{isOpen:t,onClose:s,title:a,children:n,onSubmit:l,submitText:i="Submit",isLoading:o=!1,size:d="md"}=e;return(0,r.jsx)(c,{isOpen:t,onClose:s,title:a,size:d,children:(0,r.jsxs)("form",{onSubmit:l,className:"space-y-6",children:[n,(0,r.jsxs)("div",{className:"flex space-x-3 justify-end pt-4 border-t border-gray-800",children:[(0,r.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-300 hover:text-white transition-colors",disabled:o,children:"Cancel"}),(0,r.jsxs)("button",{type:"submit",disabled:o,className:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2",children:[o&&(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:i})]})]})]})})}let d=c},93915:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,p:()=>a});var r=s(95155);let a=(0,s(12115).forwardRef)((e,t)=>{let{label:s,error:a,helperText:n,leftIcon:l,rightIcon:i,variant:c="default",className:o="",...d}=e,m="\n    w-full px-4 py-3 rounded-lg transition-all duration-200\n    focus:outline-none focus:ring-2 focus:ring-green-500/50\n    disabled:opacity-50 disabled:cursor-not-allowed\n    ".concat(l?"pl-12":"","\n    ").concat(i?"pr-12":"","\n  "),x="".concat(m," ").concat({default:"\n      bg-gray-800 border border-gray-700 text-white\n      placeholder-gray-400 hover:border-gray-600\n      focus:border-green-500\n    ",filled:"\n      bg-gray-700 border-0 text-white\n      placeholder-gray-400 hover:bg-gray-600\n    ",outline:"\n      bg-transparent border-2 border-gray-600 text-white\n      placeholder-gray-400 hover:border-gray-500\n      focus:border-green-500\n    "}[c]," ").concat(o," ").concat(a?"border-red-500 focus:border-red-500 focus:ring-red-500/50":"");return(0,r.jsxs)("div",{className:"w-full",children:[s&&(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:s}),(0,r.jsxs)("div",{className:"relative",children:[l&&(0,r.jsx)("div",{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:l}),(0,r.jsx)("input",{ref:t,className:x,...d}),i&&(0,r.jsx)("div",{className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400",children:i})]}),a&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-400",children:a}),n&&!a&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:n})]})});a.displayName="Input";let n=a}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,1846,8441,5964,7358],()=>e(e.s=14195)),_N_E=e.O()}]);