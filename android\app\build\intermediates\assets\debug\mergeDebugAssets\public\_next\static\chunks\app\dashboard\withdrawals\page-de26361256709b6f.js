(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8654],{19958:(e,t,a)=>{"use strict";a.d(t,{YO:()=>u,uk:()=>d});var n=a(95155),s=a(12115),r=a(8619),i=a(37602),l=a(58829),c=a(68289),o=a(57740);function d(e){let{children:t,className:a="",intensity:d="medium",glowEffect:u=!0,hoverScale:m=!0,borderGradient:h=!1,elevation:x=2,onClick:g}=e,{theme:p}=(0,o.DP)(),f=(0,o.Yx)(p),b=(0,s.useRef)(null),y=(0,r.d)(0),w=(0,r.d)(0),N=(0,i.z)(y),j=(0,i.z)(w),v=(0,l.G)(j,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),k=(0,l.G)(N,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),C=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a={1:"light"===p?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===p?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===p?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)",4:"light"===p?"0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)":"0 14px 28px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.8)",5:"light"===p?"0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)":"0 19px 38px rgba(0, 0, 0, 0.7), 0 15px 12px rgba(0, 0, 0, 0.9)"};return a[t?Math.min(e+2,5):e]||a[2]},A="\n    relative rounded-xl overflow-hidden transition-all duration-300 group\n    ".concat(f.bg.card,"\n    ").concat(f.border.primary,"\n    ").concat(h?"border-2 border-transparent bg-gradient-to-r from-green-400/20 to-blue-400/20 p-[1px]":"border","\n    ").concat(a,"\n  "),T=(0,n.jsxs)(c.P.div,{ref:b,className:A,style:{rotateY:k,rotateX:v,transformStyle:"preserve-3d",boxShadow:C(x)},onMouseMove:e=>{if(!b.current)return;let t=b.current.getBoundingClientRect(),a=t.width,n=t.height,s=(e.clientX-t.left)/a-.5,r=(e.clientY-t.top)/n-.5;y.set(s),w.set(r)},onMouseLeave:()=>{y.set(0),w.set(0)},whileHover:m?{scale:1.02,boxShadow:C(x,!0),transition:{duration:.2,ease:"easeOut"}}:{},transition:{type:"spring",stiffness:300,damping:30},onClick:g,children:[u&&(0,n.jsx)(c.P.div,{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl blur-xl",initial:{scale:.8},whileHover:{scale:1.1},transition:{duration:.3}}),(0,n.jsx)(c.P.div,{className:"absolute inset-0 bg-green-400/10 opacity-0 group-hover:opacity-20 rounded-xl",initial:{scale:0},whileHover:{scale:1},transition:{duration:.4,ease:"easeOut"}}),(0,n.jsx)("div",{className:"relative z-10 ".concat(h?"".concat(f.bg.card," rounded-xl"):""),children:t}),(0,n.jsx)(c.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ".concat("dark"===p?"via-white/5":"via-white/10"),initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.6,ease:"easeInOut"}})]});return(0,n.jsx)("div",{className:"group",children:T})}function u(e){let{title:t,value:a,subtitle:s,icon:r,color:i="green",className:l=""}=e,{theme:c}=(0,o.DP)(),u=(0,o.Yx)(c);return(0,n.jsx)(d,{className:"p-6 ".concat(l),glowEffect:!0,borderGradient:!0,children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"text-sm font-medium ".concat(u.text.secondary),children:t}),(0,n.jsx)("p",{className:"text-2xl font-bold ".concat(u.text.primary," mt-1"),children:a}),s&&(0,n.jsx)("p",{className:"text-xs ".concat(u.text.tertiary," mt-1"),children:s})]}),r&&(0,n.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat({green:"from-green-400 to-green-600",blue:"from-blue-400 to-blue-600",purple:"from-purple-400 to-purple-600",yellow:"from-yellow-400 to-yellow-600"}[i]," flex items-center justify-center"),children:(0,n.jsx)(r,{className:"w-6 h-6 text-white"})})]})})}},43851:(e,t,a)=>{"use strict";a.d(t,{K:()=>r});var n=a(49509);class s{getHeaders(){return{Authorization:"Bearer ".concat(this.config.secretKey),"Content-Type":"application/json"}}async makeRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",a=arguments.length>2?arguments[2]:void 0,n="".concat(this.config.baseUrl).concat(e),s={method:t,headers:this.getHeaders()};a&&("POST"===t||"PUT"===t)&&(s.body=JSON.stringify(a));try{let e=await fetch(n,s),t=await e.json();if(!e.ok)throw Error(t.message||"HTTP ".concat(e.status,": ").concat(e.statusText));return t}catch(a){throw console.error("Paystack API Error (".concat(t," ").concat(e,"):"),a),a}}async getBankList(){return this.makeRequest("/bank")}async verifyAccountNumber(e,t){let a=await fetch("".concat("http://localhost:8080/api","/api/paystack/resolve-account"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({account_number:e,bank_code:t})});return await a.json()}async createTransferRecipient(e){return this.makeRequest("/transferrecipient","POST",e)}async getTransferRecipient(e){return this.makeRequest("/transferrecipient/".concat(e))}async updateTransferRecipient(e,t){return this.makeRequest("/transferrecipient/".concat(e),"PUT",t)}async deleteTransferRecipient(e){return this.makeRequest("/transferrecipient/".concat(e),"DELETE")}async initiateTransfer(e){return this.makeRequest("/transfer","POST",e)}async getTransfer(e){return this.makeRequest("/transfer/".concat(e))}async verifyTransfer(e){return this.makeRequest("/transfer/verify/".concat(e))}async getBalance(){return this.makeRequest("/balance")}convertToKobo(e){return Math.round(100*e)}convertFromKobo(e){return e/100}generateReference(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"AUTO",t=Date.now(),a=Math.random().toString(36).substring(2,8).toUpperCase();return"".concat(e,"-").concat(t,"-").concat(a)}validateBankAccount(e){let t=[];return e.accountNumber&&/^\d{10}$/.test(e.accountNumber)||t.push({code:"INVALID_ACCOUNT_NUMBER",message:"Account number must be exactly 10 digits"}),(!e.bankCode||e.bankCode.length<3)&&t.push({code:"INVALID_BANK_CODE",message:"Bank code is required"}),(!e.accountName||e.accountName.trim().length<2)&&t.push({code:"INVALID_ACCOUNT_NAME",message:"Account name must be at least 2 characters"}),{isValid:0===t.length,errors:t}}validateTransferAmount(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"NGN",a=[],n="NGN"===t?100:1,s="NGN"===t?1e7:1e5;return e<n&&a.push({code:"AMOUNT_TOO_LOW",message:"Minimum transfer amount is ".concat("NGN"===t?"₦":"$").concat(n)}),e>s&&a.push({code:"AMOUNT_TOO_HIGH",message:"Maximum transfer amount is ".concat("NGN"===t?"₦":"$").concat(s.toLocaleString())}),{isValid:0===a.length,errors:a}}handlePaystackError(e){var t,a;return(null==(a=e.response)||null==(t=a.data)?void 0:t.message)?{code:"PAYSTACK_API_ERROR",message:e.response.data.message,details:e.response.data}:e.message?{code:"PAYSTACK_ERROR",message:e.message}:{code:"UNKNOWN_ERROR",message:"An unknown error occurred while processing the request"}}verifyWebhookSignature(e,t){return a(8777).createHmac("sha512",this.config.secretKey).update(e,"utf8").digest("hex")===t}constructor(){this.config={publicKey:"pk_test_44f6dbb6159b73b84fff2fc441dcdd997e15c10d",secretKey:n.env.PAYSTACK_SECRET_KEY||"",baseUrl:"https://api.paystack.co"}}}let r=new s},57306:(e,t,a)=>{Promise.resolve().then(a.bind(a,83532))},83532:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var n=a(95155),s=a(12115),r=a(60760),i=a(68289),l=a(10351),c=a(11846),o=a(13741),d=a(19958),u=a(43851);class m{getAuthHeaders(){let e=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}}async makeRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"GET",a=arguments.length>2?arguments[2]:void 0,n=await fetch("".concat("http://localhost:8080/api").concat(e),{method:t,headers:this.getAuthHeaders(),...a&&{body:JSON.stringify(a)}});if(!n.ok)throw Error((await n.json()).message||"HTTP ".concat(n.status,": ").concat(n.statusText));return n.json()}async getWithdrawalAccounts(){return this.makeRequest("/withdrawal-accounts")}async createWithdrawalAccount(e){if("bank_account"===e.type&&e.bankDetails){let t=u.K.validateBankAccount(e.bankDetails);if(!t.isValid)throw Error(t.errors[0].message);try{let t=await u.K.verifyAccountNumber(e.bankDetails.accountNumber,e.bankDetails.bankCode);e.bankDetails.accountName=t.data.account_name}catch(e){throw Error("Failed to verify bank account details")}}return this.makeRequest("/withdrawal-accounts","POST",e)}async updateWithdrawalAccount(e,t){return this.makeRequest("/withdrawal-accounts/".concat(e),"PUT",t)}async deleteWithdrawalAccount(e){await this.makeRequest("/withdrawal-accounts/".concat(e),"DELETE")}async setDefaultAccount(e){await this.makeRequest("/withdrawal-accounts/".concat(e,"/set-default"),"POST")}async verifyWithdrawalAccount(e){return this.makeRequest("/withdrawal-accounts/".concat(e,"/verify"),"POST")}async getAutomaticRules(){return this.makeRequest("/automatic-withdrawal-rules")}async createAutomaticRule(e){let t=this.validateAutomaticRule(e);if(!t.isValid)throw Error(t.errors[0].message);return this.makeRequest("/automatic-withdrawal-rules","POST",e)}async updateAutomaticRule(e,t){return this.makeRequest("/automatic-withdrawal-rules/".concat(e),"PUT",t)}async deleteAutomaticRule(e){await this.makeRequest("/automatic-withdrawal-rules/".concat(e),"DELETE")}async toggleRuleStatus(e,t){return this.makeRequest("/automatic-withdrawal-rules/".concat(e,"/toggle"),"POST",{isActive:t})}async triggerRule(e){return this.makeRequest("/automatic-withdrawal-rules/".concat(e,"/trigger"),"POST")}async createInstantWithdrawal(e){let t=u.K.validateTransferAmount(e.amount);if(!t.isValid)throw Error(t.errors[0].message);return this.makeRequest("/withdrawals/instant","POST",e)}async getWithdrawalHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:20,a=arguments.length>2?arguments[2]:void 0,n=arguments.length>3?arguments[3]:void 0,s=new URLSearchParams({page:e.toString(),limit:t.toString(),...a&&{status:a},...n&&{type:n}});return this.makeRequest("/withdrawals/history?".concat(s))}async getWithdrawalById(e){return this.makeRequest("/withdrawals/".concat(e))}async cancelWithdrawal(e){return this.makeRequest("/withdrawals/".concat(e,"/cancel"),"POST")}async getDashboardData(){return this.makeRequest("/withdrawals/dashboard")}async getProcessingStatus(){return this.makeRequest("/withdrawals/processing-status")}async triggerProcessing(){return this.makeRequest("/withdrawals/trigger-processing","POST")}validateAutomaticRule(e){let t=[];return(!e.name||e.name.trim().length<3)&&t.push({code:"INVALID_NAME",message:"Rule name must be at least 3 characters long"}),this.validateTriggerConditions(e.triggerType,e.triggerConditions)||t.push({code:"INVALID_TRIGGER_CONDITIONS",message:"Invalid trigger conditions for the selected trigger type"}),this.validateWithdrawalConfig(e.withdrawalConfig)||t.push({code:"INVALID_WITHDRAWAL_CONFIG",message:"Invalid withdrawal configuration"}),{isValid:0===t.length,errors:t}}validateTriggerConditions(e,t){switch(e){case"balance_threshold":return t.balanceThreshold&&t.balanceThreshold>0;case"date_based":return t.dayOfMonth&&t.dayOfMonth>=1&&t.dayOfMonth<=31||t.dayOfWeek&&t.dayOfWeek>=0&&t.dayOfWeek<=6;case"interest_earned":return t.interestThreshold&&t.interestThreshold>0;case"goal_reached":return t.goalId||t.goalPercentage&&t.goalPercentage>0;default:return!1}}validateWithdrawalConfig(e){if(!e.withdrawalAccountId||!e.amountType)return!1;switch(e.amountType){case"fixed":return e.amount&&e.amount>0;case"percentage":return e.percentage&&e.percentage>0&&e.percentage<=100;case"excess":case"all":return!0;default:return!1}}calculateWithdrawalAmount(e,t,a,n){switch(e){case"fixed":return t.amount;case"percentage":let s=a*t.percentage/100;return t.keepMinimumBalance?Math.max(0,s-t.keepMinimumBalance):s;case"excess":return n?Math.max(0,a-n):0;case"all":return t.keepMinimumBalance?Math.max(0,a-t.keepMinimumBalance):a;default:return 0}}formatCurrency(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"NGN";return"".concat("NGN"===t?"₦":"$").concat(e.toLocaleString())}getNextExecutionTime(e){let t=new Date;if("date_based"===e.triggerType){let a=e.triggerConditions;if(a.dayOfMonth){let e=new Date(t.getFullYear(),t.getMonth()+1,a.dayOfMonth);if(a.time){let[t,n]=a.time.split(":").map(Number);e.setHours(t,n,0,0)}return e}}return null}}let h=new m;function x(e){let{onAccountCreated:t,onCancel:a,editingAccount:r}=e,[i,c]=(0,s.useState)("bank_account"),[m,x]=(0,s.useState)(!1),[g,p]=(0,s.useState)(!1),[f,b]=(0,s.useState)([]),[y,w]=(0,s.useState)(null),[N,j]=(0,s.useState)(null),[v,k]=(0,s.useState)({bankCode:"",accountNumber:"",accountName:"",accountType:"savings"}),[C,A]=(0,s.useState)({provider:"mtn",phoneNumber:"",accountName:""}),[T,R]=(0,s.useState)({currency:"btc",walletAddress:"",network:""});(0,s.useEffect)(()=>{P(),r&&S()},[r]);let P=async()=>{try{let e=await u.K.getBankList();b(e.data.map(e=>({id:e.id,name:e.name,code:e.code})))}catch(e){console.error("Failed to load banks:",e)}},S=()=>{r&&(c(r.type),"bank_account"===r.type&&r.bankDetails&&k({bankCode:r.bankDetails.bankCode,accountNumber:r.bankDetails.accountNumber,accountName:r.bankDetails.accountName,accountType:r.bankDetails.accountType}))},D=async()=>{if(!v.accountNumber||!v.bankCode)return void w("Please enter account number and select a bank");try{p(!0),w(null);let e=await u.K.verifyAccountNumber(v.accountNumber,v.bankCode);k(t=>({...t,accountName:e.data.account_name})),j("Account verified successfully!")}catch(e){w("Failed to verify account. Please check your details.")}finally{p(!1)}},I=async e=>{e.preventDefault(),x(!0),w(null);try{let e,a;switch(i){case"bank_account":if(!v.accountName)return void w("Please verify your bank account first");let n=f.find(e=>e.code===v.bankCode);e={type:"bank_account",bankDetails:{bankName:(null==n?void 0:n.name)||"",bankCode:v.bankCode,accountNumber:v.accountNumber,accountName:v.accountName,accountType:v.accountType}};break;case"mobile_money":e={type:"mobile_money",mobileMoneyDetails:{provider:C.provider,phoneNumber:C.phoneNumber,accountName:C.accountName}};break;case"crypto_wallet":e={type:"crypto_wallet",cryptoDetails:{currency:T.currency,walletAddress:T.walletAddress,network:T.network}};break;default:throw Error("Invalid account type")}a=r?await h.updateWithdrawalAccount(r.id,e):await h.createWithdrawalAccount(e),t(a)}catch(e){w(e.message||"Failed to save account")}finally{x(!1)}},W=[{type:"bank_account",icon:l.lZI,title:"Bank Account",description:"Nigerian bank account for instant transfers"},{type:"mobile_money",icon:l.PCV,title:"Mobile Money",description:"MTN, Airtel, Glo, or 9mobile wallet"},{type:"crypto_wallet",icon:l.z8N,title:"Crypto Wallet",description:"Bitcoin, Ethereum, or USDT wallet"}];return(0,n.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-3 font-inter",children:"Account Type"}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-3",children:W.map(e=>{let t=e.icon,a=i===e.type;return(0,n.jsx)(d.uk,{className:"p-4 cursor-pointer transition-all duration-300 ".concat(a?"ring-2 ring-brand bg-brand/5":"hover:bg-theme-secondary/50"),onClick:()=>c(e.type),elevation:a?2:1,children:(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"p-2 rounded-lg ".concat(a?"bg-brand text-white":"bg-theme-secondary text-theme-secondary"),children:(0,n.jsx)(t,{className:"w-5 h-5"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold text-theme font-inter",children:e.title}),(0,n.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:e.description})]}),a&&(0,n.jsx)(l.YrT,{className:"w-5 h-5 text-brand ml-auto"})]})},e.type)})})]}),"bank_account"===i&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Bank"}),(0,n.jsxs)("select",{value:v.bankCode,onChange:e=>k(t=>({...t,bankCode:e.target.value})),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",required:!0,children:[(0,n.jsx)("option",{value:"",children:"Select your bank"}),f.map(e=>(0,n.jsx)("option",{value:e.code,children:e.name},e.code))]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Account Number"}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)("input",{type:"text",value:v.accountNumber,onChange:e=>k(t=>({...t,accountNumber:e.target.value})),className:"flex-1 px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"Enter 10-digit account number",maxLength:10,required:!0}),(0,n.jsx)(o.$n,{type:"button",onClick:D,loading:g,disabled:!v.accountNumber||!v.bankCode,className:"font-inter",children:"Verify"})]})]}),v.accountName&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Account Name"}),(0,n.jsx)("div",{className:"px-3 py-3 border border-green-500 rounded-lg bg-green-500/10 text-green-400 font-inter",children:v.accountName})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Account Type"}),(0,n.jsxs)("select",{value:v.accountType,onChange:e=>k(t=>({...t,accountType:e.target.value})),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",required:!0,children:[(0,n.jsx)("option",{value:"savings",children:"Savings Account"}),(0,n.jsx)("option",{value:"current",children:"Current Account"})]})]})]}),"mobile_money"===i&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Provider"}),(0,n.jsxs)("select",{value:C.provider,onChange:e=>A(t=>({...t,provider:e.target.value})),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",required:!0,children:[(0,n.jsx)("option",{value:"mtn",children:"MTN"}),(0,n.jsx)("option",{value:"airtel",children:"Airtel"}),(0,n.jsx)("option",{value:"glo",children:"Glo"}),(0,n.jsx)("option",{value:"9mobile",children:"9mobile"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Phone Number"}),(0,n.jsx)("input",{type:"tel",value:C.phoneNumber,onChange:e=>A(t=>({...t,phoneNumber:e.target.value})),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"+234 ************",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Account Name"}),(0,n.jsx)("input",{type:"text",value:C.accountName,onChange:e=>A(t=>({...t,accountName:e.target.value})),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"Enter account holder name",required:!0})]})]}),"crypto_wallet"===i&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Currency"}),(0,n.jsxs)("select",{value:T.currency,onChange:e=>R(t=>({...t,currency:e.target.value})),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",required:!0,children:[(0,n.jsx)("option",{value:"btc",children:"Bitcoin (BTC)"}),(0,n.jsx)("option",{value:"eth",children:"Ethereum (ETH)"}),(0,n.jsx)("option",{value:"usdt",children:"Tether (USDT)"})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Wallet Address"}),(0,n.jsx)("input",{type:"text",value:T.walletAddress,onChange:e=>R(t=>({...t,walletAddress:e.target.value})),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"Enter wallet address",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Network (Optional)"}),(0,n.jsx)("input",{type:"text",value:T.network,onChange:e=>R(t=>({...t,network:e.target.value})),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"e.g., ERC-20, TRC-20"})]})]}),y&&(0,n.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg",children:[(0,n.jsx)(l.y3G,{className:"w-5 h-5 text-red-400"}),(0,n.jsx)("span",{className:"text-red-400 text-sm font-inter",children:y})]}),N&&(0,n.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-green-500/10 border border-green-500/20 rounded-lg",children:[(0,n.jsx)(l.YrT,{className:"w-5 h-5 text-green-400"}),(0,n.jsx)("span",{className:"text-green-400 text-sm font-inter",children:N})]}),(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsx)(o.$n,{type:"button",variant:"outline",onClick:a,className:"flex-1 font-inter",children:"Cancel"}),(0,n.jsx)(o.$n,{type:"submit",loading:m,className:"flex-1 font-inter",children:r?"Update Account":"Add Account"})]})]})}function g(e){let{onAccountSelect:t,selectedAccountId:a,showAddButton:c=!0}=e,[u,m]=(0,s.useState)([]),[g,p]=(0,s.useState)(!0),[f,b]=(0,s.useState)(!1),[y,w]=(0,s.useState)(null),[N,j]=(0,s.useState)(null);(0,s.useEffect)(()=>{v()},[]);let v=async()=>{try{p(!0);let e=await h.getWithdrawalAccounts();m(e)}catch(e){console.error("Failed to load withdrawal accounts:",e)}finally{p(!1)}},k=async e=>{try{await h.setDefaultAccount(e),m(t=>t.map(t=>({...t,isDefault:t.id===e})))}catch(e){console.error("Failed to set default account:",e)}},C=async e=>{try{j(e),await h.deleteWithdrawalAccount(e),m(t=>t.filter(t=>t.id!==e))}catch(e){console.error("Failed to delete account:",e)}finally{j(null)}};return g?(0,n.jsx)("div",{className:"space-y-4",children:[1,2,3].map(e=>(0,n.jsx)(d.uk,{className:"p-6 animate-pulse",children:(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"w-12 h-12 bg-theme-secondary rounded-lg"}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"h-4 bg-theme-secondary rounded w-3/4 mb-2"}),(0,n.jsx)("div",{className:"h-3 bg-theme-secondary rounded w-1/2"})]})]})},e))}):(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-theme font-inter",children:"Withdrawal Accounts"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"Manage your withdrawal destinations"})]}),c&&(0,n.jsx)(o.$n,{onClick:()=>b(!0),leftIcon:l.GGD,className:"font-inter",children:"Add Account"})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(r.N,{children:u.map(e=>{let s=(e=>{switch(e){case"bank_account":default:return l.lZI;case"mobile_money":return l.PCV;case"crypto_wallet":return l.z8N}})(e.type),r=a===e.id;return(0,n.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:(0,n.jsx)(d.uk,{className:"p-6 cursor-pointer transition-all duration-300 ".concat(r?"ring-2 ring-brand bg-brand/5":"hover:bg-theme-secondary/50"),onClick:()=>null==t?void 0:t(e),elevation:r?3:1,children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)("div",{className:"p-3 rounded-lg ".concat(e.isVerified?"bg-green-500/20 text-green-400":"bg-yellow-500/20 text-yellow-400"),children:(0,n.jsx)(s,{className:"w-6 h-6"})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("h4",{className:"font-semibold text-theme font-inter",children:"bank_account"===e.type&&e.bankDetails?"".concat(e.bankDetails.bankName," - ").concat(e.bankDetails.accountNumber.slice(-4)):"mobile_money"===e.type&&e.mobileMoneyDetails?"".concat(e.mobileMoneyDetails.provider.toUpperCase()," - ").concat(e.mobileMoneyDetails.phoneNumber):"crypto_wallet"===e.type&&e.cryptoDetails?"".concat(e.cryptoDetails.currency.toUpperCase()," Wallet"):"Unknown Account"}),e.isDefault&&(0,n.jsxs)("div",{className:"flex items-center space-x-1 px-2 py-1 bg-brand/20 rounded-full",children:[(0,n.jsx)(l.usP,{className:"w-3 h-3 text-brand"}),(0,n.jsx)("span",{className:"text-xs text-brand font-medium font-inter",children:"Default"})]}),e.isVerified?(0,n.jsxs)("div",{className:"flex items-center space-x-1 px-2 py-1 bg-green-500/20 rounded-full",children:[(0,n.jsx)(l.pcC,{className:"w-3 h-3 text-green-400"}),(0,n.jsx)("span",{className:"text-xs text-green-400 font-medium font-inter",children:"Verified"})]}):(0,n.jsxs)("div",{className:"flex items-center space-x-1 px-2 py-1 bg-yellow-500/20 rounded-full",children:[(0,n.jsx)(l.yGN,{className:"w-3 h-3 text-yellow-400"}),(0,n.jsx)("span",{className:"text-xs text-yellow-400 font-medium font-inter",children:"Pending"})]})]}),(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter mt-1",children:"bank_account"===e.type&&e.bankDetails?e.bankDetails.accountName:"mobile_money"===e.type&&e.mobileMoneyDetails?e.mobileMoneyDetails.accountName:"crypto_wallet"===e.type&&e.cryptoDetails?"".concat(e.cryptoDetails.walletAddress.slice(0,8),"...").concat(e.cryptoDetails.walletAddress.slice(-8)):""}),(0,n.jsxs)("p",{className:"text-theme-secondary text-xs font-inter mt-1",children:["Added ",new Date(e.createdAt).toLocaleDateString()]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.isDefault&&(0,n.jsx)(o.$n,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),k(e.id)},className:"font-inter",children:"Set Default"}),(0,n.jsx)(o.$n,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),w(e)},leftIcon:l.Pj4,className:"font-inter",children:"Edit"}),(0,n.jsx)(o.$n,{size:"sm",variant:"outline",onClick:t=>{t.stopPropagation(),C(e.id)},leftIcon:l.IXo,loading:N===e.id,className:"font-inter text-red-400 border-red-400 hover:bg-red-400/10",children:"Delete"})]})]})})},e.id)})}),0===u.length&&(0,n.jsxs)(d.uk,{className:"p-12 text-center",children:[(0,n.jsx)(l.lZI,{className:"w-16 h-16 text-theme-secondary mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-2",children:"No Withdrawal Accounts"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter mb-6",children:"Add a withdrawal account to start receiving automatic transfers"}),(0,n.jsx)(o.$n,{onClick:()=>b(!0),leftIcon:l.GGD,className:"font-inter",children:"Add Your First Account"})]})]}),(0,n.jsx)(r.N,{children:f&&(0,n.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",onClick:()=>b(!1),children:(0,n.jsxs)(i.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-theme rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-theme font-inter",children:"Add Withdrawal Account"}),(0,n.jsx)(o.$n,{size:"sm",variant:"outline",onClick:()=>b(!1),leftIcon:l.yGN,className:"font-inter",children:"Close"})]}),(0,n.jsx)(x,{onAccountCreated:e=>{m(t=>[...t,e]),b(!1)},onCancel:()=>b(!1)})]})})}),(0,n.jsx)(r.N,{children:y&&(0,n.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",onClick:()=>w(null),children:(0,n.jsxs)(i.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-theme rounded-2xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-theme font-inter",children:"Edit Account"}),(0,n.jsx)(o.$n,{size:"sm",variant:"outline",onClick:()=>w(null),leftIcon:l.yGN,className:"font-inter",children:"Close"})]}),(0,n.jsx)(x,{editingAccount:y,onAccountCreated:e=>{m(t=>t.map(t=>t.id===e.id?e:t)),w(null)},onCancel:()=>w(null)})]})})})]})}function p(e){let{onRuleCreated:t,onCancel:a,editingRule:r,withdrawalAccounts:i}=e,[c,u]=(0,s.useState)(1),[m,x]=(0,s.useState)(!1),[g,p]=(0,s.useState)(null),[f,b]=(0,s.useState)({name:"",description:"",triggerType:"balance_threshold",triggerConditions:{},withdrawalConfig:{withdrawalAccountId:"",amountType:"fixed",currency:"NGN"},executionSettings:{priority:"medium",retryAttempts:3,retryDelay:5,notifyUser:!0,requireApproval:!1}});(0,s.useEffect)(()=>{r&&y()},[r]);let y=()=>{r&&b({name:r.name,description:r.description||"",triggerType:r.triggerType,triggerConditions:r.triggerConditions,withdrawalConfig:r.withdrawalConfig,executionSettings:r.executionSettings})},w=[{type:"balance_threshold",icon:l.ARf,title:"Balance Threshold",description:"Withdraw when balance reaches a specific amount"},{type:"date_based",icon:l.wIk,title:"Scheduled",description:"Withdraw on specific dates or intervals"},{type:"interest_earned",icon:l.fTJ,title:"Interest Earned",description:"Withdraw when interest earned reaches threshold"},{type:"goal_reached",icon:l.x_j,title:"Goal Reached",description:"Withdraw when savings goal is achieved"}],N=[{type:"fixed",title:"Fixed Amount",description:"Withdraw a specific amount each time"},{type:"percentage",title:"Percentage",description:"Withdraw a percentage of your balance"},{type:"excess",title:"Excess Amount",description:"Withdraw amount above the trigger threshold"},{type:"all",title:"All Available",description:"Withdraw all available funds (minus minimum balance)"}],j=async()=>{x(!0),p(null);try{let e;e=r?await h.updateAutomaticRule(r.id,f):await h.createAutomaticRule(f),t(e)}catch(e){p(e.message||"Failed to save automatic withdrawal rule")}finally{x(!1)}},v=e=>{b(t=>({...t,...e}))},k=e=>{b(t=>({...t,triggerConditions:{...t.triggerConditions,...e}}))},C=e=>{b(t=>({...t,withdrawalConfig:{...t.withdrawalConfig,...e}}))},A=e=>{b(t=>({...t,executionSettings:{...t.executionSettings,...e}}))},T=[{number:1,title:"Basic Info",component:()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-2",children:"Basic Information"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"Give your automatic withdrawal rule a name and description"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Rule Name"}),(0,n.jsx)("input",{type:"text",value:f.name,onChange:e=>v({name:e.target.value}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"e.g., Monthly Interest Withdrawal",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Description (Optional)"}),(0,n.jsx)("textarea",{value:f.description,onChange:e=>v({description:e.target.value}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"Describe when and why this rule should trigger",rows:3})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-3 font-inter",children:"Trigger Type"}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-3",children:w.map(e=>{let t=e.icon,a=f.triggerType===e.type;return(0,n.jsx)(d.uk,{className:"p-4 cursor-pointer transition-all duration-300 ".concat(a?"ring-2 ring-brand bg-brand/5":"hover:bg-theme-secondary/50"),onClick:()=>v({triggerType:e.type}),elevation:a?2:1,children:(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"p-2 rounded-lg ".concat(a?"bg-brand text-white":"bg-theme-secondary text-theme-secondary"),children:(0,n.jsx)(t,{className:"w-5 h-5"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold text-theme font-inter",children:e.title}),(0,n.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:e.description})]}),a&&(0,n.jsx)(l.YrT,{className:"w-5 h-5 text-brand ml-auto"})]})},e.type)})})]})]})},{number:2,title:"Trigger",component:()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-2",children:"Trigger Conditions"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"Configure when this rule should activate"})]}),"balance_threshold"===f.triggerType&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Balance Threshold (₦)"}),(0,n.jsx)("input",{type:"number",value:f.triggerConditions.balanceThreshold||"",onChange:e=>k({balanceThreshold:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"100000",min:"1000",required:!0}),(0,n.jsx)("p",{className:"text-xs text-theme-secondary mt-1 font-inter",children:"Trigger when balance reaches this amount"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Minimum Balance to Keep (₦)"}),(0,n.jsx)("input",{type:"number",value:f.triggerConditions.minimumBalance||"",onChange:e=>k({minimumBalance:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"10000",min:"0"}),(0,n.jsx)("p",{className:"text-xs text-theme-secondary mt-1 font-inter",children:"Always keep this minimum amount in your account"})]})]}),"date_based"===f.triggerType&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Frequency"}),(0,n.jsxs)("select",{value:f.triggerConditions.frequency||"monthly",onChange:e=>k({frequency:e.target.value}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",children:[(0,n.jsx)("option",{value:"daily",children:"Daily"}),(0,n.jsx)("option",{value:"weekly",children:"Weekly"}),(0,n.jsx)("option",{value:"monthly",children:"Monthly"}),(0,n.jsx)("option",{value:"quarterly",children:"Quarterly"})]})]}),"monthly"===f.triggerConditions.frequency&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Day of Month"}),(0,n.jsx)("input",{type:"number",value:f.triggerConditions.dayOfMonth||"",onChange:e=>k({dayOfMonth:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"1",min:"1",max:"31",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Time"}),(0,n.jsx)("input",{type:"time",value:f.triggerConditions.time||"09:00",onChange:e=>k({time:e.target.value}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter"})]})]}),"interest_earned"===f.triggerType&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Interest Threshold (₦)"}),(0,n.jsx)("input",{type:"number",value:f.triggerConditions.interestThreshold||"",onChange:e=>k({interestThreshold:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"5000",min:"100",required:!0}),(0,n.jsx)("p",{className:"text-xs text-theme-secondary mt-1 font-inter",children:"Trigger when interest earned reaches this amount"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Interest Period"}),(0,n.jsxs)("select",{value:f.triggerConditions.interestPeriod||"monthly",onChange:e=>k({interestPeriod:e.target.value}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",children:[(0,n.jsx)("option",{value:"daily",children:"Daily"}),(0,n.jsx)("option",{value:"weekly",children:"Weekly"}),(0,n.jsx)("option",{value:"monthly",children:"Monthly"})]})]})]})]})},{number:3,title:"Amount",component:()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-2",children:"Withdrawal Configuration"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"Configure how much to withdraw and where to send it"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Withdrawal Account"}),(0,n.jsxs)("select",{value:f.withdrawalConfig.withdrawalAccountId,onChange:e=>C({withdrawalAccountId:e.target.value}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",required:!0,children:[(0,n.jsx)("option",{value:"",children:"Select withdrawal account"}),i.map(e=>(0,n.jsx)("option",{value:e.id,children:"bank_account"===e.type&&e.bankDetails?"".concat(e.bankDetails.bankName," - ").concat(e.bankDetails.accountNumber.slice(-4)):"".concat(e.type," account")},e.id))]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-3 font-inter",children:"Amount Type"}),(0,n.jsx)("div",{className:"grid grid-cols-1 gap-3",children:N.map(e=>{let t=f.withdrawalConfig.amountType===e.type;return(0,n.jsx)(d.uk,{className:"p-4 cursor-pointer transition-all duration-300 ".concat(t?"ring-2 ring-brand bg-brand/5":"hover:bg-theme-secondary/50"),onClick:()=>C({amountType:e.type}),elevation:t?2:1,children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-semibold text-theme font-inter",children:e.title}),(0,n.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:e.description})]}),t&&(0,n.jsx)(l.YrT,{className:"w-5 h-5 text-brand"})]})},e.type)})})]}),"fixed"===f.withdrawalConfig.amountType&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Fixed Amount (₦)"}),(0,n.jsx)("input",{type:"number",value:f.withdrawalConfig.amount||"",onChange:e=>C({amount:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"50000",min:"1000",required:!0})]}),"percentage"===f.withdrawalConfig.amountType&&(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Percentage (%)"}),(0,n.jsx)("input",{type:"number",value:f.withdrawalConfig.percentage||"",onChange:e=>C({percentage:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"10",min:"1",max:"100",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Keep Minimum Balance (₦)"}),(0,n.jsx)("input",{type:"number",value:f.withdrawalConfig.keepMinimumBalance||"",onChange:e=>C({keepMinimumBalance:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"10000",min:"0"})]})]}),("all"===f.withdrawalConfig.amountType||"excess"===f.withdrawalConfig.amountType)&&(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Keep Minimum Balance (₦)"}),(0,n.jsx)("input",{type:"number",value:f.withdrawalConfig.keepMinimumBalance||"",onChange:e=>C({keepMinimumBalance:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",placeholder:"10000",min:"0"}),(0,n.jsx)("p",{className:"text-xs text-theme-secondary mt-1 font-inter",children:"Always keep this minimum amount in your account"})]})]})},{number:4,title:"Settings",component:()=>(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-2",children:"Execution Settings"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"Configure how the rule should be executed"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Priority Level"}),(0,n.jsxs)("select",{value:f.executionSettings.priority,onChange:e=>A({priority:e.target.value}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",children:[(0,n.jsx)("option",{value:"low",children:"Low Priority"}),(0,n.jsx)("option",{value:"medium",children:"Medium Priority"}),(0,n.jsx)("option",{value:"high",children:"High Priority"})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Retry Attempts"}),(0,n.jsx)("input",{type:"number",value:f.executionSettings.retryAttempts,onChange:e=>A({retryAttempts:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",min:"0",max:"10"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-theme mb-2 font-inter",children:"Retry Delay (minutes)"}),(0,n.jsx)("input",{type:"number",value:f.executionSettings.retryDelay,onChange:e=>A({retryDelay:Number(e.target.value)}),className:"w-full px-3 py-3 border border-theme rounded-lg bg-theme-secondary text-theme focus:outline-none focus:ring-2 focus:ring-brand focus:border-brand font-inter",min:"1",max:"60"})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-theme font-inter",children:"Notify User"}),(0,n.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:"Send notification when rule executes"})]}),(0,n.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,n.jsx)("input",{type:"checkbox",checked:f.executionSettings.notifyUser,onChange:e=>A({notifyUser:e.target.checked}),className:"sr-only peer"}),(0,n.jsx)("div",{className:"w-11 h-6 bg-theme-secondary peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand"})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h4",{className:"font-medium text-theme font-inter",children:"Require Approval"}),(0,n.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:"Require manual approval before execution"})]}),(0,n.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,n.jsx)("input",{type:"checkbox",checked:f.executionSettings.requireApproval,onChange:e=>A({requireApproval:e.target.checked}),className:"sr-only peer"}),(0,n.jsx)("div",{className:"w-11 h-6 bg-theme-secondary peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-brand/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-brand"})]})]})]})]})}];return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"flex items-center justify-between",children:T.map((e,t)=>(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors ".concat(c>=e.number?"bg-brand border-brand text-white":"border-theme-secondary text-theme-secondary"),children:c>e.number?(0,n.jsx)(l.YrT,{className:"w-4 h-4"}):(0,n.jsx)("span",{className:"text-sm font-medium",children:e.number})}),(0,n.jsx)("span",{className:"ml-2 text-sm font-medium ".concat(c>=e.number?"text-theme":"text-theme-secondary"," font-inter"),children:e.title}),t<T.length-1&&(0,n.jsx)("div",{className:"w-8 h-0.5 mx-4 ".concat(c>e.number?"bg-brand":"bg-theme-secondary")})]},e.number))}),(0,n.jsx)(d.uk,{className:"p-6",children:T[c-1].component()}),g&&(0,n.jsxs)("div",{className:"flex items-center space-x-2 p-3 bg-red-500/10 border border-red-500/20 rounded-lg",children:[(0,n.jsx)(l.y3G,{className:"w-5 h-5 text-red-400"}),(0,n.jsx)("span",{className:"text-red-400 text-sm font-inter",children:g})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsxs)("div",{className:"flex space-x-3",children:[(0,n.jsx)(o.$n,{variant:"outline",onClick:a,className:"font-inter",children:"Cancel"}),c>1&&(0,n.jsx)(o.$n,{variant:"outline",onClick:()=>u(e=>e-1),className:"font-inter",children:"Previous"})]}),(0,n.jsx)("div",{className:"flex space-x-3",children:c<T.length?(0,n.jsx)(o.$n,{onClick:()=>u(e=>e+1),disabled:!f.name||!f.triggerType,className:"font-inter",children:"Next"}):(0,n.jsx)(o.$n,{onClick:j,loading:m,disabled:!f.withdrawalConfig.withdrawalAccountId,className:"font-inter",children:r?"Update Rule":"Create Rule"})})]})]})}class f{startProcessingEngine(){this.processingInterval&&clearInterval(this.processingInterval),this.processingInterval=setInterval(()=>{this.processAutomaticWithdrawals()},3e5),console.log("Automatic withdrawal processing engine started")}stopProcessingEngine(){this.processingInterval&&(clearInterval(this.processingInterval),this.processingInterval=null),console.log("Automatic withdrawal processing engine stopped")}async processAutomaticWithdrawals(){if(this.isProcessing)return console.log("Processing already in progress, skipping..."),[];this.isProcessing=!0,this.statistics.lastProcessingTime=new Date().toISOString();let e=[];try{console.log("Starting automatic withdrawal processing...");let t=(await h.getAutomaticRules()).filter(e=>e.isActive);for(let a of(console.log("Found ".concat(t.length," active rules to process")),t))try{let t=await this.processRule(a);e.push(t),t.success?this.statistics.processedRulesCount++:this.statistics.failedRulesCount++}catch(t){console.error("Error processing rule ".concat(a.id,":"),t),e.push({ruleId:a.id,success:!1,error:t instanceof Error?t.message:"Unknown error",executionTime:0}),this.statistics.failedRulesCount++}await this.processQueue(),console.log("Processing completed. Processed: ".concat(e.filter(e=>e.success).length,", Failed: ").concat(e.filter(e=>!e.success).length))}catch(e){console.error("Error in automatic withdrawal processing:",e)}finally{this.isProcessing=!1}return e}async processRule(e){let t=Date.now();try{if(!await this.shouldRuleTrigger(e))return{ruleId:e.id,success:!0,executionTime:Date.now()-t};let a=await this.getUserBalance(e.userId),n=this.calculateWithdrawalAmount(e,a);if(n<=0)return{ruleId:e.id,success:!0,executionTime:Date.now()-t};let s=await this.validateWithdrawal(e,n,a);if(!s.isValid)throw Error(s.error);let r=await this.createWithdrawalRequest(e,n);return e.executionSettings.requireApproval||await this.processWithdrawal(r),await this.updateRuleStatistics(e.id,!0,n),{ruleId:e.id,success:!0,withdrawalRequestId:r.id,amount:n,executionTime:Date.now()-t}}catch(a){return console.error("Error processing rule ".concat(e.id,":"),a),await this.updateRuleStatistics(e.id,!1),e.executionSettings.retryAttempts>0&&this.addToRetryQueue(e),{ruleId:e.id,success:!1,error:a instanceof Error?a.message:"Unknown error",executionTime:Date.now()-t}}}async shouldRuleTrigger(e){let t=new Date,a=e.triggerConditions;switch(e.triggerType){case"balance_threshold":if(a.balanceThreshold)return(await this.getUserBalance(e.userId)).availableBalance>=a.balanceThreshold;return!1;case"date_based":return this.checkDateBasedTrigger(e,t);case"interest_earned":if(a.interestThreshold)return(await this.getUserBalance(e.userId)).interestEarned>=a.interestThreshold;return!1;default:return!1}}checkDateBasedTrigger(e,t){let a=e.triggerConditions,n=e.executionSettings,s=e.lastExecuted?new Date(e.lastExecuted):null;if(n.executionWindow){let e=60*t.getHours()+t.getMinutes(),[a,s]=n.executionWindow.startTime.split(":").map(Number),[r,i]=n.executionWindow.endTime.split(":").map(Number);if(e<60*a+s||e>60*r+i)return!1}switch(a.frequency){case"daily":if(!s)return!0;return Math.floor((t.getTime()-s.getTime())/864e5)>=1;case"weekly":if(!s)return!0;return Math.floor((t.getTime()-s.getTime())/6048e5)>=1&&(!a.dayOfWeek||t.getDay()===a.dayOfWeek);case"monthly":if(!s)return!0;return(t.getFullYear()-s.getFullYear())*12+(t.getMonth()-s.getMonth())>=1&&(!a.dayOfMonth||t.getDate()===a.dayOfMonth);case"quarterly":if(!s)return!0;return Math.floor(((t.getFullYear()-s.getFullYear())*12+(t.getMonth()-s.getMonth()))/3)>=1;default:return!1}}calculateWithdrawalAmount(e,t){let a=e.withdrawalConfig,n=t.availableBalance;switch(a.amountType){case"fixed":return a.amount||0;case"percentage":if(!a.percentage)return 0;let s=n*a.percentage/100;return a.keepMinimumBalance?Math.max(0,s-a.keepMinimumBalance):s;case"excess":return Math.max(0,n-(e.triggerConditions.balanceThreshold||0));case"all":return a.keepMinimumBalance?Math.max(0,n-a.keepMinimumBalance):n;default:return 0}}async validateWithdrawal(e,t,a){return t<1e3?{isValid:!1,error:"Withdrawal amount is below minimum (₦1,000)"}:e.withdrawalConfig.maxAmount&&t>e.withdrawalConfig.maxAmount?{isValid:!1,error:"Withdrawal amount exceeds maximum limit"}:t>a.availableBalance?{isValid:!1,error:"Insufficient balance for withdrawal"}:a.availableBalance-t<(e.withdrawalConfig.keepMinimumBalance||0)?{isValid:!1,error:"Withdrawal would violate minimum balance requirement"}:{isValid:!0}}async createWithdrawalRequest(e,t){let a={withdrawalAccountId:e.withdrawalConfig.withdrawalAccountId,amount:t,reason:"Automatic withdrawal: ".concat(e.name),sourceType:"balance",type:"automatic"};return h.createInstantWithdrawal(a)}async processWithdrawal(e){console.log("Processing withdrawal ".concat(e.id," for amount ₦").concat(e.amount))}async updateRuleStatistics(e,t,a){console.log("Updating statistics for rule ".concat(e,": success=").concat(t,", amount=").concat(a))}addToRetryQueue(e){let t=new Date;t.setMinutes(t.getMinutes()+e.executionSettings.retryDelay),this.processingQueue.push({ruleId:e.id,userId:e.userId,priority:e.executionSettings.priority,scheduledTime:t,retryCount:0}),this.processingQueue.sort((e,t)=>{let a={high:3,medium:2,low:1},n=a[t.priority]-a[e.priority];return 0!==n?n:e.scheduledTime.getTime()-t.scheduledTime.getTime()})}async processQueue(){let e=new Date;for(let t of this.processingQueue.filter(t=>t.scheduledTime<=e))try{this.processingQueue=this.processingQueue.filter(e=>e.ruleId!==t.ruleId);let e=(await h.getAutomaticRules()).find(e=>e.id===t.ruleId);e&&await this.processRule(e)}catch(e){console.error("Error processing queued item ".concat(t.ruleId,":"),e)}}async getUserBalance(e){return{userId:e,totalBalance:15e4,availableBalance:14e4,interestEarned:5e3,lastInterestDate:new Date().toISOString()}}async triggerRule(e){let t=(await h.getAutomaticRules()).find(t=>t.id===e);if(!t)throw Error("Rule not found");return this.processRule(t)}getProcessingStatus(){return{isProcessing:this.isProcessing,queueLength:this.processingQueue.length,lastProcessingTime:this.statistics.lastProcessingTime,processedRulesCount:this.statistics.processedRulesCount,failedRulesCount:this.statistics.failedRulesCount}}getQueueStatus(){return[...this.processingQueue]}async forceProcessing(){return this.processAutomaticWithdrawals()}constructor(){this.isProcessing=!1,this.processingQueue=[],this.processingInterval=null,this.statistics={processedRulesCount:0,failedRulesCount:0,lastProcessingTime:void 0},this.startProcessingEngine()}}let b=new f;function y(){let[e,t]=(0,s.useState)("overview"),[a,u]=(0,s.useState)(null),[m,x]=(0,s.useState)(!0),[f,y]=(0,s.useState)(!1),[w,N]=(0,s.useState)(!1),[j,v]=(0,s.useState)(null),[k,C]=(0,s.useState)(null);(0,s.useEffect)(()=>{A(),T();let e=setInterval(T,3e4);return()=>clearInterval(e)},[]);let A=async()=>{try{x(!0);let e=await h.getDashboardData();u(e)}catch(e){console.error("Failed to load dashboard data:",e),u({totalBalance:25e4,availableForWithdrawal:24e4,pendingWithdrawals:15e3,activeRules:3,recentWithdrawals:[],activeWithdrawalRules:[],withdrawalAccounts:[],monthlyWithdrawalStats:{totalAmount:85e3,totalCount:12,successRate:95.8}})}finally{x(!1)}},T=async()=>{try{let e=b.getProcessingStatus();C(e)}catch(e){console.error("Failed to load processing status:",e)}},R=async(e,t)=>{try{await h.toggleRuleStatus(e,t),A()}catch(e){console.error("Failed to toggle rule:",e)}},P=async e=>{try{await h.deleteAutomaticRule(e),A()}catch(e){console.error("Failed to delete rule:",e)}},S=async()=>{try{await b.forceProcessing(),T(),A()}catch(e){console.error("Failed to trigger processing:",e)}},D=e=>"₦".concat(e.toLocaleString()),I=[{id:"overview",label:"Overview",icon:l.ARf},{id:"accounts",label:"Accounts",icon:l.lZI},{id:"rules",label:"Auto Rules",icon:l.VSk},{id:"history",label:"History",icon:l.Ohp}];return m?(0,n.jsx)(c.A,{children:(0,n.jsx)("div",{className:"space-y-6",children:[1,2,3].map(e=>(0,n.jsxs)(d.uk,{className:"p-6 animate-pulse",children:[(0,n.jsx)("div",{className:"h-4 bg-theme-secondary rounded w-3/4 mb-4"}),(0,n.jsx)("div",{className:"h-3 bg-theme-secondary rounded w-1/2"})]},e))})}):(0,n.jsx)(c.A,{children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-theme font-inter",children:"Automatic Withdrawals"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"Manage your automatic withdrawal accounts and rules"})]}),(0,n.jsx)("div",{className:"flex space-x-1 bg-theme-secondary/50 p-1 rounded-lg",children:I.map(a=>{let s=a.icon;return(0,n.jsxs)("button",{onClick:()=>t(a.id),className:"flex items-center space-x-2 px-4 py-2 rounded-md transition-all font-inter ".concat(e===a.id?"bg-brand text-white":"text-theme-secondary hover:text-theme hover:bg-theme-secondary"),children:[(0,n.jsx)(s,{className:"w-4 h-4"}),(0,n.jsx)("span",{children:a.label})]},a.id)})}),(0,n.jsx)(r.N,{mode:"wait",children:(0,n.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},children:["overview"===e&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,n.jsx)(d.uk,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter",children:"Total Balance"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-theme font-inter",children:D((null==a?void 0:a.totalBalance)||0)})]}),(0,n.jsx)("div",{className:"p-3 bg-brand/20 rounded-lg",children:(0,n.jsx)(l.z8N,{className:"w-6 h-6 text-brand"})})]})}),(0,n.jsx)(d.uk,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter",children:"Available"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-theme font-inter",children:D((null==a?void 0:a.availableForWithdrawal)||0)})]}),(0,n.jsx)("div",{className:"p-3 bg-green-500/20 rounded-lg",children:(0,n.jsx)(l.FrA,{className:"w-6 h-6 text-green-400"})})]})}),(0,n.jsx)(d.uk,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter",children:"Pending"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-theme font-inter",children:D((null==a?void 0:a.pendingWithdrawals)||0)})]}),(0,n.jsx)("div",{className:"p-3 bg-yellow-500/20 rounded-lg",children:(0,n.jsx)(l.Ohp,{className:"w-6 h-6 text-yellow-400"})})]})}),(0,n.jsx)(d.uk,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter",children:"Active Rules"}),(0,n.jsx)("p",{className:"text-2xl font-bold text-theme font-inter",children:(null==a?void 0:a.activeRules)||0})]}),(0,n.jsx)("div",{className:"p-3 bg-blue-500/20 rounded-lg",children:(0,n.jsx)(l.VSk,{className:"w-6 h-6 text-blue-400"})})]})})]}),k&&(0,n.jsxs)(d.uk,{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter",children:"Processing Engine"}),(0,n.jsx)(o.$n,{size:"sm",onClick:S,leftIcon:l.jTZ,className:"font-inter",children:"Trigger Processing"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter",children:"Status"}),(0,n.jsxs)("div",{className:"inline-flex items-center space-x-2 px-3 py-1 rounded-full ".concat(k.isProcessing?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"),children:[(0,n.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(k.isProcessing?"bg-green-400":"bg-gray-400")}),(0,n.jsx)("span",{className:"text-sm font-medium font-inter",children:k.isProcessing?"Processing":"Idle"})]})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter",children:"Queue"}),(0,n.jsx)("p",{className:"text-xl font-bold text-theme font-inter",children:k.queueLength})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter",children:"Processed"}),(0,n.jsx)("p",{className:"text-xl font-bold text-theme font-inter",children:k.processedRulesCount})]}),(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter",children:"Failed"}),(0,n.jsx)("p",{className:"text-xl font-bold text-theme font-inter",children:k.failedRulesCount})]})]})]}),(0,n.jsxs)(d.uk,{className:"p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-4",children:"Quick Actions"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsx)(o.$n,{onClick:()=>y(!0),leftIcon:l.lZI,className:"font-inter",fullWidth:!0,children:"Manage Accounts"}),(0,n.jsx)(o.$n,{onClick:()=>N(!0),leftIcon:l.GGD,className:"font-inter",fullWidth:!0,children:"Create Auto Rule"}),(0,n.jsx)(o.$n,{onClick:()=>t("history"),leftIcon:l.Ohp,variant:"outline",className:"font-inter",fullWidth:!0,children:"View History"})]})]}),(0,n.jsxs)(d.uk,{className:"p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-4",children:"Recent Withdrawals"}),(null==a?void 0:a.recentWithdrawals.length)===0?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(l.Ohp,{className:"w-12 h-12 text-theme-secondary mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"No recent withdrawals"})]}):(0,n.jsx)("div",{className:"space-y-3",children:null==a?void 0:a.recentWithdrawals.map(e=>(0,n.jsxs)("div",{className:"flex items-center justify-between p-3 bg-theme-secondary/50 rounded-lg",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"font-medium text-theme font-inter",children:D(e.amount)}),(0,n.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,n.jsx)("div",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat("completed"===e.status?"bg-green-500/20 text-green-400":"pending"===e.status?"bg-yellow-500/20 text-yellow-400":"bg-red-500/20 text-red-400"),children:e.status})]},e.id))})]})]}),"accounts"===e&&(0,n.jsx)(g,{showAddButton:!0}),"rules"===e&&(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-xl font-bold text-theme font-inter",children:"Automatic Withdrawal Rules"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"Manage your automatic withdrawal rules"})]}),(0,n.jsx)(o.$n,{onClick:()=>N(!0),leftIcon:l.GGD,className:"font-inter",children:"Create Rule"})]}),(null==a?void 0:a.activeWithdrawalRules.length)===0?(0,n.jsxs)(d.uk,{className:"p-12 text-center",children:[(0,n.jsx)(l.VSk,{className:"w-16 h-16 text-theme-secondary mx-auto mb-4"}),(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-2",children:"No Automatic Rules"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter mb-6",children:"Create automatic withdrawal rules to streamline your transfers"}),(0,n.jsx)(o.$n,{onClick:()=>N(!0),leftIcon:l.GGD,className:"font-inter",children:"Create Your First Rule"})]}):(0,n.jsx)("div",{className:"space-y-4",children:null==a?void 0:a.activeWithdrawalRules.map(e=>(0,n.jsx)(d.uk,{className:"p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,n.jsx)("h4",{className:"font-semibold text-theme font-inter",children:e.name}),(0,n.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(e.isActive?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"),children:e.isActive?"Active":"Inactive"})]}),e.description&&(0,n.jsx)("p",{className:"text-theme-secondary text-sm font-inter mb-2",children:e.description}),(0,n.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-theme-secondary",children:[(0,n.jsxs)("span",{className:"font-inter",children:["Trigger: ",e.triggerType.replace("_"," ")]}),(0,n.jsxs)("span",{className:"font-inter",children:["Amount: ",e.withdrawalConfig.amountType]}),(0,n.jsxs)("span",{className:"font-inter",children:["Executions: ",e.statistics.totalExecutions]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)(o.$n,{size:"sm",variant:"outline",onClick:()=>R(e.id,!e.isActive),leftIcon:e.isActive?l.GHw:l.aze,className:"font-inter",children:e.isActive?"Pause":"Activate"}),(0,n.jsx)(o.$n,{size:"sm",variant:"outline",onClick:()=>v(e),leftIcon:l.Pj4,className:"font-inter",children:"Edit"}),(0,n.jsx)(o.$n,{size:"sm",variant:"outline",onClick:()=>P(e.id),leftIcon:l.IXo,className:"font-inter text-red-400 border-red-400 hover:bg-red-400/10",children:"Delete"})]})]})},e.id))})]}),"history"===e&&(0,n.jsxs)(d.uk,{className:"p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-4",children:"Withdrawal History"}),(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)(l.Ohp,{className:"w-12 h-12 text-theme-secondary mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-theme-secondary font-inter",children:"Withdrawal history will be displayed here"})]})]})]},e)}),(0,n.jsxs)(r.N,{children:[f&&(0,n.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",onClick:()=>y(!1),children:(0,n.jsx)(i.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-theme rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:(0,n.jsx)(g,{})})}),(w||j)&&(0,n.jsx)(i.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",onClick:()=>{N(!1),v(null)},children:(0,n.jsx)(i.P.div,{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.9,opacity:0},className:"bg-theme rounded-2xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto",onClick:e=>e.stopPropagation(),children:(0,n.jsx)(p,{editingRule:j||void 0,withdrawalAccounts:(null==a?void 0:a.withdrawalAccounts)||[],onRuleCreated:e=>{N(!1),v(null),A()},onCancel:()=>{N(!1),v(null)}})})})]})]})})}}},e=>{e.O(0,[844,9268,5236,6874,6766,5221,193,3289,1846,8441,5964,7358],()=>e(e.s=57306)),_N_E=e.O()}]);