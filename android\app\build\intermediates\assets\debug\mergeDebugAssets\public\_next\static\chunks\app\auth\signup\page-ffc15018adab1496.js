(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2778],{585:(e,t,s)=>{"use strict";s.d(t,{L$:()=>i});var r=s(95155);s(12115);var a=s(68289);let n=[{left:10,top:20},{left:25,top:15},{left:40,top:30},{left:55,top:10},{left:70,top:25},{left:85,top:35},{left:15,top:45},{left:30,top:55},{left:45,top:40},{left:60,top:50},{left:75,top:60},{left:90,top:45},{left:5,top:70},{left:20,top:80},{left:35,top:65},{left:50,top:75},{left:65,top:85},{left:80,top:70},{left:95,top:80},{left:10,top:90},{left:25,top:5},{left:40,top:85},{left:55,top:95},{left:70,top:5},{left:85,top:15},{left:15,top:25},{left:30,top:35},{left:45,top:45},{left:60,top:55},{left:75,top:65},{left:90,top:75},{left:5,top:85},{left:20,top:95},{left:35,top:5},{left:50,top:15},{left:65,top:25},{left:80,top:35},{left:95,top:45}];function i(e){let{variant:t="default",className:s=""}=e,i={default:{gradient:"bg-gradient-to-br from-gray-900 via-green-900 to-black",particles:30,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]},auth:{gradient:"bg-gradient-to-br from-gray-900 via-black to-gray-900",particles:20,colors:["bg-green-500","bg-orange-500","bg-yellow-500"]},dashboard:{gradient:"bg-gradient-to-br from-gray-900 via-gray-800 to-black",particles:15,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]}}[t];return(0,r.jsxs)("div",{className:"fixed inset-0 z-0 ".concat(s),children:[(0,r.jsx)("div",{className:"absolute inset-0 ".concat(i.gradient," animate-gradient")}),(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)(a.P.div,{className:"absolute -top-40 -right-40 w-80 h-80 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,30,-20,0],y:[0,-50,20,0],scale:[1,1.1,.9,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut"}}),(0,r.jsx)(a.P.div,{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,-30,20,0],y:[0,50,-20,0],scale:[1,.9,1.1,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:2}}),(0,r.jsx)(a.P.div,{className:"absolute top-40 left-40 w-80 h-80 bg-amber-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,20,-30,0],y:[0,-30,40,0],scale:[1,1.2,.8,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:4}})]}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",children:[...Array(i.particles)].map((e,t)=>{let s=n[t%n.length];return(0,r.jsx)(a.P.div,{className:"absolute w-1 h-1 ".concat(i.colors[t%i.colors.length]," rounded-full"),style:{left:"".concat(s.left,"%"),top:"".concat(s.top,"%")},animate:{y:[0,-20,0],opacity:[.2,.8,.2],scale:[1,1.5,1]},transition:{duration:3+t%3,repeat:1/0,delay:t%4*.5,ease:"easeInOut"}},t)})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"})]})}},9609:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var r=s(95155),a=s(11518),n=s.n(a),i=s(68289),o=s(66766),l=s(6874),c=s.n(l),d=s(35695),u=s(12115),h=s(10351),m=s(31246),f=s(585),p=s(49697),x=s(64198);let g=u.lazy(()=>s.e(2266).then(s.bind(s,62266))),y=u.lazy(()=>s.e(7223).then(s.bind(s,27223)));function b(){(0,d.useRouter)();let[e,t]=(0,u.useState)(!1),[s,a]=(0,u.useState)(!1),[l,b]=(0,u.useState)(!1),[v,j]=(0,u.useState)(1),[w,N]=(0,u.useState)(!1);u.useEffect(()=>{N((()=>{let e=navigator.userAgent.toLowerCase();return[/android/i,/iphone/i,/ipad/i,/mobile/i].some(t=>t.test(e))||window.innerWidth<768})())},[]);let[S,_]=(0,u.useState)({firstName:"",lastName:"",email:"",phoneNumber:"",password:"",confirmPassword:"",agreeToTerms:!1}),[F,k]=(0,u.useState)({score:0,feedback:[]}),C=(e,t)=>{_({...S,[e]:t}),"password"===e&&(e=>{let t=0,s=[];e.length>=8?t++:s.push("At least 8 characters"),/[A-Z]/.test(e)?t++:s.push("One uppercase letter"),/[a-z]/.test(e)?t++:s.push("One lowercase letter"),/\d/.test(e)?t++:s.push("One number"),/[!@#$%^&*(),.?":{}|<>]/.test(e)?t++:s.push("One special character"),k({score:t,feedback:s})})(t)},P=()=>{1!==v||(S.firstName&&S.lastName&&S.email&&S.phoneNumber?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(S.email)?!/^(\+234|234|0)?[789][01]\d{8}$/.test(S.phoneNumber)&&(x.P0.error("Please enter a valid Nigerian phone number"),1):(x.P0.error("Please enter a valid email address"),1):(x.P0.error("Please fill in all fields"),1))||j(2)},R=async e=>{if(e.preventDefault(),S.password&&S.confirmPassword?S.password!==S.confirmPassword?(x.P0.error("Passwords do not match"),!1):F.score<3?(x.P0.error("Password is too weak. Please choose a stronger password."),!1):!!S.agreeToTerms||(x.P0.error("Please agree to the terms and conditions"),!1):(x.P0.error("Please fill in all password fields"),!1)){t(!0);try{console.log("API_BASE_URL:","http://localhost:8080/api"),console.log("Signup formData:",S);let e=await fetch("".concat("http://localhost:8080/api","/api/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(S)}),s=await e.json();if(console.log("Signup API response:",s),!e.ok||!s.data||!s.data.token){x.P0.error(s.error||s.message||"Signup failed"),t(!1);return}localStorage.setItem("auth_token",s.data.token),localStorage.setItem("user_data",JSON.stringify(s.data.user)),window.location.href="/dashboard"}catch(e){console.error("Signup error:",e),x.P0.error("Signup failed. Please try again.")}finally{t(!1)}}};return w?(0,r.jsx)(u.Suspense,{fallback:(0,r.jsx)("div",{children:"Loading..."}),children:(0,r.jsx)(y,{isLoading:e,children:(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-green-500 via-green-600 to-green-700",children:[(0,r.jsxs)("div",{className:"relative z-10 pt-16 pb-4 px-6 flex items-center",children:[(0,r.jsx)(g,{href:"/auth/login",variant:"default",className:"mr-4"}),(0,r.jsx)("h1",{className:"text-white text-lg font-semibold",children:"Create Account"})]}),(0,r.jsxs)("div",{className:"px-6 py-4 mt-4",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"mx-auto mb-6",children:(0,r.jsx)(o.default,{src:"/logo.svg",alt:"Better Interest",width:80,height:80,className:"w-20 h-20 object-contain",priority:!0})}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Join Better Interest"}),(0,r.jsx)("p",{className:"text-white/80",children:"Start your savings journey today"})]}),1===v&&(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),P()},className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-3 text-white/90",children:"First Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.JXP,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,r.jsx)("input",{type:"text",name:"firstName",value:S.firstName,onChange:e=>C("firstName",e.target.value),className:"w-full pl-12 pr-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl focus:border-white/40 focus:outline-none transition-all text-white placeholder-white/60 text-lg",placeholder:"Enter your first name",required:!0})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-3 text-white/90",children:"Last Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.JXP,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60"}),(0,r.jsx)("input",{type:"text",name:"lastName",value:S.lastName,onChange:e=>C("lastName",e.target.value),className:"w-full pl-12 pr-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl focus:border-white/40 focus:outline-none transition-all text-white placeholder-white/60 text-lg",placeholder:"Enter your last name",required:!0})]})]}),(0,r.jsx)("button",{type:"submit",className:"w-full py-4 bg-white text-green-600 rounded-2xl font-semibold text-lg hover:bg-white/90 transition-all shadow-lg",children:"Continue"})]}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsxs)("p",{className:"text-white/80",children:["Already have an account?"," ",(0,r.jsx)(c(),{href:"/auth/login",className:"text-white font-semibold underline",children:"Sign in"})]})})]})]})})}):(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 min-h-screen flex items-center justify-center p-4 relative",children:[(0,r.jsx)(f.L$,{variant:"auth"}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative z-10 w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center",children:[(0,r.jsx)(i.P.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"hidden lg:flex justify-center",children:(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,r.jsx)(p.XT,{src:"/ChatGPT Image Jul 8, 2025, 03_09_42 PM.png",alt:"Join BetterInterest - Start Your Savings Journey",width:600,height:400,intensity:"strong",className:"w-full h-auto mx-auto"}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 absolute bottom-6 left-6 bg-black/70 backdrop-blur-sm rounded-lg p-4 z-20",children:[(0,r.jsx)("h3",{className:"jsx-24f01c58ae8ac726 text-white font-semibold mb-1",children:"Start Your Journey"}),(0,r.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-green-400 text-sm",children:"Better interest rates await you"})]})]})}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 w-full max-w-md mx-auto lg:mx-0",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"text-center mb-8",children:[(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 inline-flex items-center justify-center mb-4",children:(0,r.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-3xl font-bold bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent",children:"BetterInterest"})}),(0,r.jsx)("h1",{className:"jsx-24f01c58ae8ac726 text-3xl font-display font-bold text-white mb-2 text-shadow",children:"Create Account"}),(0,r.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:"Join thousands saving for their future"})]}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"flex items-center justify-center mb-6",children:(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 "+"w-8 h-8 rounded-full flex items-center justify-center ".concat(v>=1?"bg-green-600 text-white":"bg-gray-700 text-gray-400"),children:v>1?(0,r.jsx)(h.YrT,{}):"1"}),(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 "+"w-16 h-1 ".concat(v>=2?"bg-green-600":"bg-gray-700")}),(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 "+"w-8 h-8 rounded-full flex items-center justify-center ".concat(v>=2?"bg-green-600 text-white":"bg-gray-700 text-gray-400"),children:"2"})]})}),(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"max-w-md mx-auto",children:[(0,r.jsxs)("form",{onSubmit:1===v?e=>{e.preventDefault(),P()}:R,className:"jsx-24f01c58ae8ac726 space-y-6",children:[1===v&&(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:[(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,r.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"First Name"}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,r.jsx)(h.JXP,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"text",value:S.firstName,onChange:e=>C("firstName",e.target.value),placeholder:"John",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,r.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Last Name"}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,r.jsx)(h.JXP,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"text",value:S.lastName,onChange:e=>C("lastName",e.target.value),placeholder:"Doe",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,r.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,r.jsx)(h.pHD,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"email",value:S.email,onChange:e=>C("email",e.target.value),placeholder:"<EMAIL>",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,r.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Phone Number"}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,r.jsx)(h.QFc,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"tel",value:S.phoneNumber,onChange:e=>C("phoneNumber",e.target.value),placeholder:"+234 ************",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"})]})]}),(0,r.jsx)(m.jn,{type:"submit",className:"w-full",children:"Continue"})]}),2===v&&(0,r.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:[(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,r.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,r.jsx)(h.F5$,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:s?"text":"password",value:S.password,onChange:e=>C("password",e.target.value),placeholder:"Create a strong password",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-12 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"}),(0,r.jsx)("button",{type:"button",onClick:()=>a(!s),className:"jsx-24f01c58ae8ac726 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:s?(0,r.jsx)(h._NO,{}):(0,r.jsx)(h.Vap,{})})]})]}),S.password&&(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 mt-2",children:[(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex items-center justify-between text-sm mb-1",children:[(0,r.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:"Password strength:"}),(0,r.jsx)("span",{className:"jsx-24f01c58ae8ac726 "+"font-medium ".concat(F.score<=1?"text-red-400":F.score<=3?"text-yellow-400":"text-green-400"),children:F.score<=1?"Weak":F.score<=3?"Medium":"Strong"})]}),(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 w-full bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{style:{width:"".concat(F.score/5*100,"%")},className:"jsx-24f01c58ae8ac726 "+"h-2 rounded-full transition-all duration-300 ".concat(F.score<=1?"bg-red-500":F.score<=3?"bg-yellow-500":"bg-green-500")})}),F.feedback.length>0&&(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 mt-2 text-xs text-gray-400",children:["Missing: ",F.feedback.join(", ")]})]})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726",children:[(0,r.jsx)("label",{className:"jsx-24f01c58ae8ac726 block text-sm font-medium text-gray-300 mb-2",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 relative",children:[(0,r.jsx)(h.F5$,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:l?"text":"password",value:S.confirmPassword,onChange:e=>C("confirmPassword",e.target.value),placeholder:"Confirm your password",required:!0,className:"jsx-24f01c58ae8ac726 w-full pl-10 pr-12 py-3 bg-gray-700/50 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"}),(0,r.jsx)("button",{type:"button",onClick:()=>b(!l),className:"jsx-24f01c58ae8ac726 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:l?(0,r.jsx)(h._NO,{}):(0,r.jsx)(h.Vap,{})})]})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex items-start space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"agreeToTerms",checked:S.agreeToTerms,onChange:e=>C("agreeToTerms",e.target.checked),required:!0,className:"jsx-24f01c58ae8ac726 mt-1 rounded border-gray-600 bg-gray-700 text-green-600 focus:ring-green-500"}),(0,r.jsxs)("label",{htmlFor:"agreeToTerms",className:"jsx-24f01c58ae8ac726 text-sm text-gray-400",children:["I agree to the"," ",(0,r.jsx)(c(),{href:"/terms",className:"text-green-400 hover:text-green-300",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(c(),{href:"/privacy",className:"text-green-400 hover:text-green-300",children:"Privacy Policy"})]})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex space-x-3",children:[(0,r.jsx)(m.rp,{type:"button",onClick:()=>{2===v&&j(1)},className:"flex-1",children:"Back"}),(0,r.jsx)(m.jn,{type:"submit",className:"flex-1",disabled:e,children:e?(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Creating..."]}):"Create Account"})]})]})]}),(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 mt-6 text-center",children:(0,r.jsxs)("p",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:["Already have an account?"," ",(0,r.jsx)(c(),{href:"/auth/login",className:"text-green-400 hover:text-green-300 font-medium",children:"Sign in here"})]})})]}),(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"mt-8 grid grid-cols-3 gap-4 text-center",children:[(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:[(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-green-400 text-sm",children:"\uD83D\uDD12"})}),(0,r.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-xs",children:"Bank-level Security"})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:[(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-green-400 text-sm",children:"\uD83D\uDCB0"})}),(0,r.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-xs",children:"High Interest Rates"})]}),(0,r.jsxs)("div",{className:"jsx-24f01c58ae8ac726 text-gray-400",children:[(0,r.jsx)("div",{className:"jsx-24f01c58ae8ac726 w-8 h-8 bg-green-600/20 rounded-lg flex items-center justify-center mx-auto mb-2",children:(0,r.jsx)("span",{className:"jsx-24f01c58ae8ac726 text-green-400 text-sm",children:"\uD83D\uDCF1"})}),(0,r.jsx)("p",{className:"jsx-24f01c58ae8ac726 text-xs",children:"Mobile First"})]})]})]})]}),(0,r.jsx)(n(),{id:"24f01c58ae8ac726",children:"@keyframes blob{0%{transform:translate(0px,0px)scale(1)}33%{transform:translate(30px,-50px)scale(1.1)}66%{transform:translate(-20px,20px)scale(.9)}100%{transform:translate(0px,0px)scale(1)}}.animate-blob.jsx-24f01c58ae8ac726{animation:blob 7s infinite}.animation-delay-2000.jsx-24f01c58ae8ac726{animation-delay:2s}.animation-delay-4000.jsx-24f01c58ae8ac726{animation-delay:4s}"})]})}},11518:(e,t,s)=>{"use strict";e.exports=s(82269).style},18980:(e,t,s)=>{Promise.resolve().then(s.bind(s,9609))},31246:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>c,jn:()=>o,rp:()=>l});var r=s(95155);s(12115);var a=s(68289);let n=()=>(0,r.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,r.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,r.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function i(e){let{children:t,onClick:s,href:i,variant:o="primary",size:l="md",disabled:c=!1,className:d="",type:u="button",icon:h}=e,m="\n    cursor-pointer font-bold font-sans transition-all duration-200 \n    border-2 border-transparent flex items-center justify-center\n    rounded-full relative overflow-hidden group\n    shadow-lg hover:shadow-xl active:shadow-inner\n    transform hover:-translate-y-1 active:translate-y-0\n    ".concat(c?"opacity-50 cursor-not-allowed":"","\n  "),f="\n    ".concat(m,"\n    ").concat({primary:"\n      bg-gradient-to-r from-green-400 to-green-600 \n      hover:from-green-500 hover:to-green-700\n      text-white border-green-500\n      active:border-green-600 active:shadow-inner\n    ",secondary:"\n      bg-gradient-to-r from-blue-400 to-blue-600 \n      hover:from-blue-500 hover:to-blue-700\n      text-white border-blue-500\n      active:border-blue-600 active:shadow-inner\n    ",outline:"\n      bg-transparent border-green-400 text-green-400\n      hover:bg-green-400 hover:text-black\n      active:border-green-500 active:shadow-inner\n    "}[o],"\n    ").concat({sm:"px-3 py-1.5 text-xs min-w-[80px]",md:"px-4 py-2 text-sm min-w-[100px]",lg:"px-6 py-3 text-base min-w-[120px]"}[l],"\n    ").concat(d,"\n  "),p=()=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,r.jsxs)("span",{className:"relative z-10 flex items-center justify-center",children:[(0,r.jsx)("span",{className:"text",children:t}),(h||"primary"===o)&&(0,r.jsx)(a.P.div,{className:"ml-2 text-current",whileHover:{x:3},transition:{type:"spring",stiffness:400,damping:10},children:h||(0,r.jsx)(n,{})})]})]});return i?(0,r.jsx)(a.P.a,{href:i,className:f,whileHover:{scale:1.02,y:-1},whileTap:{scale:.96,y:1},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(p,{})}):(0,r.jsx)(a.P.button,{type:u,onClick:s,disabled:c,className:f,whileHover:{scale:c?1:1.02,y:c?0:-1},whileTap:{scale:c?1:.96,y:+!c},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(p,{})})}function o(e){return(0,r.jsx)(i,{...e,variant:"primary"})}function l(e){return(0,r.jsx)(i,{...e,variant:"outline"})}let c=i},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},49697:(e,t,s)=>{"use strict";s.d(t,{HC:()=>u,XT:()=>m,co:()=>h,nv:()=>d});var r=s(95155),a=s(12115),n=s(66766),i=s(68289);let o={default:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.05)",shadow:"rgba(0, 0, 0, 0.24) 0px 8px 20px"},hero:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-xl",transform:"perspective(600px) rotateX(20deg) rotateZ(-8deg)",hoverTransform:"perspective(600px) rotateX(8deg) rotateY(15deg) rotateZ(-3deg)",shadow:"rgba(0, 0, 0, 0.3) -15px 25px 30px"},card:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.03)",shadow:"rgba(0, 0, 0, 0.2) 0px 6px 15px"},testimonial:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-full",transform:"none",hoverTransform:"scale(1.1)",shadow:"rgba(0, 0, 0, 0.15) 0px 4px 12px"},feature:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.02)",shadow:"rgba(0, 0, 0, 0.25) 0px 8px 25px"}},l={light:.5,medium:1,strong:1.5};function c(e){let{src:t,alt:s,width:c,height:d,className:u="",priority:h=!1,fill:m=!1,sizes:f,quality:p=75,objectFit:x="cover",variant:g="default",intensity:y="medium",...b}=e,[v,j]=(0,a.useState)(!1),[w,N]=(0,a.useState)(!0),[S,_]=(0,a.useState)(!1),F=o[g];return(l[y],S)?(0,r.jsx)("div",{className:"bg-gray-800 border border-gray-700 flex items-center justify-center ".concat(F.wrapper," ").concat(u),style:{width:m?"100%":c,height:m?"100%":d,transform:F.transform,boxShadow:F.shadow,transformStyle:"preserve-3d",transition:"transform 0.6s ease-out"},children:(0,r.jsxs)("div",{className:"text-center text-gray-400",children:[(0,r.jsx)("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})}),(0,r.jsx)("p",{className:"text-xs",children:"Image not found"})]})}):(0,r.jsxs)("div",{className:F.container,children:[w&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-800 animate-pulse ".concat(F.wrapper),style:{width:m?"100%":c,height:m?"100%":d,transform:F.transform,boxShadow:F.shadow,transformStyle:"preserve-3d",zIndex:10},children:(0,r.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-green-400 border-t-transparent rounded-full animate-spin"})})}),(0,r.jsxs)(i.P.div,{className:"".concat(F.wrapper," ").concat(u),style:{transformStyle:"preserve-3d",transform:F.transform,boxShadow:F.shadow,transition:"transform 0.6s ease-out, box-shadow 0.6s ease-out"},animate:{transform:v?F.hoverTransform:F.transform,boxShadow:v?F.shadow.replace(/rgba\(0, 0, 0, ([\d.]+)\)/,(e,t)=>"rgba(0, 0, 0, ".concat(1.3*parseFloat(t),")")):F.shadow},transition:{type:"spring",stiffness:300,damping:30},onMouseEnter:()=>j(!0),onMouseLeave:()=>j(!1),children:[(0,r.jsx)("div",{className:"absolute inset-0 z-10 pointer-events-none",style:{background:"linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 50%, rgba(0, 0, 0, 0.1) 100%)",opacity:v?.8:.4,transition:"opacity 0.3s ease"}}),(0,r.jsx)(n.default,{src:t,alt:s,width:m?void 0:c,height:m?void 0:d,fill:m,priority:h,quality:p,sizes:f,className:"\n            ".concat("cover"===x?"object-cover":"","\n            ").concat("contain"===x?"object-contain":"","\n            ").concat("fill"===x?"object-fill":"","\n            ").concat("none"===x?"object-none":"","\n            ").concat("scale-down"===x?"object-scale-down":"","\n            transition-transform duration-300\n          "),style:{opacity:+!w,transition:"opacity 0.3s ease"},onLoad:()=>{N(!1)},onError:()=>{N(!1),_(!0)},...b}),(0,r.jsx)(i.P.div,{className:"absolute inset-0 pointer-events-none",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)",transform:"translateX(-100%)"},animate:{transform:v?"translateX(100%)":"translateX(-100%)"},transition:{duration:.6,ease:"easeInOut"}})]})]})}function d(e){return(0,r.jsx)(c,{...e,variant:"hero"})}function u(e){return(0,r.jsx)(c,{...e,variant:"card"})}function h(e){return(0,r.jsx)(c,{...e,variant:"testimonial"})}function m(e){return(0,r.jsx)(c,{...e,variant:"feature"})}},64198:(e,t,s)=>{"use strict";s.d(t,{CustomToaster:()=>l,P0:()=>o,oR:()=>n.Ay});var r=s(95155),a=s(68289),n=s(13568),i=s(10351);let o={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,t)=>n.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function l(){return(0,r.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,r.jsx)(n.bv,{toast:e,children:t=>{let{icon:s,message:o}=t;return(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:s}),(0,r.jsx)("div",{className:"flex-1",children:o}),"loading"!==e.type&&(0,r.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,r.jsx)(i.yGN,{className:"w-4 h-4"})})]})}})})}},68375:()=>{},82269:(e,t,s)=>{"use strict";var r=s(49509);s(68375);var a=s(12115),n=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(a),i=void 0!==r&&r.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,a=t.optimizeForSpeed,n=void 0===a?i:a;c(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",c("boolean"==typeof n,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=n,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(i||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){if(c(o(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var s=this.getSheet();"number"!=typeof t&&(t=s.cssRules.length);try{s.insertRule(e,t)}catch(t){return i||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},s.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var s="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];c(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},s.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},s.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},s.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,s){return s?t=t.concat(Array.prototype.map.call(e.getSheetForTag(s).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},s.makeStyleTag=function(e,t,s){t&&c(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var a=document.head||document.getElementsByTagName("head")[0];return s?a.insertBefore(r,s):a.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},u={};function h(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return u[r]||(u[r]="jsx-"+d(e+"-"+s)),u[r]}function m(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var s=e+t;return u[s]||(u[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[s]}var f=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,a=t.optimizeForSpeed,n=void 0!==a&&a;this._sheet=r||new l({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),r&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var s=this.getIdAndRules(e),r=s.styleId,a=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var n=a.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=n,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return n.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var a=h(r,s);return{styleId:a,rules:Array.isArray(t)?t.map(function(e){return m(a,e)}):[m(a,t)]}}return{styleId:h(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=a.createContext(null);p.displayName="StyleSheetContext";var x=n.default.useInsertionEffect||n.default.useLayoutEffect,g="undefined"!=typeof window?new f:void 0;function y(e){var t=g||a.useContext(p);return t&&("undefined"==typeof window?t.add(e):x(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=y}},e=>{e.O(0,[844,5236,6874,6766,3568,8441,5964,7358],()=>e(e.s=18980)),_N_E=e.O()}]);