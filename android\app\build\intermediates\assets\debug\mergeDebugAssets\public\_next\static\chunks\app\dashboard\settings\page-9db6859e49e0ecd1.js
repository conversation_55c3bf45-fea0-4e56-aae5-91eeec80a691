(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4631],{87095:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(95155),r=t(12115),i=t(10351),n=t(11846),c=t(24630),l=t(13741),d=t(17703),o=t(68289);function x(e){let{checked:s,onChange:t,disabled:r=!1,size:i="md",color:n="green",label:c,description:l,className:d=""}=e,x={sm:{switch:"w-8 h-4",thumb:"w-3 h-3",translate:"translate-x-4"},md:{switch:"w-11 h-6",thumb:"w-5 h-5",translate:"translate-x-5"},lg:{switch:"w-14 h-8",thumb:"w-7 h-7",translate:"translate-x-6"}}[i],h=()=>{r||t(!s)},m=(0,a.jsx)("button",{type:"button",role:"switch","aria-checked":s,disabled:r,onClick:h,className:"\n        relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-green-500\n        ".concat(s?({green:"bg-green-600",blue:"bg-blue-600",purple:"bg-purple-600",red:"bg-red-600"})[n]:"bg-gray-600","\n        ").concat(r?"opacity-50 cursor-not-allowed":"cursor-pointer","\n        ").concat(x.switch,"\n        ").concat(d,"\n      "),children:(0,a.jsx)(o.P.span,{className:"\n          inline-block rounded-full bg-white shadow-lg transform transition-transform duration-200 ease-in-out\n          ".concat(x.thumb,"\n        "),animate:{x:s?x.translate.replace("translate-x-",""):"0"},transition:{type:"spring",stiffness:500,damping:30}})});return c||l?(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[m,(0,a.jsxs)("div",{className:"flex-1",children:[c&&(0,a.jsx)("label",{className:"text-sm font-medium text-white cursor-pointer",onClick:h,children:c}),l&&(0,a.jsx)("p",{className:"text-sm text-gray-400",children:l})]})]}):m}var h=t(30353),m=t(93915),u=t(64198);function g(){let[e,s]=(0,r.useState)(null),[t,o]=(0,r.useState)(!0),[g,j]=(0,r.useState)(!0),[f,p]=(0,r.useState)("en"),[w,N]=(0,r.useState)("NGN"),[b,y]=(0,r.useState)({twoFactorEnabled:!1,emailNotifications:!0,smsNotifications:!0,loginAlerts:!0}),[v,P]=(0,r.useState)({currentPassword:"",newPassword:"",confirmPassword:"",showPasswords:!1});(0,r.useEffect)(()=>{k()},[]);let k=async()=>{try{let e=await c.wy.getNotificationPreferences();s(e)}catch(e){console.error("Failed to load preferences:",e)}finally{o(!1)}},C=async(t,a)=>{try{if(e){let r={...e,[t]:a};await c.wy.updateNotificationPreferences(r),s(r),u.oR.success("Notification preferences updated")}}catch(e){u.oR.error("Failed to update preferences")}},S=async(t,a,r)=>{try{if(e){let i,n=e.typePreferences[t]||[];i=r?[...n,a]:n.filter(e=>e!==a),await c.wy.updateChannelPreference(t,i);let l={...e,typePreferences:{...e.typePreferences,[t]:i}};s(l),u.oR.success("Channel preferences updated")}}catch(e){u.oR.error("Failed to update channel preferences")}},E=async()=>{try{if(v.newPassword!==v.confirmPassword)return void u.oR.error("Passwords do not match");if(v.newPassword.length<8)return void u.oR.error("Password must be at least 8 characters");await c.y1.changePassword({currentPassword:v.currentPassword,newPassword:v.newPassword,confirmPassword:v.confirmPassword}),P({currentPassword:"",newPassword:"",confirmPassword:"",showPasswords:!1}),u.oR.success("Password changed successfully")}catch(e){u.oR.error("Failed to change password")}};return t?(0,a.jsx)(n.A,{title:"Settings",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})})}):(0,a.jsx)(n.A,{title:"Settings",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Settings"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Manage your account preferences and security"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(i.zd,{className:"mr-2"}),"Notification Preferences"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Email Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Receive notifications via email"})]}),(0,a.jsx)(x,{checked:(null==e?void 0:e.emailNotifications)||!1,onChange:e=>C("emailNotifications",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"SMS Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Receive notifications via SMS"})]}),(0,a.jsx)(x,{checked:(null==e?void 0:e.smsNotifications)||!1,onChange:e=>C("smsNotifications",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Push Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Receive push notifications"})]}),(0,a.jsx)(x,{checked:(null==e?void 0:e.pushNotifications)||!1,onChange:e=>C("pushNotifications",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"In-App Notifications"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Show notifications in the app"})]}),(0,a.jsx)(x,{checked:(null==e?void 0:e.inAppNotifications)||!1,onChange:e=>C("inAppNotifications",e)})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-700",children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-white mb-4",children:"Notification Types"}),(0,a.jsx)("div",{className:"space-y-3",children:e&&Object.entries(e.typePreferences).slice(0,6).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-white capitalize",children:s.replace(/_/g," ").toLowerCase()}),(0,a.jsxs)("div",{className:"flex space-x-4 text-sm",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.includes("EMAIL"),onChange:e=>S(s,"EMAIL",e.target.checked),className:"rounded border-gray-600 bg-gray-700 text-green-600"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Email"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.includes("SMS"),onChange:e=>S(s,"SMS",e.target.checked),className:"rounded border-gray-600 bg-gray-700 text-green-600"}),(0,a.jsx)("span",{className:"text-gray-400",children:"SMS"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:t.includes("PUSH"),onChange:e=>S(s,"PUSH",e.target.checked),className:"rounded border-gray-600 bg-gray-700 text-green-600"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Push"})]})]})]},s)})})]})]}),(0,a.jsxs)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(i.pcC,{className:"mr-2"}),"Security Settings"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Two-Factor Authentication"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Add an extra layer of security"})]}),(0,a.jsx)(x,{checked:b.twoFactorEnabled,onChange:e=>y({...b,twoFactorEnabled:e})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Login Alerts"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Get notified of new logins"})]}),(0,a.jsx)(x,{checked:b.loginAlerts,onChange:e=>y({...b,loginAlerts:e})})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-700",children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-white mb-4",children:"Change Password"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(m.p,{label:"Current Password",type:v.showPasswords?"text":"password",value:v.currentPassword,onChange:e=>P({...v,currentPassword:e.target.value}),placeholder:"Enter current password"})}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(m.p,{label:"New Password",type:v.showPasswords?"text":"password",value:v.newPassword,onChange:e=>P({...v,newPassword:e.target.value}),placeholder:"Enter new password"})}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)(m.p,{label:"Confirm New Password",type:v.showPasswords?"text":"password",value:v.confirmPassword,onChange:e=>P({...v,confirmPassword:e.target.value}),placeholder:"Confirm new password"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"showPasswords",checked:v.showPasswords,onChange:e=>P({...v,showPasswords:e.target.checked}),className:"rounded border-gray-600 bg-gray-700 text-green-600"}),(0,a.jsx)("label",{htmlFor:"showPasswords",className:"text-sm text-gray-400",children:"Show passwords"})]}),(0,a.jsxs)(l.$n,{onClick:E,className:"w-full bg-green-600 hover:bg-green-700",disabled:!v.currentPassword||!v.newPassword||!v.confirmPassword,children:[(0,a.jsx)(i.F5$,{className:"mr-2"}),"Change Password"]})]})]})]}),(0,a.jsxs)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(i.VSk,{className:"mr-2"}),"App Preferences"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Dark Mode"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Use dark theme"})]}),(0,a.jsx)(x,{checked:g,onChange:j})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Language"}),(0,a.jsx)(h.l,{value:f,onChange:e=>p(e.target.value),options:[{value:"en",label:"English"},{value:"yo",label:"Yoruba"},{value:"ig",label:"Igbo"},{value:"ha",label:"Hausa"}]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Currency"}),(0,a.jsx)(h.l,{value:w,onChange:e=>N(e.target.value),options:[{value:"NGN",label:"Nigerian Naira (₦)"},{value:"USD",label:"US Dollar ($)"},{value:"EUR",label:"Euro (€)"},{value:"GBP",label:"British Pound (\xa3)"}]})]})]})]}),(0,a.jsxs)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,a.jsx)(i.Vap,{className:"mr-2"}),"Privacy Settings"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Profile Visibility"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Make your profile visible to others"})]}),(0,a.jsx)(x,{checked:!1,onChange:()=>{}})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Activity Status"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Show when you're active"})]}),(0,a.jsx)(x,{checked:!0,onChange:()=>{}})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Data Analytics"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Help improve our services"})]}),(0,a.jsx)(x,{checked:!0,onChange:()=>{}})]})]}),(0,a.jsxs)("div",{className:"mt-6 pt-6 border-t border-gray-700",children:[(0,a.jsx)("h4",{className:"text-md font-semibold text-white mb-4",children:"Quiet Hours"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-white",children:"Enable Quiet Hours"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Pause notifications during specified hours"})]}),(0,a.jsx)(x,{checked:(null==e?void 0:e.quietHours.enabled)||!1,onChange:t=>{if(e){let a={...e,quietHours:{...e.quietHours,enabled:t}};c.wy.updateNotificationPreferences(a),s(a)}}})]}),(null==e?void 0:e.quietHours.enabled)&&(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"Start Time"}),(0,a.jsx)(m.p,{type:"time",value:e.quietHours.startTime,onChange:t=>{s({...e,quietHours:{...e.quietHours,startTime:t.target.value}})}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-2",children:"End Time"}),(0,a.jsx)(m.p,{type:"time",value:e.quietHours.endTime,onChange:t=>{s({...e,quietHours:{...e.quietHours,endTime:t.target.value}})}})]})]})]})]})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsxs)(l.$n,{onClick:()=>u.oR.success("Settings saved successfully"),className:"bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(i.Bc_,{className:"mr-2"}),"Save All Settings"]})})]})})}},93733:(e,s,t)=>{Promise.resolve().then(t.bind(t,87095))}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,1846,411,8441,5964,7358],()=>e(e.s=93733)),_N_E=e.O()}]);