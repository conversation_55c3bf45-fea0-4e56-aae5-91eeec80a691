"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3289],{13741:(e,t,r)=>{r.d(t,{$n:()=>s,Ay:()=>c,K0:()=>i});var a=r(95155),o=r(12115),n=r(68289);function s(e){let{children:t,variant:r="primary",size:s="md",loading:i=!1,leftIcon:c,rightIcon:d,fullWidth:l=!1,href:h,className:u="",disabled:p,effect3D:m=!0,...g}=e,f="\n    ".concat("\n    relative inline-flex items-center justify-center font-semibold font-inter\n    transition-all duration-200 ease-in-out transform-gpu\n    focus:outline-none focus:ring-2 focus:ring-brand focus:ring-offset-2\n    disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\n    overflow-hidden group\n    ".concat(l?"w-full":"","\n  "),"\n    ").concat({primary:"\n      bg-brand hover:bg-brand-dark text-white\n      shadow-lg hover:shadow-xl hover:shadow-brand/25\n      border-2 border-brand hover:border-brand-dark\n      active:border-brand-dark active:shadow-inner\n      ".concat(m?"hover:scale-105 active:scale-95":"","\n    "),secondary:"\n      bg-theme-secondary hover:bg-opacity-80 text-theme\n      border-2 border-theme hover:border-brand\n      active:border-brand active:shadow-inner\n      ".concat(m?"hover:scale-105 active:scale-95":"","\n    "),outline:"\n      bg-transparent border-2 border-brand text-brand\n      hover:bg-brand hover:text-white\n      active:border-brand-dark active:shadow-inner\n      ".concat(m?"hover:scale-105 active:scale-95":"","\n    "),ghost:"\n      bg-transparent text-brand hover:text-brand-dark\n      hover:bg-brand hover:bg-opacity-10\n      ".concat(m?"hover:scale-105 active:scale-95":"","\n    "),danger:"\n      bg-red-600 hover:bg-red-700 text-white\n      shadow-lg hover:shadow-xl hover:shadow-red-500/25\n      border-2 border-red-600 hover:border-red-700\n      active:border-red-700 active:shadow-inner\n      ".concat(m?"hover:scale-105 active:scale-95":"","\n    ")}[r],"\n    ").concat({sm:"px-2 py-1 text-xs rounded-md gap-1 min-w-[60px]",md:"px-3 py-1.5 text-sm rounded-lg gap-1.5 min-w-[80px]",lg:"px-4 py-2 text-base rounded-lg gap-2 min-w-[100px]",xl:"px-6 py-3 text-lg rounded-xl gap-2 min-w-[120px]"}[s],"\n    ").concat(u,"\n  "),w=(e,t)=>o.isValidElement(e)?(0,a.jsx)("span",{className:t,children:e}):"function"==typeof e?(0,a.jsx)(e,{className:"".concat(t," text-brand")}):null,b=()=>(0,a.jsxs)(a.Fragment,{children:[m&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-inherit opacity-0 group-hover:opacity-100 transition-opacity duration-200"}),m&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700"}),(0,a.jsxs)("span",{className:"relative z-10 flex items-center gap-inherit",children:[i&&(0,a.jsx)(n.P.div,{className:"w-4 h-4 border-2 border-current border-t-transparent rounded-full",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),c&&!i&&w(c,""),t,d&&w(d,"")]})]});return h?(0,a.jsx)(n.P.a,{href:h,className:f,whileHover:{scale:p||i?1:m?1.02:1.01,y:p||i?0:-1,boxShadow:p||i?void 0:"0 4px 12px rgba(34, 197, 94, 0.2)"},whileTap:{scale:p||i?1:.96,y:p||i?0:1},transition:{type:"spring",stiffness:400,damping:17},children:(0,a.jsx)(b,{})}):(0,a.jsx)(n.P.button,{className:f,disabled:p||i,whileHover:{scale:p||i?1:m?1.02:1.01,y:p||i?0:-1,boxShadow:p||i?void 0:"0 4px 12px rgba(34, 197, 94, 0.2)"},whileTap:{scale:p||i?1:.96,y:p||i?0:1},transition:{type:"spring",stiffness:400,damping:17},onClick:g.onClick,type:g.type,form:g.form,formAction:g.formAction,formEncType:g.formEncType,formMethod:g.formMethod,formNoValidate:g.formNoValidate,formTarget:g.formTarget,name:g.name,value:g.value,children:(0,a.jsx)(b,{})})}function i(e){let{icon:t,variant:r="ghost",size:o="md",disabled:s=!1,onClick:i,className:c="",tooltip:d,effect3D:l=!0}=e,h="\n    ".concat("\n    relative inline-flex items-center justify-center\n    transition-all duration-200 ease-in-out transform-gpu\n    focus:outline-none focus:ring-2 focus:ring-brand focus:ring-offset-2\n    disabled:opacity-50 disabled:cursor-not-allowed\n    group overflow-hidden\n  ","\n    ").concat({sm:"p-1 rounded-md",md:"p-1.5 rounded-lg",lg:"p-2 rounded-xl"}[o],"\n    ").concat({primary:"\n      bg-brand hover:bg-brand-dark text-white\n      ".concat(l?"hover:scale-110 active:scale-95":"","\n    "),secondary:"\n      bg-theme-secondary hover:bg-opacity-80 text-theme\n      border border-theme hover:border-brand\n      ".concat(l?"hover:scale-110 active:scale-95":"","\n    "),outline:"\n      bg-transparent hover:bg-brand hover:text-white text-brand\n      border border-brand hover:border-brand-dark\n      ".concat(l?"hover:scale-110 active:scale-95":"","\n    "),ghost:"\n      bg-transparent hover:bg-brand hover:bg-opacity-10 text-brand\n      hover:text-brand-dark\n      ".concat(l?"hover:scale-110 active:scale-95":"","\n    ")}[r],"\n    ").concat(c,"\n  ");return(0,a.jsxs)(n.P.button,{onClick:i,disabled:s,className:h,whileHover:{scale:s?1:l?1.05:1.02,y:s?0:-1,rotate:s?0:3*!!l},whileTap:{scale:s?1:.98,y:0},transition:{type:"spring",stiffness:400,damping:17},title:d,children:[l&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-inherit opacity-0 group-hover:opacity-100 transition-opacity duration-200"}),(0,a.jsx)(t,{className:"".concat({sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"}[o]," text-brand relative z-10")})]})}let c=s},96365:(e,t,r)=>{r.d(t,{authService:()=>f});class a extends Error{static fromResponse(e){return new a(e.message||e.error,500,e.code,e.details,e.timestamp)}toJSON(){return{name:this.name,message:this.message,statusCode:this.statusCode,code:this.code,details:this.details,timestamp:this.timestamp}}constructor(e,t,r,a,o){super(e),this.statusCode=t,this.code=r,this.details=a,this.timestamp=o,this.name="ApiError",this.timestamp=o||new Date().toISOString()}}class o extends a{constructor(e,t){super(e,422,"VALIDATION_ERROR",t),this.validationErrors=t,this.name="ValidationError"}}class n extends a{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class s extends a{constructor(e="Access denied"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class i extends a{constructor(e="Resource not found"){super(e,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class c extends a{constructor(e="Network error occurred"){super(e,0,"NETWORK_ERROR"),this.name="NetworkError"}}class d extends a{constructor(e="Request timeout"){super(e,408,"TIMEOUT_ERROR"),this.name="TimeoutError"}}class l extends a{constructor(e="Internal server error"){super(e,500,"SERVER_ERROR"),this.name="ServerError"}}class h extends a{constructor(e="Service temporarily unavailable"){super(e,503,"SERVICE_UNAVAILABLE"),this.name="ServiceUnavailableError"}}let u=async e=>{let t;try{t=await e.json()}catch(r){t={error:"Unknown error occurred",message:"HTTP ".concat(e.status,": ").concat(e.statusText)}}switch(e.status){case 400:if(t.errors)throw new o(t.message||"Validation failed",t.errors);throw new a(t.message||t.error||"Bad request",400,t.code,t.details);case 401:throw new n(t.message||t.error);case 403:throw new s(t.message||t.error);case 404:throw new i(t.message||t.error);case 408:throw new d(t.message||t.error);case 422:throw new o(t.message||"Validation failed",t.errors||t.details||{});case 500:throw new l(t.message||t.error);case 503:throw new h(t.message||t.error);default:throw new a(t.message||t.error||"Request failed",e.status,t.code,t.details)}},p=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,o=new AbortController,n=setTimeout(()=>o.abort(),r);try{let r=await fetch(e,{...t,signal:o.signal,headers:{"Content-Type":"application/json",...t.headers}});clearTimeout(n),r.ok||await u(r);let s=r.headers.get("content-type");if(!s||!s.includes("application/json"))return{};let i=await r.json();if(!1===i.success)throw a.fromResponse(i);return i.data||i}catch(e){if(clearTimeout(n),e instanceof a)throw e;throw(e=>{if("AbortError"===e.name)throw new d("Request was cancelled");if("TypeError"===e.name&&e.message.includes("fetch"))throw new c("Network connection failed");throw new c(e.message||"Network error occurred")})(e)}},m="http://localhost:8080";class g{getAuthHeaders(){let e=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}}async login(e){var t,r;let a=await p("".concat(m,"/api/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});console.log("LOGIN RESPONSE:",a);let o=(null==a||null==(t=a.data)?void 0:t.token)||(null==a?void 0:a.token),n=(null==a||null==(r=a.data)?void 0:r.refreshToken)||(null==a?void 0:a.refreshToken);return o?(console.log("Saving auth_token:",o),localStorage.setItem("auth_token",o)):console.warn("No token found in login response:",a),n&&localStorage.setItem("refresh_token",n),a}async signup(e){var t,r,a,o;let n=await p("".concat(m,"/api/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=(null==n||null==(t=n.data)?void 0:t.token)||(null==n?void 0:n.token),i=(null==n||null==(r=n.data)?void 0:r.refreshToken)||(null==n?void 0:n.refreshToken),c=(null==n||null==(a=n.data)?void 0:a.user)||(null==n?void 0:n.user);return s&&localStorage.setItem("auth_token",s),i&&localStorage.setItem("refresh_token",i),{user:c,token:s,refreshToken:i,expiresIn:(null==n||null==(o=n.data)?void 0:o.expiresIn)||(null==n?void 0:n.expiresIn)||3600}}async adminLogin(e){let t=await fetch("".concat(m,"/auth/admin/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Admin login failed");let r=await t.json();return localStorage.setItem("auth_token",r.token),r.refreshToken&&localStorage.setItem("refresh_token",r.refreshToken),r}async logout(){try{await fetch("".concat(m,"/auth/logout"),{method:"POST",headers:this.getAuthHeaders()})}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token")}}async getCurrentUser(){let e=await fetch("".concat(m,"/api/auth/user-info"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to get current user");return(await e.json()).user}async refreshToken(){let e=localStorage.getItem("refresh_token");if(!e)throw Error("No refresh token available");let t=await fetch("".concat(m,"/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error("Token refresh failed");let r=await t.json();return localStorage.setItem("auth_token",r.data.token),r.data.refreshToken&&localStorage.setItem("refresh_token",r.data.refreshToken),{success:r.success,token:r.data.token,refreshToken:r.data.refreshToken,expiresIn:r.data.expiresIn||3600}}async requestPasswordReset(e){let t=await fetch("".concat(m,"/auth/password-reset/request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset request failed")}async confirmPasswordReset(e){let t=await fetch("".concat(m,"/auth/password-reset/confirm"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset failed")}async changePassword(e){let t=await fetch("".concat(m,"/auth/change-password"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password change failed")}getStoredToken(){return localStorage.getItem("auth_token")}isTokenExpired(e){try{let t=JSON.parse(atob(e.split(".")[1]));return 1e3*t.exp<Date.now()}catch(e){return!0}}}let f=new g}}]);