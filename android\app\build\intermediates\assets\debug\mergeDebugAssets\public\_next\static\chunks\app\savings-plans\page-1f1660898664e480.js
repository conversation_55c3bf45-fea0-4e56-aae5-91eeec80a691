(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8063],{5856:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});var n=t(95155),r=t(35695),u=t(12115);function a(){let e=(0,r.useRouter)();return(0,u.useEffect)(()=>{e.push("/dashboard/savings-plans")},[e]),(0,n.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("p",{className:"text-green-400 text-lg",children:"Redirecting to Savings Plans..."})})})}},27028:(e,s,t)=>{Promise.resolve().then(t.bind(t,5856))},35695:(e,s,t)=>{"use strict";var n=t(18999);t.o(n,"usePathname")&&t.d(s,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(s,{useRouter:function(){return n.useRouter}})}},e=>{e.O(0,[8441,5964,7358],()=>e(e.s=27028)),_N_E=e.O()}]);