"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2266],{62266:(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var l=a(95155),r=a(68289),s=a(35695),c=a(10351);function n(e){let{onClick:t,href:a,className:n="",variant:h="default",showText:o=!1}=e,u=(0,s.useRouter)();return(0,l.jsxs)(r.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{t?t():a?u.push(a):u.back()},className:"".concat("flex items-center justify-center transition-all duration-200 active:scale-95"," ").concat({default:"w-10 h-10 bg-white/10 backdrop-blur-sm rounded-full text-white hover:bg-white/20",minimal:"w-8 h-8 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-full",floating:"w-12 h-12 bg-white shadow-lg rounded-full text-gray-700 hover:shadow-xl hover:bg-gray-50"}[h]," ").concat(n),children:[(0,l.jsx)(c.kRp,{className:"w-5 h-5"}),o&&(0,l.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Back"})]})}}}]);