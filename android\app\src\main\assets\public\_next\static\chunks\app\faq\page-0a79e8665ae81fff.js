(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7505],{358:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(95155),n=a(12115),i=a(68289),s=a(60760),o=a(6874),l=a.n(o),c=a(10351),d=a(585),g=a(48016),x=a(19958);let p=[{id:"1",question:"How does BetterInterest work?",answer:"BetterInterest is a digital savings platform that helps you earn higher interest rates on your savings. You can create individual or group savings plans, set targets, and earn competitive interest rates while your money grows safely.",category:"General"},{id:"2",question:"What interest rates do you offer?",answer:"Our interest rates range from 8.5% to 15% annually, depending on your savings plan type, duration, and amount. Premium and Elite plans offer higher rates for larger deposits and longer terms.",category:"Interest & Returns"},{id:"3",question:"Is my money safe with BetterInterest?",answer:"Yes, your funds are secured through bank-grade security measures, encryption, and regulatory compliance. We partner with licensed financial institutions to ensure your money is protected.",category:"Security"},{id:"4",question:"Can I withdraw my money anytime?",answer:"You can withdraw your money, but early withdrawal may incur penalties depending on your savings plan terms. Emergency withdrawals are available with reduced penalties for urgent situations.",category:"Withdrawals"},{id:"5",question:"How do group savings work?",answer:"Group savings allow you to save with friends, family, or colleagues towards common goals. Members contribute regularly, and the group earns higher interest rates. You can create or join existing groups.",category:"Group Savings"},{id:"6",question:"What payment methods do you accept?",answer:"We accept bank transfers, debit cards, and mobile money payments. You can link multiple payment methods to your account for convenient deposits.",category:"Payments"},{id:"7",question:"How do I verify my account (KYC)?",answer:"Account verification requires uploading a valid government-issued ID, proof of address, and a selfie. The process typically takes 24-48 hours for approval.",category:"Account"},{id:"8",question:"Are there any fees?",answer:"We charge minimal fees for certain services like external transfers (1.5%) and early withdrawals (5%). Account maintenance and deposits are free.",category:"Fees"},{id:"9",question:"How is interest calculated and paid?",answer:"Interest is calculated daily and compounded monthly. Payments are made directly to your account at the end of each month or at maturity, depending on your plan.",category:"Interest & Returns"},{id:"10",question:"Can I have multiple savings plans?",answer:"Yes, you can create multiple individual savings plans and participate in multiple group savings simultaneously. Each plan can have different goals and terms.",category:"General"}],u=[{name:"All",icon:c.lrG,color:"text-green-400"},{name:"General",icon:c.lrG,color:"text-blue-400"},{name:"Interest & Returns",icon:c.z8N,color:"text-green-400"},{name:"Security",icon:c.pcC,color:"text-red-400"},{name:"Group Savings",icon:c.cfS,color:"text-purple-400"},{name:"Payments",icon:c.lZI,color:"text-yellow-400"},{name:"Account",icon:c.VSk,color:"text-gray-400"}];function m(){let[e,t]=(0,n.useState)(""),[a,o]=(0,n.useState)("All"),[m,h]=(0,n.useState)([]),y=p.filter(t=>{let r=t.question.toLowerCase().includes(e.toLowerCase())||t.answer.toLowerCase().includes(e.toLowerCase()),n="All"===a||t.category===a;return r&&n});return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white",children:[(0,r.jsx)(d.L$,{variant:"default"}),(0,r.jsx)("header",{className:"relative z-50 px-6 py-4",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsx)(l(),{href:"/",className:"flex items-center",children:(0,r.jsx)(g.UU,{size:"md",variant:"light"})}),(0,r.jsxs)(l(),{href:"/",className:"inline-flex items-center text-gray-400 hover:text-white transition-colors",children:[(0,r.jsx)(c.kRp,{className:"mr-2"}),"Back to Home"]})]})}),(0,r.jsxs)("main",{className:"relative z-10 max-w-6xl mx-auto px-6 py-12",children:[(0,r.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center mb-12",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-4",children:[(0,r.jsx)(c.lrG,{className:"text-green-400 text-4xl mr-3"}),(0,r.jsx)("h1",{className:"text-4xl font-bold",children:"Frequently Asked Questions"})]}),(0,r.jsx)("p",{className:"text-gray-300 text-lg max-w-2xl mx-auto",children:"Find answers to common questions about BetterInterest savings platform"})]}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"mb-8",children:(0,r.jsxs)(x.uk,{className:"bg-gray-900/50 border-gray-800 p-6",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"relative max-w-md mx-auto",children:[(0,r.jsx)(c.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search FAQs...",value:e,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white"})]})}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:u.map(e=>{let t=e.icon,n=a===e.name;return(0,r.jsxs)("button",{onClick:()=>o(e.name),className:"flex items-center px-4 py-2 rounded-lg transition-all duration-200 ".concat(n?"bg-green-600 text-white":"bg-gray-800 text-gray-300 hover:bg-gray-700"),children:[(0,r.jsx)(t,{className:"mr-2 ".concat(n?"text-white":e.color)}),e.name]},e.name)})})]})}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"space-y-4",children:0===y.length?(0,r.jsxs)(x.uk,{className:"bg-gray-900/50 border-gray-800 p-8 text-center",children:[(0,r.jsx)(c.lrG,{className:"text-gray-500 text-4xl mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-400 mb-2",children:"No FAQs found"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Try adjusting your search or selecting a different category"})]}):y.map((e,t)=>{let a=m.includes(e.id);return(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:(0,r.jsxs)(x.uk,{className:"bg-gray-900/50 border-gray-800 overflow-hidden",children:[(0,r.jsx)("button",{onClick:()=>{var t;return t=e.id,void h(e=>e.includes(t)?e.filter(e=>e!==t):[...e,t])},className:"w-full p-6 text-left hover:bg-gray-800/30 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("div",{className:"flex items-center mb-2",children:(0,r.jsx)("span",{className:"text-xs bg-green-600 text-white px-2 py-1 rounded-full mr-3",children:e.category})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:e.question})]}),(0,r.jsx)(i.P.div,{animate:{rotate:180*!!a},transition:{duration:.2},className:"ml-4",children:(0,r.jsx)(c.fK4,{className:"text-gray-400 text-xl"})})]})}),(0,r.jsx)(s.N,{children:a&&(0,r.jsx)(i.P.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},className:"overflow-hidden",children:(0,r.jsx)("div",{className:"px-6 pb-6 border-t border-gray-700",children:(0,r.jsx)("p",{className:"text-gray-300 leading-relaxed pt-4",children:e.answer})})})})]})},e.id)})}),(0,r.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},className:"mt-12",children:(0,r.jsxs)(x.uk,{className:"bg-gradient-to-r from-green-900/50 to-blue-900/50 border-green-800 p-8 text-center",children:[(0,r.jsx)(c.lrG,{className:"text-green-400 text-4xl mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:"Still have questions?"}),(0,r.jsx)("p",{className:"text-gray-300 mb-6",children:"Our support team is here to help you 24/7"}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(l(),{href:"/contact",className:"inline-flex items-center justify-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors",children:[(0,r.jsx)(c.lrG,{className:"mr-2"}),"Contact Support"]}),(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"inline-flex items-center justify-center px-6 py-3 bg-gray-800 hover:bg-gray-700 text-white rounded-lg transition-colors",children:"Email Us"})]})]})})]})]})}},585:(e,t,a)=>{"use strict";a.d(t,{L$:()=>s});var r=a(95155);a(12115);var n=a(68289);let i=[{left:10,top:20},{left:25,top:15},{left:40,top:30},{left:55,top:10},{left:70,top:25},{left:85,top:35},{left:15,top:45},{left:30,top:55},{left:45,top:40},{left:60,top:50},{left:75,top:60},{left:90,top:45},{left:5,top:70},{left:20,top:80},{left:35,top:65},{left:50,top:75},{left:65,top:85},{left:80,top:70},{left:95,top:80},{left:10,top:90},{left:25,top:5},{left:40,top:85},{left:55,top:95},{left:70,top:5},{left:85,top:15},{left:15,top:25},{left:30,top:35},{left:45,top:45},{left:60,top:55},{left:75,top:65},{left:90,top:75},{left:5,top:85},{left:20,top:95},{left:35,top:5},{left:50,top:15},{left:65,top:25},{left:80,top:35},{left:95,top:45}];function s(e){let{variant:t="default",className:a=""}=e,s={default:{gradient:"bg-gradient-to-br from-gray-900 via-green-900 to-black",particles:30,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]},auth:{gradient:"bg-gradient-to-br from-gray-900 via-black to-gray-900",particles:20,colors:["bg-green-500","bg-orange-500","bg-yellow-500"]},dashboard:{gradient:"bg-gradient-to-br from-gray-900 via-gray-800 to-black",particles:15,colors:["bg-green-400","bg-orange-400","bg-yellow-400"]}}[t];return(0,r.jsxs)("div",{className:"fixed inset-0 z-0 ".concat(a),children:[(0,r.jsx)("div",{className:"absolute inset-0 ".concat(s.gradient," animate-gradient")}),(0,r.jsxs)("div",{className:"absolute inset-0 overflow-hidden",children:[(0,r.jsx)(n.P.div,{className:"absolute -top-40 -right-40 w-80 h-80 bg-orange-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,30,-20,0],y:[0,-50,20,0],scale:[1,1.1,.9,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut"}}),(0,r.jsx)(n.P.div,{className:"absolute -bottom-40 -left-40 w-80 h-80 bg-yellow-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,-30,20,0],y:[0,50,-20,0],scale:[1,.9,1.1,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:2}}),(0,r.jsx)(n.P.div,{className:"absolute top-40 left-40 w-80 h-80 bg-amber-500 rounded-full mix-blend-multiply filter blur-xl opacity-20",animate:{x:[0,20,-30,0],y:[0,-30,40,0],scale:[1,1.2,.8,1]},transition:{duration:7,repeat:1/0,ease:"easeInOut",delay:4}})]}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",children:[...Array(s.particles)].map((e,t)=>{let a=i[t%i.length];return(0,r.jsx)(n.P.div,{className:"absolute w-1 h-1 ".concat(s.colors[t%s.colors.length]," rounded-full"),style:{left:"".concat(a.left,"%"),top:"".concat(a.top,"%")},animate:{y:[0,-20,0],opacity:[.2,.8,.2],scale:[1,1.5,1]},transition:{duration:3+t%3,repeat:1/0,delay:t%4*.5,ease:"easeInOut"}},t)})}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-black/30"})]})}},19958:(e,t,a)=>{"use strict";a.d(t,{YO:()=>g,uk:()=>d});var r=a(95155),n=a(12115),i=a(8619),s=a(37602),o=a(58829),l=a(68289),c=a(57740);function d(e){let{children:t,className:a="",intensity:d="medium",glowEffect:g=!0,hoverScale:x=!0,borderGradient:p=!1,elevation:u=2,onClick:m}=e,{theme:h}=(0,c.DP)(),y=(0,c.Yx)(h),b=(0,n.useRef)(null),f=(0,i.d)(0),v=(0,i.d)(0),w=(0,s.z)(f),j=(0,s.z)(v),N=(0,o.G)(j,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),k=(0,o.G)(w,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),P=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a={1:"light"===h?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===h?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===h?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)",4:"light"===h?"0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)":"0 14px 28px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.8)",5:"light"===h?"0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)":"0 19px 38px rgba(0, 0, 0, 0.7), 0 15px 12px rgba(0, 0, 0, 0.9)"};return a[t?Math.min(e+2,5):e]||a[2]},I="\n    relative rounded-xl overflow-hidden transition-all duration-300 group\n    ".concat(y.bg.card,"\n    ").concat(y.border.primary,"\n    ").concat(p?"border-2 border-transparent bg-gradient-to-r from-green-400/20 to-blue-400/20 p-[1px]":"border","\n    ").concat(a,"\n  "),S=(0,r.jsxs)(l.P.div,{ref:b,className:I,style:{rotateY:k,rotateX:N,transformStyle:"preserve-3d",boxShadow:P(u)},onMouseMove:e=>{if(!b.current)return;let t=b.current.getBoundingClientRect(),a=t.width,r=t.height,n=(e.clientX-t.left)/a-.5,i=(e.clientY-t.top)/r-.5;f.set(n),v.set(i)},onMouseLeave:()=>{f.set(0),v.set(0)},whileHover:x?{scale:1.02,boxShadow:P(u,!0),transition:{duration:.2,ease:"easeOut"}}:{},transition:{type:"spring",stiffness:300,damping:30},onClick:m,children:[g&&(0,r.jsx)(l.P.div,{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl blur-xl",initial:{scale:.8},whileHover:{scale:1.1},transition:{duration:.3}}),(0,r.jsx)(l.P.div,{className:"absolute inset-0 bg-green-400/10 opacity-0 group-hover:opacity-20 rounded-xl",initial:{scale:0},whileHover:{scale:1},transition:{duration:.4,ease:"easeOut"}}),(0,r.jsx)("div",{className:"relative z-10 ".concat(p?"".concat(y.bg.card," rounded-xl"):""),children:t}),(0,r.jsx)(l.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ".concat("dark"===h?"via-white/5":"via-white/10"),initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.6,ease:"easeInOut"}})]});return(0,r.jsx)("div",{className:"group",children:S})}function g(e){let{title:t,value:a,subtitle:n,icon:i,color:s="green",className:o=""}=e,{theme:l}=(0,c.DP)(),g=(0,c.Yx)(l);return(0,r.jsx)(d,{className:"p-6 ".concat(o),glowEffect:!0,borderGradient:!0,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium ".concat(g.text.secondary),children:t}),(0,r.jsx)("p",{className:"text-2xl font-bold ".concat(g.text.primary," mt-1"),children:a}),n&&(0,r.jsx)("p",{className:"text-xs ".concat(g.text.tertiary," mt-1"),children:n})]}),i&&(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat({green:"from-green-400 to-green-600",blue:"from-blue-400 to-blue-600",purple:"from-purple-400 to-purple-600",yellow:"from-yellow-400 to-yellow-600"}[s]," flex items-center justify-center"),children:(0,r.jsx)(i,{className:"w-6 h-6 text-white"})})]})})}},48016:(e,t,a)=>{"use strict";a.d(t,{Qh:()=>c,UU:()=>l});var r=a(95155),n=a(68289),i=a(66766);let s={sm:{container:"h-8",text:"text-lg",icon:"w-6 h-6"},md:{container:"h-10",text:"text-xl",icon:"w-8 h-8"},lg:{container:"h-12",text:"text-2xl",icon:"w-10 h-10"},xl:{container:"h-16",text:"text-3xl",icon:"w-12 h-12"}},o={light:"text-white",dark:"text-gray-900",gradient:"bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"};function l(e){let{size:t="md",variant:a="gradient",showIcon:l=!0,className:c=""}=e,d=s[t],g=o[a];return(0,r.jsxs)(n.P.div,{className:"flex items-center space-x-2 ".concat(d.container," ").concat(c),whileHover:{scale:1.05},transition:{type:"spring",stiffness:400,damping:10},children:[l&&(0,r.jsx)(n.P.div,{className:"".concat(d.icon," flex items-center justify-center relative"),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(i.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(d.icon.split(" ")[0].replace("w-","")),height:4*parseInt(d.icon.split(" ")[1].replace("h-","")),className:"".concat(d.icon," object-contain"),priority:!0})}),(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsxs)(n.P.h1,{className:"font-inter font-bold leading-tight ".concat(d.text," ").concat(g," relative"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},style:{letterSpacing:"0.02em",textShadow:"0 0 20px rgba(255, 255, 255, 0.1)"},children:[(0,r.jsx)("span",{className:"text-white relative",style:{textShadow:"0 0 15px rgba(255, 255, 255, 0.2)",fontWeight:"700"},children:"Better"}),(0,r.jsx)("span",{className:"text-green-400 ml-1 relative",style:{textShadow:"0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2)",fontWeight:"800",letterSpacing:"0.05em"},children:"Interest"})]}),"lg"===t||"xl"===t?(0,r.jsx)(n.P.p,{className:"text-xs text-gray-400 -mt-1",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.2},children:"Smart Savings Platform"}):null]})]})}function c(e){let{size:t="md",className:a=""}=e,o=s[t];return(0,r.jsx)(n.P.div,{className:"".concat(o.icon," flex items-center justify-center ").concat(a),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,r.jsx)(i.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(o.icon.split(" ")[0].replace("w-","")),height:4*parseInt(o.icon.split(" ")[1].replace("h-","")),className:"".concat(o.icon," object-contain"),priority:!0})})}},57740:(e,t,a)=>{"use strict";a.d(t,{DP:()=>o,ThemeProvider:()=>s,Yx:()=>c});var r=a(95155),n=a(12115);let i=(0,n.createContext)(void 0);function s(e){let{children:t}=e,[a,s]=(0,n.useState)("dark"),[o,l]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{l(!0);let e=localStorage.getItem("theme");e?s(e):s("dark")},[]),(0,n.useEffect)(()=>{o&&(document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(a),localStorage.setItem("theme",a))},[a,o]),o)?(0,r.jsx)(i.Provider,{value:{theme:a,toggleTheme:()=>{s(e=>"light"===e?"dark":"light")},setTheme:e=>{s(e)}},children:t}):(0,r.jsx)("div",{className:"min-h-screen bg-black",children:t})}function o(){let e=(0,n.useContext)(i);return void 0===e?(console.warn("useTheme called outside of ThemeProvider, using fallback values"),{theme:"dark",toggleTheme:()=>{},setTheme:()=>{}}):e}let l={light:{bg:{primary:"bg-white",secondary:"bg-gray-50",tertiary:"bg-gray-100",card:"bg-white",sidebar:"bg-white",header:"bg-white/80"},text:{primary:"text-gray-900",secondary:"text-gray-600",tertiary:"text-gray-500",inverse:"text-white"},border:{primary:"border-gray-200",secondary:"border-gray-300",accent:"border-green-200"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"},accent:{green:"text-green-600",blue:"text-blue-600",purple:"text-purple-600",yellow:"text-yellow-600"}},dark:{bg:{primary:"bg-black",secondary:"bg-gray-900",tertiary:"bg-gray-800",card:"bg-gray-900",sidebar:"bg-gray-900",header:"bg-gray-900/80"},text:{primary:"text-white",secondary:"text-gray-300",tertiary:"text-gray-400",inverse:"text-black"},border:{primary:"border-gray-800",secondary:"border-gray-700",accent:"border-green-500/30"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-black"},accent:{green:"text-green-400",blue:"text-blue-400",purple:"text-purple-400",yellow:"text-yellow-400"}}};function c(e){return l[e]}},92702:(e,t,a)=>{Promise.resolve().then(a.bind(a,358))}},e=>{e.O(0,[844,5236,6874,6766,7153,8441,5964,7358],()=>e(e.s=92702)),_N_E=e.O()}]);