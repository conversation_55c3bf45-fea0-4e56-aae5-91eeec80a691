"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1846],{11846:(e,t,r)=>{r.d(t,{A:()=>N});var a=r(95155),n=r(60760),s=r(68289),i=r(66766),o=r(6874),l=r.n(o),d=r(35695),c=r(12115),h=r(10351),x=r(57740),m=r(98030),g=r(13741);let u=[{id:"1",title:"Savings Goal Achieved!",message:"Congratulations! You've reached your Emergency Fund goal of ₦500,000.",type:"success",timestamp:new Date(Date.now()-3e5),read:!1,icon:h.ARf},{id:"2",title:"Monthly Contribution Due",message:"Your monthly contribution of ₦25,000 is due in 3 days.",type:"warning",timestamp:new Date(Date.now()-72e5),read:!1,icon:h.z8N},{id:"3",title:"KYC Verification Complete",message:"Your identity verification has been approved. You now have full access.",type:"success",timestamp:new Date(Date.now()-864e5),read:!0,icon:h.pcC}];function p(){let[e,t]=(0,c.useState)(!1),[r,i]=(0,c.useState)(u),o=r.filter(e=>!e.read).length;return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.K0,{icon:h.zd,onClick:()=>t(!e),className:"relative",tooltip:"Notifications"}),o>0&&(0,a.jsx)(s.P.span,{initial:{scale:0},animate:{scale:1},className:"absolute -top-1 -right-1 w-5 h-5 bg-brand text-white text-xs rounded-full flex items-center justify-center font-bold shadow-lg",children:o>9?"9+":o})]}),(0,a.jsx)(n.N,{children:e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>t(!1)}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 top-full mt-2 w-96 bg-theme border border-theme rounded-xl shadow-2xl z-50 overflow-hidden",style:{boxShadow:"0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(34, 197, 94, 0.1)"},children:[(0,a.jsx)("div",{className:"p-4 border-b border-theme bg-theme-secondary",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"font-inter font-semibold text-theme",children:"Notifications"}),o>0&&(0,a.jsx)("button",{onClick:()=>{i(e=>e.map(e=>({...e,read:!0})))},className:"text-brand hover:text-brand-dark text-sm font-medium transition-colors",children:"Mark all read"})]})}),(0,a.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===r.length?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)(h.zd,{className:"w-12 h-12 text-theme-secondary mx-auto mb-3"}),(0,a.jsx)("p",{className:"text-theme-secondary font-inter",children:"No notifications"})]}):(0,a.jsx)("div",{className:"divide-y divide-theme",children:r.map(e=>{let t=e.icon||h.zd;return(0,a.jsx)(s.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"p-4 hover:bg-theme-secondary transition-colors cursor-pointer group ".concat(e.read?"":"bg-brand/5"),onClick:()=>{var t;return t=e.id,void i(e=>e.map(e=>e.id===t?{...e,read:!0}:e))},children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat((e=>{switch(e){case"success":return"text-brand bg-brand/10";case"warning":return"text-yellow-500 bg-yellow-500/10";case"error":return"text-red-500 bg-red-500/10";default:return"text-blue-500 bg-blue-500/10"}})(e.type)),children:(0,a.jsx)(t,{className:"w-4 h-4"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsx)("h4",{className:"font-medium text-sm font-inter ".concat(e.read?"text-theme-secondary":"text-theme"),children:e.title}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 ml-2",children:[(0,a.jsx)("span",{className:"text-xs text-theme-secondary whitespace-nowrap",children:(e=>{let t=new Date().getTime()-e.getTime(),r=Math.floor(t/6e4),a=Math.floor(t/36e5),n=Math.floor(t/864e5);return r<60?"".concat(r,"m ago"):a<24?"".concat(a,"h ago"):"".concat(n,"d ago")})(e.timestamp)}),(0,a.jsx)("button",{onClick:t=>{var r;t.stopPropagation(),r=e.id,i(e=>e.filter(e=>e.id!==r))},className:"opacity-0 group-hover:opacity-100 p-1 hover:bg-red-500/10 rounded transition-all",children:(0,a.jsx)(h.yGN,{className:"w-3 h-3 text-red-500"})})]})]}),(0,a.jsx)("p",{className:"text-xs text-theme-secondary mt-1 font-inter",children:e.message}),!e.read&&(0,a.jsx)("div",{className:"w-2 h-2 bg-brand rounded-full mt-2"})]})]})},e.id)})})}),r.length>0&&(0,a.jsx)("div",{className:"p-3 border-t border-theme bg-theme-secondary",children:(0,a.jsx)("button",{className:"w-full text-center text-brand hover:text-brand-dark text-sm font-medium transition-colors font-inter",children:"View all notifications"})})]})]})})]})}function b(){var e,t,r,i;let[o,l]=(0,c.useState)(!1),{user:x,logout:g}=(0,m.A)(),u=(0,d.useRouter)(),p=async()=>{try{await g(),u.push("/")}catch(e){console.error("Logout failed:",e)}},b=x||{firstName:"Demo",lastName:"User",email:"<EMAIL>",role:"USER",kycStatus:"PENDING"},f=[{id:"profile",label:"View Profile",icon:h.JXP,href:"/dashboard/profile"},{id:"kyc",label:"Identity Verification",icon:h.pcC,href:"/dashboard/kyc",status:"APPROVED"===b.kycStatus?"verified":"pending"},{id:"payment",label:"Payment Methods",icon:h.lZI,href:"/dashboard/payment-methods"},{id:"settings",label:"Account Settings",icon:h.VSk,href:"/dashboard/settings",divider:!0},{id:"logout",label:"Sign Out",icon:h.QeK,action:p}];return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(s.P.button,{onClick:()=>l(!o),className:"flex items-center space-x-2 p-1 rounded-lg hover:bg-theme-secondary transition-colors",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-brand to-brand-dark rounded-full flex items-center justify-center shadow-lg",children:(0,a.jsxs)("span",{className:"text-white font-semibold text-sm font-inter",children:[null==b||null==(e=b.firstName)?void 0:e[0],null==b||null==(t=b.lastName)?void 0:t[0]]})}),(0,a.jsxs)("div",{className:"hidden md:block text-left",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-theme font-inter",children:[null==b?void 0:b.firstName," ",null==b?void 0:b.lastName]}),(0,a.jsx)("p",{className:"text-xs text-theme-secondary",children:null==b?void 0:b.role})]})]}),(0,a.jsx)(n.N,{children:o&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>l(!1)}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 top-full mt-2 w-72 bg-theme border border-theme rounded-xl shadow-2xl z-50 overflow-hidden",style:{boxShadow:"0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(34, 197, 94, 0.1)"},children:[(0,a.jsx)("div",{className:"p-4 border-b border-theme bg-theme-secondary",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-brand to-brand-dark rounded-full flex items-center justify-center shadow-lg",children:(0,a.jsxs)("span",{className:"text-white font-bold text-lg font-inter",children:[null==b||null==(r=b.firstName)?void 0:r[0],null==b||null==(i=b.lastName)?void 0:i[0]]})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("h3",{className:"font-inter font-semibold text-theme",children:[null==b?void 0:b.firstName," ",null==b?void 0:b.lastName]}),(0,a.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:null==b?void 0:b.email}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)("span",{className:"inline-block px-2 py-1 text-xs rounded-full font-medium ".concat((null==b?void 0:b.role)==="ADMIN"?"bg-red-500/20 text-red-400":"bg-brand/20 text-brand"),children:null==b?void 0:b.role}),(null==b?void 0:b.kycStatus)==="APPROVED"&&(0,a.jsxs)("span",{className:"inline-flex items-center space-x-1 px-2 py-1 text-xs rounded-full bg-brand/20 text-brand",children:[(0,a.jsx)(h.YrT,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Verified"})]})]})]})]})}),(0,a.jsx)("div",{className:"py-2",children:f.map((e,t)=>(0,a.jsxs)(c.Fragment,{children:[e.divider&&t>0&&(0,a.jsx)("div",{className:"my-2 border-t border-theme"}),(0,a.jsxs)(s.P.button,{onClick:()=>{e.action?e.action():e.href&&u.push(e.href),l(!1)},className:"w-full px-4 py-3 flex items-center justify-between hover:bg-theme-secondary transition-colors group",whileHover:{x:4},transition:{duration:.2},children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-lg bg-brand/10 group-hover:bg-brand/20 transition-colors",children:(0,a.jsx)(e.icon,{className:"w-4 h-4 text-brand"})}),(0,a.jsx)("span",{className:"font-inter text-sm text-theme group-hover:text-brand transition-colors",children:e.label})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.status&&(0,a.jsx)("div",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"verified":return"bg-brand/10 text-brand";case"pending":return"bg-yellow-500/10 text-yellow-500";default:return""}})(e.status)),children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(e=>{switch(e){case"verified":return(0,a.jsx)(h.YrT,{className:"w-3 h-3 text-brand"});case"pending":return(0,a.jsx)(h.Ohp,{className:"w-3 h-3 text-yellow-500"});default:return null}})(e.status),(0,a.jsx)("span",{children:"verified"===e.status?"Verified":"Pending"})]})}),e.badge&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-brand text-white rounded-full",children:e.badge}),(0,a.jsx)(h.fOo,{className:"w-4 h-4 text-theme-secondary group-hover:text-brand transition-colors"})]})]})]},e.id))})]})]})})]})}var f=r(40108);function y(){let[e,t]=(0,c.useState)(!1),{theme:r,toggleTheme:i}=(0,x.DP)(),{logout:o}=(0,m.A)(),l=(0,d.useRouter)(),u=async()=>{try{await o(),l.push("/")}catch(e){console.error("Logout failed:",e)}},p=[{id:"profile",label:"Profile Settings",icon:h.JXP,href:"/dashboard/profile"},{id:"security",label:"Security & Privacy",icon:h.pcC,href:"/dashboard/security"},{id:"notifications",label:"Notification Preferences",icon:h.zd,href:"/dashboard/notifications"},{id:"theme",label:"Switch to ".concat("light"===r?"Dark":"Light"," Mode"),icon:"light"===r?h.hkc:h.Wh$,action:i},{id:"help",label:"Help & Support",icon:h.lrG,href:"/dashboard/help",divider:!0},{id:"logout",label:"Sign Out",icon:h.QeK,action:u}];return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.K0,{icon:h.VSk,onClick:()=>t(!e),tooltip:"Settings"}),(0,a.jsx)(n.N,{children:e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>t(!1)}),(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute right-0 top-full mt-2 w-64 bg-theme border border-theme rounded-xl shadow-2xl z-50 overflow-hidden",style:{boxShadow:"0 20px 40px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(34, 197, 94, 0.1)"},children:[(0,a.jsx)("div",{className:"p-4 border-b border-theme bg-theme-secondary",children:(0,a.jsxs)("h3",{className:"font-inter font-semibold text-theme flex items-center",children:[(0,a.jsx)(h.VSk,{className:"w-4 h-4 mr-2 text-brand"}),"Settings"]})}),(0,a.jsx)("div",{className:"py-2",children:p.map((e,r)=>(0,a.jsxs)(c.Fragment,{children:[e.divider&&r>0&&(0,a.jsx)("div",{className:"my-2 border-t border-theme"}),(0,a.jsxs)(s.P.button,{onClick:()=>{e.action?e.action():e.href&&l.push(e.href),t(!1)},className:"w-full px-4 py-3 flex items-center justify-between hover:bg-theme-secondary transition-colors group",whileHover:{x:4},transition:{duration:.2},children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-1.5 rounded-lg bg-brand/10 group-hover:bg-brand/20 transition-colors",children:(0,a.jsx)(e.icon,{className:"w-4 h-4 text-brand"})}),(0,a.jsx)("span",{className:"font-inter text-sm text-theme group-hover:text-brand transition-colors",children:e.label})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[e.badge&&(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-brand text-white rounded-full",children:e.badge}),"theme"===e.id?(0,a.jsx)(f.MG,{variant:"switch",className:"scale-75"}):(0,a.jsx)(h.fOo,{className:"w-4 h-4 text-theme-secondary group-hover:text-brand transition-colors"})]})]})]},e.id))}),(0,a.jsx)("div",{className:"p-3 border-t border-theme bg-theme-secondary",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-xs text-theme-secondary font-inter",children:"BetterInterest v1.0.0"})})})]})]})})]})}var v=r(31246);let w=[{name:"Dashboard",href:"/dashboard",icon:h.V5Y,description:"Overview and quick actions"},{name:"Group Savings",href:"/dashboard/group-savings",icon:h.cfS,description:"Collaborative savings",isNew:!0},{name:"Rotational Savings",href:"/dashboard/rotational",icon:h.ARf,description:"Join rotating savings groups",isNew:!0},{name:"Target Savings",href:"/dashboard/target-savings",icon:h.x_j,description:"Goal-based savings"},{name:"Individual Savings",href:"/dashboard/savings-plans",icon:h.eXT,description:"Normal savings plans"},{name:"Payments",href:"/dashboard/payments",icon:h.lZI,description:"Payment history"},{name:"KYC Verification",href:"/dashboard/kyc",icon:h.p45,description:"Identity verification"},{name:"Profile",href:"/dashboard/profile",icon:h.JXP,description:"Account settings"},{name:"Settings",href:"/dashboard/settings",icon:h.VSk,description:"App preferences"}],j=[{name:"Admin Dashboard",href:"/admin/dashboard",icon:h.pcC,description:"Admin overview"},{name:"User Management",href:"/admin/users",icon:h.cfS,description:"Manage users",badge:"12"},{name:"Payment Management",href:"/admin/payments",icon:h.z8N,description:"Payment oversight"},{name:"KYC Management",href:"/admin/kyc",icon:h.p45,description:"Identity verification",badge:"5"},{name:"Analytics",href:"/admin/analytics",icon:h.ARf,description:"Platform analytics"},{name:"Notifications",href:"/admin/notifications",icon:h.zd,description:"System notifications",badge:"3"},{name:"Reports",href:"/admin/reports",icon:h.jH2,description:"Generate reports"},{name:"Settings",href:"/admin/settings",icon:h.VSk,description:"System settings"}];function N(e){var t,r;let{children:o,title:g}=e,[u,f]=(0,c.useState)(!1),[N,k]=(0,c.useState)(!1),S=(0,d.useRouter)(),{user:C,logout:A,isLoading:T}=(0,m.A)(),{theme:P,toggleTheme:L}=(0,x.DP)();(0,c.useEffect)(()=>{let e=()=>{k(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let E=async()=>{try{await A(),S.push("/")}catch(e){console.error("Logout failed:",e)}},U=C||{firstName:"Demo",lastName:"User",email:"<EMAIL>",role:"USER"},M=(null==U?void 0:U.role)==="ADMIN"?j:w;return T?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-800",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gradient-to-r from-green-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg mb-6 mx-auto",children:(0,a.jsx)("span",{className:"text-white font-bold text-3xl",children:"BI"})}),(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Loading your dashboard..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen flex transition-colors duration-300 ".concat("light"===P?"bg-gradient-to-br from-gray-50 via-white to-gray-100 text-gray-900":"bg-gradient-to-br from-gray-900 via-black to-gray-800 text-white"),children:[(0,a.jsx)(n.N,{children:u&&!N&&(0,a.jsx)(s.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 z-40 bg-black/50",onClick:()=>f(!1)})}),(0,a.jsx)("div",{className:"\n          ".concat(N?"relative translate-x-0":"fixed ".concat(u?"translate-x-0":"-translate-x-full"),"\n          inset-y-0 left-0\n          ").concat(N?"z-10":"z-50","\n          w-80 backdrop-blur-lg border-r flex flex-col\n          transition-transform duration-300 ease-in-out\n          ").concat("light"===P?"bg-white/95 border-gray-200 text-gray-900 shadow-xl":"bg-gray-900/95 border-gray-700 text-white shadow-2xl","\n        "),style:{borderRadius:"0 20px 20px 0",boxShadow:"light"===P?"0 4px 20px rgba(0, 0, 0, 0.08), 0 8px 40px rgba(34, 197, 94, 0.12), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 20px rgba(0, 0, 0, 0.3), 0 8px 40px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.05)",border:"1px solid rgba(34, 197, 94, 0.2)"},children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-800",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,a.jsx)(i.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:32,height:32,className:"w-8 h-8 object-contain",priority:!0})}),(0,a.jsx)("span",{className:"text-xl font-bold",children:"BetterInterest"})]}),(0,a.jsx)("button",{onClick:()=>f(!1),className:"lg:hidden p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,a.jsx)(h.yGN,{className:"w-5 h-5"})})]}),(0,a.jsx)("div",{className:"p-6 border-b border-gray-800",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-black font-semibold",children:[null==U||null==(t=U.firstName)?void 0:t[0],null==U||null==(r=U.lastName)?void 0:r[0]]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:[null==U?void 0:U.firstName," ",null==U?void 0:U.lastName]}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:null==U?void 0:U.email}),(0,a.jsx)("span",{className:"inline-block px-2 py-1 text-xs rounded-full mt-1 ".concat((null==U?void 0:U.role)==="ADMIN"?"bg-red-500/20 text-red-400":"bg-green-500/20 text-green-400"),children:null==U?void 0:U.role})]})]})}),(0,a.jsx)("nav",{className:"flex-1 px-6 py-6 space-y-1 overflow-y-auto",children:M.map((e,t)=>(0,a.jsx)(s.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.3,delay:.1*t},children:(0,a.jsxs)(l(),{href:e.href,className:"\n                    flex items-center justify-between px-4 py-3 rounded-lg\n                    transition-all duration-200 group relative overflow-hidden\n                    font-inter font-medium transform-gpu hover:scale-105 active:scale-95\n                    border border-transparent hover:border-brand/20\n                    ".concat("light"===P?"hover:bg-gray-100 hover:shadow-md":"hover:bg-gray-800 hover:shadow-lg","\n                    hover:shadow-brand/10\n                  "),onClick:()=>f(!1),style:{boxShadow:"0 2px 8px rgba(34, 197, 94, 0.1)"},children:[(0,a.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-transparent group-hover:bg-brand rounded-r-full transition-all duration-200 shadow-lg shadow-brand/50"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-brand/5 to-brand/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 relative z-10",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(e.icon,{className:"w-5 h-5 text-brand transition-all duration-200 group-hover:drop-shadow-sm group-hover:scale-110"}),e.isNew&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-2 h-2 bg-brand rounded-full animate-pulse shadow-sm"})]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-medium transition-colors duration-200 ".concat("light"===P?"text-gray-700 group-hover:text-brand":"text-gray-300 group-hover:text-white"),children:e.name}),e.isNew&&(0,a.jsx)("span",{className:"px-1.5 py-0.5 text-xs bg-brand text-white rounded-full shadow-sm",children:"NEW"})]}),e.description&&(0,a.jsx)("p",{className:"text-xs transition-colors duration-200 mt-0.5 ".concat("light"===P?"text-gray-500 group-hover:text-gray-600":"text-gray-500 group-hover:text-gray-300"),children:e.description})]})]}),e.badge&&(0,a.jsx)("div",{className:"relative z-10",children:(0,a.jsx)("span",{className:"px-2 py-1 text-xs bg-brand text-white rounded-full shadow-md",children:e.badge})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 rounded-lg"}),(0,a.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity duration-200 relative z-10",children:(0,a.jsx)("svg",{className:"w-4 h-4 text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})},e.name))}),(0,a.jsxs)("div",{className:"p-6 border-t border-gray-800 space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-800/50 rounded-lg border border-gray-700",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-white",children:"Theme"}),(0,a.jsx)(v.Ay,{onClick:L,className:"p-2 rounded-lg bg-gray-700 hover:bg-gray-600 transition-all duration-200 border border-gray-600 hover:border-green-500",children:"light"===P?(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,a.jsx)("svg",{className:"w-4 h-4 text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})]}),(0,a.jsxs)(v.Ay,{onClick:E,className:"flex items-center space-x-3 w-full px-4 py-3 rounded-lg hover:bg-red-500/20 hover:text-red-400 transition-all duration-200 group",children:[(0,a.jsx)(h.QeK,{className:"w-5 h-5 group-hover:scale-110 transition-transform duration-200"}),(0,a.jsx)("span",{className:"font-medium",children:"Logout"})]})]})]})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col min-w-0 lg:ml-0",children:[(0,a.jsxs)("header",{className:"h-16 backdrop-blur-sm border-b flex items-center justify-between px-6 relative ".concat("light"===P?"bg-white/80 border-gray-200":"bg-gray-900/80 border-gray-800"),children:[(0,a.jsx)("div",{className:"absolute inset-0 ".concat("light"===P?"bg-gradient-to-r from-white/50 to-gray-50/50":"bg-gradient-to-r from-gray-900/50 to-gray-800/50")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 relative z-10",children:[(0,a.jsx)(s.P.button,{onClick:()=>f(!0),className:"lg:hidden p-2 rounded-lg transition-all duration-200 hover:scale-105 ".concat("light"===P?"hover:bg-gray-100 text-gray-700":"hover:bg-gray-800 text-gray-300"),whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsx)(h.ND1,{className:"w-5 h-5"})}),g&&(0,a.jsx)("h1",{className:"text-xl font-bold font-inter bg-gradient-to-r bg-clip-text text-transparent ".concat("light"===P?"from-gray-800 to-gray-600":"from-white to-gray-300"),children:g})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3 relative z-10",children:[(0,a.jsx)(v.Ay,{icon:(0,a.jsx)(h.CKj,{}),className:"text-brand",children:""}),(0,a.jsx)(p,{}),(0,a.jsx)(y,{}),(0,a.jsx)(b,{})]})]}),(0,a.jsx)("main",{className:"p-6 flex-1 ".concat("light"===P?"bg-gray-50/50":"bg-transparent"),children:o})]})]})}},31246:(e,t,r)=>{r.d(t,{Ay:()=>d,jn:()=>o,rp:()=>l});var a=r(95155);r(12115);var n=r(68289);let s=()=>(0,a.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,a.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function i(e){let{children:t,onClick:r,href:i,variant:o="primary",size:l="md",disabled:d=!1,className:c="",type:h="button",icon:x}=e,m="\n    cursor-pointer font-bold font-sans transition-all duration-200 \n    border-2 border-transparent flex items-center justify-center\n    rounded-full relative overflow-hidden group\n    shadow-lg hover:shadow-xl active:shadow-inner\n    transform hover:-translate-y-1 active:translate-y-0\n    ".concat(d?"opacity-50 cursor-not-allowed":"","\n  "),g="\n    ".concat(m,"\n    ").concat({primary:"\n      bg-gradient-to-r from-green-400 to-green-600 \n      hover:from-green-500 hover:to-green-700\n      text-white border-green-500\n      active:border-green-600 active:shadow-inner\n    ",secondary:"\n      bg-gradient-to-r from-blue-400 to-blue-600 \n      hover:from-blue-500 hover:to-blue-700\n      text-white border-blue-500\n      active:border-blue-600 active:shadow-inner\n    ",outline:"\n      bg-transparent border-green-400 text-green-400\n      hover:bg-green-400 hover:text-black\n      active:border-green-500 active:shadow-inner\n    "}[o],"\n    ").concat({sm:"px-3 py-1.5 text-xs min-w-[80px]",md:"px-4 py-2 text-sm min-w-[100px]",lg:"px-6 py-3 text-base min-w-[120px]"}[l],"\n    ").concat(c,"\n  "),u=()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,a.jsxs)("span",{className:"relative z-10 flex items-center justify-center",children:[(0,a.jsx)("span",{className:"text",children:t}),(x||"primary"===o)&&(0,a.jsx)(n.P.div,{className:"ml-2 text-current",whileHover:{x:3},transition:{type:"spring",stiffness:400,damping:10},children:x||(0,a.jsx)(s,{})})]})]});return i?(0,a.jsx)(n.P.a,{href:i,className:g,whileHover:{scale:1.02,y:-1},whileTap:{scale:.96,y:1},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(u,{})}):(0,a.jsx)(n.P.button,{type:h,onClick:r,disabled:d,className:g,whileHover:{scale:d?1:1.02,y:d?0:-1},whileTap:{scale:d?1:.96,y:+!d},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(u,{})})}function o(e){return(0,a.jsx)(i,{...e,variant:"primary"})}function l(e){return(0,a.jsx)(i,{...e,variant:"outline"})}let d=i},40108:(e,t,r)=>{r.d(t,{MG:()=>c,jn:()=>l,rp:()=>d});var a=r(95155);r(12115);var n=r(68289),s=r(57740);let i=()=>(0,a.jsxs)("svg",{width:34,height:34,viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,a.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function o(e){let{children:t,onClick:r,href:o,variant:l="primary",size:d="md",disabled:c=!1,className:h="",type:x="button",icon:m,fullWidth:g=!1}=e,{theme:u}=(0,s.DP)(),p=(0,s.Yx)(u),b="\n    cursor-pointer font-semibold font-inter transition-all duration-300 transform hover:scale-105\n    border border-transparent flex items-center justify-center\n    rounded-xl relative overflow-hidden group shadow-lg\n    ".concat(g?"w-full":"","\n    ").concat(c?"opacity-50 cursor-not-allowed":"","\n  "),f={primary:p.button.primary,secondary:p.button.secondary,outline:p.button.outline},y="\n    ".concat(b,"\n    ").concat(f[l],"\n    ").concat({sm:"px-4 py-[7px] text-sm",md:"px-6 py-[11px] text-base",lg:"px-8 py-[15px] text-lg"}[d],"\n    ").concat(h,"\n  "),v=()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,a.jsxs)("span",{className:"relative z-10 flex items-center",children:[(0,a.jsx)("span",{className:"text",children:t}),(m||"primary"===l)&&(0,a.jsx)(n.P.div,{className:"ml-2 text-current",whileHover:{x:5},transition:{type:"spring",stiffness:400,damping:10},children:m||(0,a.jsx)(i,{})})]})]});return o?(0,a.jsx)(n.P.a,{href:o,className:y,whileHover:{scale:1.02},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(v,{})}):(0,a.jsx)(n.P.button,{type:x,onClick:r,disabled:c,className:y,whileHover:{scale:c?1:1.02},whileTap:{scale:c?1:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(v,{})})}function l(e){return(0,a.jsx)(o,{...e,variant:"primary"})}function d(e){return(0,a.jsx)(o,{...e,variant:"outline"})}function c(e){let{className:t="",variant:r="icon"}=e,{theme:i,toggleTheme:o}=(0,s.DP)(),l=(0,s.Yx)(i);return"switch"===r?(0,a.jsxs)("div",{className:"flex items-center space-x-3 ".concat(t),children:[(0,a.jsx)("span",{className:"text-sm font-medium ".concat(l.text.secondary),children:"light"===i?"Light":"Dark"}),(0,a.jsx)(n.P.button,{onClick:o,className:"relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ".concat("light"===i?"bg-gray-200 focus:ring-offset-white":"bg-green-600 focus:ring-offset-gray-900"),whileTap:{scale:.95},title:"Switch to ".concat("light"===i?"dark":"light"," mode"),children:(0,a.jsx)(n.P.span,{className:"inline-block h-4 w-4 transform rounded-full bg-white shadow-lg transition-transform ".concat("light"===i?"translate-x-1":"translate-x-6"),layout:!0,transition:{type:"spring",stiffness:500,damping:30}})})]}):"fab"===r?(0,a.jsx)(n.P.button,{onClick:o,className:"fixed bottom-6 right-6 w-14 h-14 rounded-full shadow-lg transition-all duration-300 z-50 ".concat("light"===i?"bg-white text-gray-800 shadow-gray-300 hover:shadow-xl":"bg-gray-800 text-yellow-400 shadow-gray-900 hover:shadow-2xl"),whileHover:{scale:1.1,rotate:180},whileTap:{scale:.9},title:"Switch to ".concat("light"===i?"dark":"light"," mode"),style:{boxShadow:"light"===i?"0 4px 20px rgba(0, 0, 0, 0.1), 0 8px 40px rgba(0, 0, 0, 0.05)":"0 4px 20px rgba(0, 0, 0, 0.3), 0 8px 40px rgba(34, 197, 94, 0.1)"},children:(0,a.jsx)(n.P.div,{initial:!1,animate:{rotate:180*("light"!==i)},transition:{duration:.3},children:"light"===i?(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,a.jsx)("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})}):(0,a.jsxs)(n.P.button,{onClick:o,className:"relative p-3 rounded-full transition-all duration-300 overflow-hidden group ".concat("light"===i?"bg-gray-100 hover:bg-gray-200 text-gray-700 hover:text-gray-900":"bg-gray-800 hover:bg-gray-700 text-gray-300 hover:text-white"," ").concat(t),whileHover:{scale:1.05},whileTap:{scale:.95},title:"Switch to ".concat("light"===i?"dark":"light"," mode"),style:{boxShadow:"light"===i?"0 2px 8px rgba(0, 0, 0, 0.1)":"0 2px 8px rgba(0, 0, 0, 0.3)"},children:[(0,a.jsx)(n.P.div,{className:"absolute inset-0 bg-green-400 opacity-0 group-hover:opacity-20 rounded-full",initial:{scale:0},whileHover:{scale:1},transition:{duration:.3}}),(0,a.jsx)(n.P.div,{initial:!1,animate:{rotate:180*("light"!==i)},transition:{duration:.5,type:"spring",stiffness:200},className:"relative z-10",children:"light"===i?(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,a.jsx)("svg",{className:"w-5 h-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})]})}},57740:(e,t,r)=>{r.d(t,{DP:()=>o,ThemeProvider:()=>i,Yx:()=>d});var a=r(95155),n=r(12115);let s=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,n.useState)("dark"),[o,l]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{l(!0);let e=localStorage.getItem("theme");e?i(e):i("dark")},[]),(0,n.useEffect)(()=>{o&&(document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(r),localStorage.setItem("theme",r))},[r,o]),o)?(0,a.jsx)(s.Provider,{value:{theme:r,toggleTheme:()=>{i(e=>"light"===e?"dark":"light")},setTheme:e=>{i(e)}},children:t}):(0,a.jsx)("div",{className:"min-h-screen bg-black",children:t})}function o(){let e=(0,n.useContext)(s);return void 0===e?(console.warn("useTheme called outside of ThemeProvider, using fallback values"),{theme:"dark",toggleTheme:()=>{},setTheme:()=>{}}):e}let l={light:{bg:{primary:"bg-white",secondary:"bg-gray-50",tertiary:"bg-gray-100",card:"bg-white",sidebar:"bg-white",header:"bg-white/80"},text:{primary:"text-gray-900",secondary:"text-gray-600",tertiary:"text-gray-500",inverse:"text-white"},border:{primary:"border-gray-200",secondary:"border-gray-300",accent:"border-green-200"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"},accent:{green:"text-green-600",blue:"text-blue-600",purple:"text-purple-600",yellow:"text-yellow-600"}},dark:{bg:{primary:"bg-black",secondary:"bg-gray-900",tertiary:"bg-gray-800",card:"bg-gray-900",sidebar:"bg-gray-900",header:"bg-gray-900/80"},text:{primary:"text-white",secondary:"text-gray-300",tertiary:"text-gray-400",inverse:"text-black"},border:{primary:"border-gray-800",secondary:"border-gray-700",accent:"border-green-500/30"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-black"},accent:{green:"text-green-400",blue:"text-blue-400",purple:"text-purple-400",yellow:"text-yellow-400"}}};function d(e){return l[e]}},98030:(e,t,r)=>{r.d(t,{A:()=>c,AuthProvider:()=>d});var a=r(95155),n=r(12115),s=r(96365);let i={user:null,token:null,isAuthenticated:!1,isLoading:!0,error:null};function o(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload.user,token:t.payload.token,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"LOGOUT":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null};case"UPDATE_USER":return{...e,user:t.payload};case"CLEAR_ERROR":return{...e,error:null};default:return e}}let l=(0,n.createContext)(void 0);function d(e){let{children:t}=e,[r,d]=(0,n.useReducer)(o,i);(0,n.useEffect)(()=>{c()},[]),(0,n.useEffect)(()=>{if(r.token&&r.isAuthenticated){let e=setInterval(async()=>{try{r.token&&s.authService.isTokenExpired(r.token)&&await s.authService.refreshToken()}catch(e){console.error("Token refresh failed:",e),g()}},3e5);return()=>clearInterval(e)}},[r.token,r.isAuthenticated]);let c=async()=>{try{let e=s.authService.getStoredToken();if(!e)return void d({type:"AUTH_FAILURE",payload:"No token found"});if(s.authService.isTokenExpired(e))try{await s.authService.refreshToken();let e=s.authService.getStoredToken();if(e){let t=await s.authService.getCurrentUser();d({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}else throw Error("Token refresh failed")}catch(e){d({type:"AUTH_FAILURE",payload:"Session expired"});return}else{let t=await s.authService.getCurrentUser();d({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}}catch(e){d({type:"AUTH_FAILURE",payload:"Authentication check failed"})}},h=async e=>{try{d({type:"AUTH_START"});let t=await s.authService.login(e);d({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw d({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Login failed"}),e}},x=async e=>{try{d({type:"AUTH_START"});let t=await s.authService.signup(e);d({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw d({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Signup failed"}),e}},m=async e=>{try{d({type:"AUTH_START"});let t=await s.authService.adminLogin(e);d({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw d({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Admin login failed"}),e}},g=async()=>{try{await s.authService.logout()}catch(e){console.error("Logout error:",e)}finally{d({type:"LOGOUT"})}},u={...r,login:h,signup:x,adminLogin:m,logout:g,updateUser:e=>{d({type:"UPDATE_USER",payload:e})},clearError:()=>{d({type:"CLEAR_ERROR"})},checkAuth:c};return(0,a.jsx)(l.Provider,{value:u,children:t})}function c(){let e=(0,n.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}}]);