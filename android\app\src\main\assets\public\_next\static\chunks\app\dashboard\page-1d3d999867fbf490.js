(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{10924:(e,t,a)=>{"use strict";a.d(t,{KR:()=>s,re:()=>r});let s=()=>{var e,t;if(null==(t=window.Capacitor)||null==(e=t.isNativePlatform)?void 0:e.call(t))return!0;let a=navigator.userAgent.toLowerCase();return[/android/i,/iphone/i,/ipad/i,/ipod/i,/blackberry/i,/windows phone/i,/mobile/i].some(e=>e.test(a))},r=()=>s()},13428:(e,t,a)=>{"use strict";a.d(t,{r:()=>i});var s=a(98030),r=a(35695),n=a(12115);function i(){let{redirect:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{user:t,isLoading:a,isAuthenticated:i}=(0,s.A)(),l=(0,r.useRouter)();return(0,n.useEffect)(()=>{!a&&i&&(null==t?void 0:t.kycStatus)!=="APPROVED"&&e&&l.push("/dashboard/kyc")},[t,a,i,e,l]),(null==t?void 0:t.kycStatus)==="APPROVED"}},48016:(e,t,a)=>{"use strict";a.d(t,{Qh:()=>c,UU:()=>o});var s=a(95155),r=a(68289),n=a(66766);let i={sm:{container:"h-8",text:"text-lg",icon:"w-6 h-6"},md:{container:"h-10",text:"text-xl",icon:"w-8 h-8"},lg:{container:"h-12",text:"text-2xl",icon:"w-10 h-10"},xl:{container:"h-16",text:"text-3xl",icon:"w-12 h-12"}},l={light:"text-white",dark:"text-gray-900",gradient:"bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"};function o(e){let{size:t="md",variant:a="gradient",showIcon:o=!0,className:c=""}=e,d=i[t],x=l[a];return(0,s.jsxs)(r.P.div,{className:"flex items-center space-x-2 ".concat(d.container," ").concat(c),whileHover:{scale:1.05},transition:{type:"spring",stiffness:400,damping:10},children:[o&&(0,s.jsx)(r.P.div,{className:"".concat(d.icon," flex items-center justify-center relative"),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,s.jsx)(n.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(d.icon.split(" ")[0].replace("w-","")),height:4*parseInt(d.icon.split(" ")[1].replace("h-","")),className:"".concat(d.icon," object-contain"),priority:!0})}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)(r.P.h1,{className:"font-inter font-bold leading-tight ".concat(d.text," ").concat(x," relative"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},style:{letterSpacing:"0.02em",textShadow:"0 0 20px rgba(255, 255, 255, 0.1)"},children:[(0,s.jsx)("span",{className:"text-white relative",style:{textShadow:"0 0 15px rgba(255, 255, 255, 0.2)",fontWeight:"700"},children:"Better"}),(0,s.jsx)("span",{className:"text-green-400 ml-1 relative",style:{textShadow:"0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2)",fontWeight:"800",letterSpacing:"0.05em"},children:"Interest"})]}),"lg"===t||"xl"===t?(0,s.jsx)(r.P.p,{className:"text-xs text-gray-400 -mt-1",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.2},children:"Smart Savings Platform"}):null]})]})}function c(e){let{size:t="md",className:a=""}=e,l=i[t];return(0,s.jsx)(r.P.div,{className:"".concat(l.icon," flex items-center justify-center ").concat(a),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,s.jsx)(n.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(l.icon.split(" ")[0].replace("w-","")),height:4*parseInt(l.icon.split(" ")[1].replace("h-","")),className:"".concat(l.icon," object-contain"),priority:!0})})}},49697:(e,t,a)=>{"use strict";a.d(t,{HC:()=>x,XT:()=>m,co:()=>h,nv:()=>d});var s=a(95155),r=a(12115),n=a(66766),i=a(68289);let l={default:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.05)",shadow:"rgba(0, 0, 0, 0.24) 0px 8px 20px"},hero:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-xl",transform:"perspective(600px) rotateX(20deg) rotateZ(-8deg)",hoverTransform:"perspective(600px) rotateX(8deg) rotateY(15deg) rotateZ(-3deg)",shadow:"rgba(0, 0, 0, 0.3) -15px 25px 30px"},card:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.03)",shadow:"rgba(0, 0, 0, 0.2) 0px 6px 15px"},testimonial:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-full",transform:"none",hoverTransform:"scale(1.1)",shadow:"rgba(0, 0, 0, 0.15) 0px 4px 12px"},feature:{container:"relative inline-block",wrapper:"relative overflow-hidden rounded-lg",transform:"none",hoverTransform:"scale(1.02)",shadow:"rgba(0, 0, 0, 0.25) 0px 8px 25px"}},o={light:.5,medium:1,strong:1.5};function c(e){let{src:t,alt:a,width:c,height:d,className:x="",priority:h=!1,fill:m=!1,sizes:g,quality:u=75,objectFit:p="cover",variant:b="default",intensity:v="medium",...f}=e,[j,y]=(0,r.useState)(!1),[N,w]=(0,r.useState)(!0),[S,A]=(0,r.useState)(!1),D=l[b];return(o[v],S)?(0,s.jsx)("div",{className:"bg-gray-800 border border-gray-700 flex items-center justify-center ".concat(D.wrapper," ").concat(x),style:{width:m?"100%":c,height:m?"100%":d,transform:D.transform,boxShadow:D.shadow,transformStyle:"preserve-3d",transition:"transform 0.6s ease-out"},children:(0,s.jsxs)("div",{className:"text-center text-gray-400",children:[(0,s.jsx)("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"currentColor",viewBox:"0 0 20 20",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})}),(0,s.jsx)("p",{className:"text-xs",children:"Image not found"})]})}):(0,s.jsxs)("div",{className:D.container,children:[N&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-800 animate-pulse ".concat(D.wrapper),style:{width:m?"100%":c,height:m?"100%":d,transform:D.transform,boxShadow:D.shadow,transformStyle:"preserve-3d",zIndex:10},children:(0,s.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,s.jsx)("div",{className:"w-8 h-8 border-2 border-green-400 border-t-transparent rounded-full animate-spin"})})}),(0,s.jsxs)(i.P.div,{className:"".concat(D.wrapper," ").concat(x),style:{transformStyle:"preserve-3d",transform:D.transform,boxShadow:D.shadow,transition:"transform 0.6s ease-out, box-shadow 0.6s ease-out"},animate:{transform:j?D.hoverTransform:D.transform,boxShadow:j?D.shadow.replace(/rgba\(0, 0, 0, ([\d.]+)\)/,(e,t)=>"rgba(0, 0, 0, ".concat(1.3*parseFloat(t),")")):D.shadow},transition:{type:"spring",stiffness:300,damping:30},onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:[(0,s.jsx)("div",{className:"absolute inset-0 z-10 pointer-events-none",style:{background:"linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.05) 50%, rgba(0, 0, 0, 0.1) 100%)",opacity:j?.8:.4,transition:"opacity 0.3s ease"}}),(0,s.jsx)(n.default,{src:t,alt:a,width:m?void 0:c,height:m?void 0:d,fill:m,priority:h,quality:u,sizes:g,className:"\n            ".concat("cover"===p?"object-cover":"","\n            ").concat("contain"===p?"object-contain":"","\n            ").concat("fill"===p?"object-fill":"","\n            ").concat("none"===p?"object-none":"","\n            ").concat("scale-down"===p?"object-scale-down":"","\n            transition-transform duration-300\n          "),style:{opacity:+!N,transition:"opacity 0.3s ease"},onLoad:()=>{w(!1)},onError:()=>{w(!1),A(!0)},...f}),(0,s.jsx)(i.P.div,{className:"absolute inset-0 pointer-events-none",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%)",transform:"translateX(-100%)"},animate:{transform:j?"translateX(100%)":"translateX(-100%)"},transition:{duration:.6,ease:"easeInOut"}})]})]})}function d(e){return(0,s.jsx)(c,{...e,variant:"hero"})}function x(e){return(0,s.jsx)(c,{...e,variant:"card"})}function h(e){return(0,s.jsx)(c,{...e,variant:"testimonial"})}function m(e){return(0,s.jsx)(c,{...e,variant:"feature"})}},58111:(e,t,a)=>{"use strict";a.d(t,{Lz:()=>d,gz:()=>x,hI:()=>c});var s=a(95155),r=a(12115),n=a(68289),i=a(57740);let l={green:{bg:"bg-green-500/20",text:"text-green-400",border:"border-green-500/30",icon:"text-green-400"},blue:{bg:"bg-blue-500/20",text:"text-blue-400",border:"border-blue-500/30",icon:"text-blue-400"},red:{bg:"bg-red-500/20",text:"text-red-400",border:"border-red-500/30",icon:"text-red-400"},yellow:{bg:"bg-yellow-500/20",text:"text-yellow-400",border:"border-yellow-500/30",icon:"text-yellow-400"},purple:{bg:"bg-purple-500/20",text:"text-purple-400",border:"border-purple-500/30",icon:"text-purple-400"}};function o(e){var t,a,o,c;let{title:d,value:x,subtitle:h,icon:m,trend:g,color:u="green",onClick:p,className:b=""}=e,v=l[u]||l.green,{theme:f}=(0,i.DP)(),j=(0,i.Yx)(f),y=(0,r.useRef)(null),N=e=>{let t={1:"light"===f?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===f?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===f?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)"};return t[e]||t[1]};return(0,s.jsxs)(n.P.div,{ref:y,initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-4,boxShadow:N(3),transition:{duration:.2,ease:"easeOut"}},className:"\n        relative overflow-hidden rounded-xl p-6 transition-all duration-300 group\n        ".concat(p?"cursor-pointer":"","\n        ").concat("light"===f?"bg-white border border-gray-200 hover:border-gray-300":"bg-gray-900/80 border border-gray-700 hover:border-gray-600","\n        ").concat(b,"\n      "),style:{boxShadow:N(1)},onClick:p,children:[p&&(0,s.jsx)(n.P.div,{className:"absolute inset-0 bg-green-400 opacity-0 group-hover:opacity-5 transition-opacity duration-300",initial:{scale:0,opacity:0},whileHover:{scale:1,opacity:.05},transition:{duration:.3}}),(0,s.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==v||null==(t=v.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent opacity-60")}),(0,s.jsxs)("div",{className:"flex items-start justify-between relative z-10",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium mb-3 ".concat(j.text.secondary),children:d}),(0,s.jsx)("p",{className:"text-3xl font-bold mb-2 ".concat(j.text.primary),style:{fontFamily:"Inter, system-ui, sans-serif"},children:x}),h&&(0,s.jsx)("p",{className:"text-sm ".concat(j.text.tertiary," mb-2"),children:h}),g&&(0,s.jsxs)(n.P.div,{className:"flex items-center mt-3",initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,s.jsxs)("span",{className:"text-sm font-semibold px-2 py-1 rounded-full ".concat(g.isPositive?"text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30":"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30"),children:[g.isPositive?"↗":"↘"," ",g.isPositive?"+":"",g.value,"%"]}),(0,s.jsx)("span",{className:"text-xs ml-2 ".concat(j.text.tertiary),children:"vs last month"})]})]}),m&&(0,s.jsx)(n.P.div,{className:"p-4 rounded-xl ".concat((null==v?void 0:v.bg)||"bg-brand/20"," ").concat((null==v?void 0:v.border)||"border-brand"," border-2 shadow-lg"),whileHover:{scale:1.05,rotate:5},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==v||null==(a=v.bg)?void 0:a.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==v||null==(o=v.bg)?void 0:o.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==v||null==(c=v.bg)?void 0:c.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,s.jsx)(m,{className:"w-7 h-7 ".concat((null==v?void 0:v.icon)||"text-brand")})})]})]})}function c(e){let{title:t,value:a,subtitle:r,icon:n,color:i="green",trend:l}=e;return(0,s.jsx)(o,{title:t,value:a,subtitle:r,icon:n,color:i,trend:l})}function d(e){var t,a,r,o;let{title:c,subtitle:d,icon:x,color:h="blue",onClick:m}=e,g=l[h]||l.blue,{theme:u}=(0,i.DP)(),p=(0,i.Yx)(u);return(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-6,scale:1.02,boxShadow:"light"===u?"0 8px 25px rgba(0, 0, 0, 0.15), 0 16px 40px rgba(0, 0, 0, 0.1)":"0 8px 25px rgba(0, 0, 0, 0.4), 0 16px 40px rgba(0, 0, 0, 0.3)",transition:{duration:.2,ease:"easeOut"}},whileTap:{scale:.98},className:"\n        relative overflow-hidden rounded-xl p-6 cursor-pointer transition-all duration-300 group\n        ".concat("light"===u?"bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300":"bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 hover:border-gray-600","\n      "),style:{boxShadow:"light"===u?"0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05)":"0 2px 8px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(0, 0, 0, 0.2)"},onClick:m,children:[(0,s.jsx)(n.P.div,{className:"absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 ".concat((null==g?void 0:g.bg)||"bg-brand/10"),initial:{scale:0},whileHover:{scale:1},transition:{duration:.3}}),(0,s.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==g||null==(t=g.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent")}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 relative z-10",children:[(0,s.jsx)(n.P.div,{className:"p-4 rounded-xl ".concat((null==g?void 0:g.bg)||"bg-brand/20"," shadow-lg"),whileHover:{scale:1.1,rotate:10},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==g||null==(a=g.bg)?void 0:a.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==g||null==(r=g.bg)?void 0:r.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==g||null==(o=g.bg)?void 0:o.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,s.jsx)(x,{className:"w-6 h-6 ".concat((null==g?void 0:g.icon)||"text-brand")})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg ".concat(p.text.primary," mb-1"),children:c}),(0,s.jsx)("p",{className:"text-sm ".concat(p.text.secondary),children:d})]}),(0,s.jsx)(n.P.div,{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat((null==g?void 0:g.bg)||"bg-brand/20"," opacity-70"),whileHover:{scale:1.2,opacity:1},transition:{duration:.2},children:(0,s.jsx)("svg",{className:"w-4 h-4 ".concat((null==g?void 0:g.icon)||"text-brand"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]})}function x(e){let{title:t,current:a,target:r,unit:i="",color:o="green"}=e,c=Math.min(a/r*100,100),d=l[o]||l.green;return(0,s.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-white font-semibold mb-4",children:t}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,s.jsxs)("span",{className:(null==d?void 0:d.text)||"text-brand",children:[c.toFixed(1),"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-800 rounded-full h-2",children:(0,s.jsx)(n.P.div,{initial:{width:0},animate:{width:"".concat(c,"%")},transition:{duration:1,ease:"easeOut"},className:"h-2 rounded-full bg-gradient-to-r from-".concat(o,"-400 to-").concat(o,"-600")})}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsxs)("span",{className:"text-gray-400",children:[a.toLocaleString(),i," / ",r.toLocaleString(),i]}),(0,s.jsxs)("span",{className:"text-gray-400",children:[(r-a).toLocaleString(),i," remaining"]})]})]})]})}},84158:(e,t,a)=>{Promise.resolve().then(a.bind(a,96431))},87101:(e,t,a)=>{"use strict";a.d(t,{I:()=>i});var s=a(95155);a(12115);var r=a(10351),n=a(35695);function i(e){let{className:t=""}=e,a=(0,n.useRouter)();return(0,s.jsxs)("div",{className:"bg-red-600/10 border border-red-600 rounded-lg p-6 mb-6 flex items-center space-x-3 ".concat(t),children:[(0,s.jsx)(r.p45,{className:"w-6 h-6 text-red-500"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-red-500 font-semibold",children:"Complete Your KYC Verification"}),(0,s.jsx)("p",{className:"text-red-300 text-sm",children:"Verify your identity to unlock higher savings limits and additional features."})]}),(0,s.jsx)("button",{onClick:()=>a.push("/dashboard/kyc"),className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",children:"Verify Now"})]})}},96431:(e,t,a)=>{"use strict";let s,r,n,i,l,o,c,d,x,h,m,g,u,p;a.r(t),a.d(t,{default:()=>M});var b=a(95155),v=a(12115),f=a(10351),j=a(48016);let y=e=>{let{size:t=48,className:a="",showPulse:s=!0}=e;return(0,b.jsx)("div",{className:"flex items-center justify-center ".concat(a),children:(0,b.jsx)("div",{className:"".concat(s?"animate-pulse":""),children:(0,b.jsx)(j.Qh,{size:"md",className:"text-green-400 drop-shadow-lg"})})})};var N=a(68289);try{let e=a(97327);({LineChart:s,Line:r,XAxis:n,YAxis:i,CartesianGrid:l,Tooltip:o,ResponsiveContainer:c,AreaChart:d,Area:x,PieChart:h,Pie:m,Cell:g,BarChart:u,Bar:p}=e)}catch(e){console.warn("Recharts not available:",e)}let w=e=>{let{active:t,payload:a,label:s}=e;return t&&a&&a.length?(0,b.jsxs)("div",{className:"bg-gray-900 border border-gray-700 rounded-lg p-3 shadow-lg",children:[(0,b.jsx)("p",{className:"text-gray-300 text-sm",children:s}),a.map((e,t)=>{var a;return(0,b.jsxs)("p",{className:"text-green-400 font-medium",children:[e.name,": ₦",null==(a=e.value)?void 0:a.toLocaleString()]},t)})]}):null},S=e=>{let{title:t,data:a}=e;return(0,b.jsx)("div",{className:"bg-gray-800/50 rounded-lg p-6 h-64 flex items-center justify-center",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsx)("div",{className:"text-gray-400 mb-2 text-2xl",children:"\uD83D\uDCCA"}),(0,b.jsx)("h3",{className:"text-white font-semibold mb-2",children:t}),(0,b.jsx)("p",{className:"text-gray-400 text-sm",children:"Chart visualization unavailable"}),(0,b.jsxs)("div",{className:"mt-4 text-xs text-gray-500",children:[a.length," data points available"]})]})})};function A(e){var t;let{data:a,title:d,color:x="#10B981",height:h=300}=e;return s&&c?(0,b.jsxs)(N.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[d&&(0,b.jsx)("h3",{className:"text-white font-semibold mb-4",children:d}),(0,b.jsx)(c,{width:"100%",height:h,children:(0,b.jsxs)(s,{data:a,children:[(0,b.jsx)(l,{strokeDasharray:"3 3",stroke:"#374151"}),(0,b.jsx)(n,{dataKey:"name",stroke:"#9CA3AF",fontSize:12}),(0,b.jsx)(i,{stroke:"#9CA3AF",fontSize:12,tickFormatter:e=>"₦".concat(e.toLocaleString())}),(0,b.jsx)(o,{content:(0,b.jsx)(w,{})}),(0,b.jsx)(r,{type:"monotone",dataKey:"value",stroke:x,strokeWidth:3,dot:{fill:x,strokeWidth:2,r:4},activeDot:{r:6,stroke:x,strokeWidth:2}}),(null==(t=a[0])?void 0:t.target)&&(0,b.jsx)(r,{type:"monotone",dataKey:"target",stroke:"#6B7280",strokeWidth:2,strokeDasharray:"5 5",dot:!1})]})})]}):(0,b.jsx)(S,{data:a,title:d})}var D=a(58111),k=a(11846),P=a(87101),R=a(98030),C=a(10924),T=a(52596),I=a(39688);function B(e){let{children:t,className:a,padding:s="md",shadow:r="sm",rounded:n="lg",onClick:i,hoverable:l=!1}=e,o=i?N.P.button:N.P.div;return(0,b.jsx)(o,{whileTap:i?{scale:.98}:void 0,className:function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,I.QP)((0,T.$)(t))}("bg-white border border-gray-200 transition-all duration-200",{sm:"p-3",md:"p-4",lg:"p-6"}[s],{none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"}[r],{none:"",sm:"rounded-sm",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl"}[n],i&&"cursor-pointer",l&&"hover:shadow-md hover:border-gray-300",i&&"active:scale-98",a),onClick:i,children:t})}function H(){let[e,t]=(0,v.useState)(!0),[a,s]=(0,v.useState)(!1),{user:r}=(0,R.A)();if((0,v.useEffect)(()=>{s((0,C.KR)())},[]),!a)return null;let n=[{title:"Total Savings",value:"₦125,450.00",change:"+12.5%",changeType:"positive",icon:f.ARf},{title:"Monthly Goal",value:"₦50,000.00",change:"75% complete",changeType:"neutral",icon:f.x_j},{title:"Interest Earned",value:"₦8,750.00",change:"+5.2%",changeType:"positive",icon:f.z8N}],i=[{title:"Add Money",icon:f.GGD,color:"bg-green-500",href:"/dashboard/deposits"},{title:"Withdraw",icon:f.qdV,color:"bg-blue-500",href:"/dashboard/withdrawals"},{title:"Transfer",icon:f.FMz,color:"bg-purple-500",href:"/dashboard/transfers"},{title:"Savings Plan",icon:f.x_j,color:"bg-orange-500",href:"/dashboard/savings-plans"}];return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"text-center py-6",children:[(0,b.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:["Welcome back, ",(null==r?void 0:r.firstName)||"User","!"]}),(0,b.jsx)("p",{className:"text-gray-600",children:"Your financial journey continues"})]}),(0,b.jsxs)(B,{className:"bg-gradient-to-r from-green-600 to-green-700 text-white border-0",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,b.jsx)("h2",{className:"text-lg font-semibold",children:"Total Balance"}),(0,b.jsx)("button",{onClick:()=>t(!e),className:"p-2 hover:bg-white/10 rounded-full transition-colors",children:e?(0,b.jsx)(f.Vap,{size:20}):(0,b.jsx)(f._NO,{size:20})})]}),(0,b.jsx)("div",{className:"text-3xl font-bold mb-2",children:e?"₦125,450.00":"₦••••••••"}),(0,b.jsx)("p",{className:"text-green-100",children:"+₦12,500 this month"})]}),(0,b.jsx)("div",{className:"grid grid-cols-1 gap-4",children:n.map((e,t)=>(0,b.jsx)(N.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:(0,b.jsx)(B,{hoverable:!0,children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:"p-2 bg-gray-100 rounded-lg",children:(0,b.jsx)(e.icon,{className:"w-5 h-5 text-gray-600"})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"text-sm text-gray-600",children:e.title}),(0,b.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:e.value})]})]}),(0,b.jsx)("div",{className:"text-sm font-medium ".concat("positive"===e.changeType?"text-green-600":"negative"===e.changeType?"text-red-600":"text-gray-600"),children:e.change})]})})},e.title))}),(0,b.jsxs)("div",{children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"Quick Actions"}),(0,b.jsx)("div",{className:"grid grid-cols-2 gap-4",children:i.map((e,t)=>(0,b.jsx)(N.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.1*t},children:(0,b.jsxs)(B,{onClick:()=>window.location.href=e.href,hoverable:!0,className:"text-center",children:[(0,b.jsx)("div",{className:"w-12 h-12 ".concat(e.color," rounded-full flex items-center justify-center mx-auto mb-3"),children:(0,b.jsx)(e.icon,{className:"w-6 h-6 text-white"})}),(0,b.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.title})]})},e.title))})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"}),(0,b.jsx)("button",{className:"text-green-600 text-sm font-medium",children:"View All"})]}),(0,b.jsx)("div",{className:"space-y-3",children:[1,2,3].map((e,t)=>(0,b.jsx)(B,{padding:"sm",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:"w-10 h-10 bg-green-100 rounded-full flex items-center justify-center",children:(0,b.jsx)(f.qdV,{className:"w-5 h-5 text-green-600"})}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"font-medium text-gray-900",children:"Savings Deposit"}),(0,b.jsx)("p",{className:"text-sm text-gray-600",children:"Today, 2:30 PM"})]})]}),(0,b.jsx)("p",{className:"font-semibold text-green-600",children:"+₦5,000"})]})},t))})]}),(0,b.jsx)("div",{className:"h-20"})]})}var z=a(6874),O=a.n(z),E=a(35695);let L=[{name:"Dashboard",href:"/dashboard",icon:f.V5Y},{name:"Savings",href:"/dashboard/savings-plans",icon:f.ARf},{name:"Payments",href:"/dashboard/payments",icon:f.lZI},{name:"Profile",href:"/dashboard/profile",icon:f.JXP}];function F(){var e,t;let[a,s]=(0,v.useState)(!1),r=(0,E.usePathname)(),{user:n,logout:i}=(0,R.A)(),l=[{name:"Home",href:"/dashboard",icon:f.V5Y},{name:"Savings",href:"/dashboard/savings-plans",icon:f.ARf},{name:"Payments",href:"/dashboard/payments",icon:f.lZI},{name:"Profile",href:"/dashboard/profile",icon:f.JXP}],o=e=>!!r&&("/dashboard"===e?"/dashboard"===r:r.startsWith(e)),c=async()=>{await i(),s(!1)};return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"lg:hidden bg-gray-900 border-b border-gray-800 px-4 py-3 flex items-center justify-between",children:[(0,b.jsx)("button",{onClick:()=>s(!a),className:"text-gray-400 hover:text-white transition-colors",children:a?(0,b.jsx)(f.yGN,{size:24}):(0,b.jsx)(f.ND1,{size:24})}),(0,b.jsx)("div",{className:"flex-1 text-center",children:(0,b.jsxs)("h1",{className:"text-white font-medium text-lg",children:["/dashboard"===r&&"Dashboard",(null==r?void 0:r.includes("/savings"))&&"Savings",(null==r?void 0:r.includes("/payments"))&&"Payments",(null==r?void 0:r.includes("/profile"))&&"Profile",(null==r?void 0:r.includes("/settings"))&&"Settings"]})}),(0,b.jsxs)(O(),{href:"/dashboard/notifications",className:"text-gray-400 hover:text-white transition-colors relative",children:[(0,b.jsx)(f.zd,{size:20}),(0,b.jsx)("span",{className:"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:"3"})]})]}),a&&(0,b.jsxs)(N.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"lg:hidden fixed inset-0 z-50 bg-gray-900",children:[(0,b.jsxs)("div",{className:"border-b border-gray-800 px-4 py-3 flex items-center justify-between",children:[(0,b.jsx)("h2",{className:"text-white font-semibold text-lg",children:"Menu"}),(0,b.jsx)("button",{onClick:()=>s(!1),className:"text-gray-400 hover:text-white transition-colors",children:(0,b.jsx)(f.yGN,{size:24})})]}),n&&(0,b.jsx)("div",{className:"px-4 py-6 border-b border-gray-800",children:(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsx)("div",{className:"w-12 h-12 bg-green-600 rounded-full flex items-center justify-center",children:(0,b.jsx)("span",{className:"text-white font-semibold text-lg",children:(null==(e=n.firstName)?void 0:e.charAt(0))||(null==(t=n.email)?void 0:t.charAt(0))||"U"})}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("p",{className:"text-white font-medium",children:[n.firstName," ",n.lastName]}),(0,b.jsx)("p",{className:"text-gray-400 text-sm",children:n.email})]})]})}),(0,b.jsxs)("div",{className:"px-2 py-4 space-y-1",children:[L.map(e=>(0,b.jsxs)(O(),{href:e.href,onClick:()=>s(!1),className:"group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-colors ".concat(o(e.href)?"bg-green-600 text-white":"text-gray-300 hover:bg-gray-800 hover:text-white"),children:[(0,b.jsx)(e.icon,{className:"mr-4 flex-shrink-0 h-6 w-6 ".concat(o(e.href)?"text-white":"text-gray-400 group-hover:text-white")}),e.name]},e.name)),(0,b.jsxs)(O(),{href:"/dashboard/settings",onClick:()=>s(!1),className:"group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-colors text-gray-300 hover:bg-gray-800 hover:text-white",children:[(0,b.jsx)(f.VSk,{className:"mr-4 flex-shrink-0 h-6 w-6 text-gray-400 group-hover:text-white"}),"Settings"]}),(0,b.jsxs)("button",{onClick:c,className:"w-full group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-colors text-red-400 hover:bg-red-600 hover:text-white",children:[(0,b.jsx)(f.QeK,{className:"mr-4 flex-shrink-0 h-6 w-6"}),"Logout"]})]})]}),(0,b.jsx)("div",{className:"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-2 py-1 z-40",children:(0,b.jsx)("div",{className:"flex items-center justify-around",children:l.map(e=>(0,b.jsxs)(O(),{href:e.href,className:"flex flex-col items-center py-2 px-3 rounded-lg transition-colors ".concat(o(e.href)?"text-green-600":"text-gray-500 hover:text-gray-700"),children:[(0,b.jsx)(e.icon,{className:"w-5 h-5 mb-1 ".concat(o(e.href)?"text-green-600":"text-gray-500")}),(0,b.jsx)("span",{className:"text-xs font-medium ".concat(o(e.href)?"text-green-600":"text-gray-500"),children:e.name})]},e.name))})})]})}function G(e){let{children:t,title:a,showBackButton:s=!1,onBackClick:r}=e;return(0,C.KR)()?(0,b.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[(0,b.jsx)(F,{}),(0,b.jsxs)("main",{className:"flex-1 overflow-y-auto",children:[a&&(0,b.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,b.jsxs)("div",{className:"flex items-center",children:[s&&(0,b.jsx)("button",{onClick:r,className:"mr-3 p-2 -ml-2 text-gray-600 hover:text-gray-900",children:(0,b.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,b.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),(0,b.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:a})]})}),(0,b.jsx)("div",{className:"p-4 pb-20",children:t})]})]}):(0,b.jsx)(b.Fragment,{children:t})}var W=a(49697),U=a(13428),V=a(24630);function M(){let[e,t]=(0,v.useState)(null),[a,s]=(0,v.useState)(null),[r,n]=(0,v.useState)([]),[i,l]=(0,v.useState)([]),[o,c]=(0,v.useState)([]),[d,x]=(0,v.useState)(!0),[h,m]=(0,v.useState)(!1),g=(0,U.r)({redirect:!1});return((0,v.useEffect)(()=>{m((0,C.KR)())},[]),(0,v.useEffect)(()=>{!async function(){x(!0);try{console.log("[DASHBOARD] Fetching user profile...");let e=await V.Dv.getCurrentUserProfile();console.log("[DASHBOARD] userService.getCurrentUserProfile:",e),e?(console.log("[DASHBOARD][DEBUG] user fields:",Object.keys(e),e),console.log("[DASHBOARD][DEBUG] user.balance:",e.balance)):console.log("[DASHBOARD][DEBUG] userRes is null or undefined"),t(e),e&&console.log("[DASHBOARD][DEBUG] user fields:",Object.keys(e),e),console.log("[DASHBOARD] Fetching savings summary...");let a=await fetch("".concat("http://localhost:8080/api","/api/savings/summary"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}}),r=null;a.ok?r=await a.json():console.error("[DASHBOARD] Failed to fetch savings summary:",a.status,await a.text()),console.log("[DASHBOARD] savings summary:",r),s(r),console.log("[DASHBOARD] Fetching user savings plans...");let i=await V.TA.getUserSavingsPlans();console.log("[DASHBOARD] savingsService.getUserSavingsPlans:",i),n(i||[]),console.log("[DASHBOARD] Fetching group savings plans...");let o=await V.Iu.getUserGroups();console.log("[DASHBOARD] groupSavingsService.getUserGroups:",o),l(o||[]),console.log("[DASHBOARD] Fetching user transactions...");let d=null;e&&e.id?(d=await V.lD.getUserTransactions(e.id),console.log("[DASHBOARD] transactionsService.getUserTransactions:",d),c(d&&d.transactions||[])):c([])}catch(e){console.error("[DASHBOARD] fetchData error:",e)}x(!1)}()},[]),d)?(0,b.jsx)("div",{className:"flex justify-center items-center min-h-[60vh]",children:(0,b.jsx)(y,{})}):h?(0,b.jsx)(G,{children:(0,b.jsx)(H,{})}):(0,b.jsx)(k.A,{title:"Dashboard",children:(0,b.jsxs)("div",{className:"space-y-6",children:[!g&&(0,b.jsx)(P.I,{}),(0,b.jsxs)("div",{className:"bg-gradient-to-r from-brand/10 to-brand/5 border border-brand/30 rounded-xl p-8 relative overflow-hidden shadow-xl",children:[(0,b.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,b.jsx)("div",{className:"absolute top-4 right-4 w-32 h-32 bg-brand rounded-full blur-3xl"}),(0,b.jsx)("div",{className:"absolute bottom-4 left-4 w-24 h-24 bg-brand rounded-full blur-2xl"})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between relative z-10",children:[(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsxs)("h1",{className:"text-4xl font-inter font-bold text-theme mb-3 tracking-tight",children:["Welcome back, ",(0,b.jsx)("span",{className:"text-brand",children:null==e?void 0:e.firstName}),"! \uD83D\uDC4B"]}),(0,b.jsx)("p",{className:"text-theme-secondary text-lg mb-6 font-inter",children:"Here's an overview of your savings journey and financial progress."}),(0,b.jsxs)("div",{className:"flex items-center space-x-6 text-sm",children:[(0,b.jsxs)("span",{className:"flex items-center space-x-2 text-brand font-medium",children:[(0,b.jsx)("span",{children:"\uD83D\uDCB0"}),(0,b.jsx)("span",{children:"Better Interest Rates"})]}),(0,b.jsxs)("span",{className:"flex items-center space-x-2 text-brand font-medium",children:[(0,b.jsx)("span",{children:"\uD83D\uDCC8"}),(0,b.jsx)("span",{children:"Growing Savings"})]}),(0,b.jsxs)("span",{className:"flex items-center space-x-2 text-brand font-medium",children:[(0,b.jsx)("span",{children:"\uD83C\uDFAF"}),(0,b.jsx)("span",{children:"Goals Achieved"})]})]})]}),(0,b.jsx)("div",{className:"hidden md:flex relative ml-6 justify-center",children:(0,b.jsx)(W.co,{src:"/Celebrating with her iPhone 14 Pro.png",alt:"Happy user celebrating savings success",width:128,height:128,intensity:"light",className:"border-2 border-green-400/50 mx-auto"})})]})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(()=>{let t=e&&void 0!==e.balance&&null!==e.balance&&!isNaN(Number(e.balance))?Number(e.balance):0;return console.log("[DASHBOARD][RENDER] Wallet Balance value for StatCard:",t),(0,b.jsx)(D.hI,{title:"Wallet Balance",value:"₦".concat(t.toLocaleString()),subtitle:"Available balance",icon:f.z8N,color:"green",trend:{value:0,isPositive:!0}})})(),(0,b.jsx)(D.hI,{title:"Total Savings",value:a&&"number"==typeof a.totalTargetAmount?"₦".concat(a.totalTargetAmount.toLocaleString()):"₦0",subtitle:"All plan targets",icon:f.ARf,color:"blue",trend:{value:0,isPositive:!0}}),(0,b.jsx)(D.hI,{title:"Total Saved",value:a&&"number"==typeof a.totalSaved?"₦".concat(a.totalSaved.toLocaleString()):"₦0",subtitle:"Deposited so far",icon:f.eXT,color:"purple"}),(0,b.jsx)(D.hI,{title:"Total Earnings",value:a&&"number"==typeof a.totalInterestEarned?"₦".concat(a.totalInterestEarned.toLocaleString()):"₦0",subtitle:"Interest & bonuses",icon:f.eXT,color:"purple"}),(0,b.jsx)(D.hI,{title:"Active Plans",value:a&&"number"==typeof a.activePlans?a.activePlans:0,subtitle:"Individual & Group",icon:f.eXT,color:"yellow"})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,b.jsx)(D.Lz,{title:"Create Savings Plan",subtitle:"Start a new individual savings plan",icon:f.GGD,color:"green",onClick:()=>window.location.href="/dashboard/savings-plans"}),(0,b.jsx)(D.Lz,{title:"Join Group Savings",subtitle:"Find and join group savings plans",icon:f.cfS,color:"blue",onClick:()=>window.location.href="/dashboard/group-savings"}),(0,b.jsx)(D.Lz,{title:"Make Payment",subtitle:"Add funds to your savings",icon:f.lZI,color:"purple",onClick:()=>window.location.href="/dashboard/payments"})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(console.log("[DASHBOARD][RENDER] savingsPlans for chart:",r),null),(0,b.jsx)(A,{data:r.length>0?r.map(e=>({name:e.name||"Untitled",value:"number"==typeof e.currentAmount?e.currentAmount:0})):[{name:"No Plans",value:0}],title:"Savings Progress",height:300}),(0,b.jsxs)("div",{className:"space-y-4",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Goals Progress"}),r.map((e,t)=>(0,b.jsx)(D.gz,{title:e.name,current:e.currentAmount,target:e.targetAmount,unit:"₦",color:"green"},e.id||"plan-".concat(t)))]})]}),(0,b.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,b.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,b.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Recent Activity"})}),(0,b.jsx)("div",{className:"space-y-3",children:o.slice(0,5).map((e,t)=>{var a;return(0,b.jsxs)("div",{className:"flex items-center justify-between py-3 border-b border-gray-800 last:border-b-0",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,b.jsxs)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("DEPOSIT"===e.type?"bg-green-500/20":"INTEREST"===e.type?"bg-purple-500/20":"WITHDRAWAL"===e.type?"bg-blue-500/20":"bg-yellow-500/20"),children:["DEPOSIT"===e.type&&(0,b.jsx)(f.z8N,{className:"w-4 h-4 text-green-400"}),"INTEREST"===e.type&&(0,b.jsx)(f.ARf,{className:"w-4 h-4 text-purple-400"}),"WITHDRAWAL"===e.type&&(0,b.jsx)(f.lZI,{className:"w-4 h-4 text-blue-400"}),"PENALTY"===e.type&&(0,b.jsx)(f.x_j,{className:"w-4 h-4 text-yellow-400"})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"text-white text-sm font-medium",children:e.type}),(0,b.jsx)("p",{className:"text-gray-400 text-xs",children:e.description||e.planId||e.goalId})]})]}),(0,b.jsxs)("div",{className:"text-right",children:[(0,b.jsxs)("p",{className:"text-green-400 text-sm font-medium",children:["₦",null==(a=e.amount)?void 0:a.toLocaleString()]}),(0,b.jsx)("p",{className:"text-gray-500 text-xs",children:new Date(e.createdAt).toLocaleString()})]})]},t)})})]})]})})}}},e=>{e.O(0,[844,9268,5236,6874,6766,5221,4449,3289,4630,1846,8441,5964,7358],()=>e(e.s=84158)),_N_E=e.O()}]);