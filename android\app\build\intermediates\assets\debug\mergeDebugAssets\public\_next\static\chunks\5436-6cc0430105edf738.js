(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[100,5436],{14915:function(t){t.exports=function(){var t=[function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}var o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},i=(r(n(1)),n(6)),a=r(i),l=r(n(7)),s=r(n(8)),c=r(n(9)),u=r(n(10)),f=r(n(11)),d=r(n(14)),h=[],p=!1,b={offset:120,delay:0,easing:"ease",duration:400,disable:!1,once:!1,startEvent:"DOMContentLoaded",throttleDelay:99,debounceDelay:50,disableMutationObserver:!1},g=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(t&&(p=!0),p)return h=(0,f.default)(h,b),(0,u.default)(h,b.once),h},m=function(){h=(0,d.default)(),g()},y=function(){h.forEach(function(t,e){t.node.removeAttribute("data-aos"),t.node.removeAttribute("data-aos-easing"),t.node.removeAttribute("data-aos-duration"),t.node.removeAttribute("data-aos-delay")})};t.exports={init:function(t){b=o(b,t),h=(0,d.default)();var e,n=document.all&&!window.atob;return!0===(e=b.disable)||"mobile"===e&&c.default.mobile()||"phone"===e&&c.default.phone()||"tablet"===e&&c.default.tablet()||"function"==typeof e&&!0===e()||n?y():(b.disableMutationObserver||s.default.isSupported()||(console.info('\n      aos: MutationObserver is not supported on this browser,\n      code mutations observing has been disabled.\n      You may have to call "refreshHard()" by yourself.\n    '),b.disableMutationObserver=!0),document.querySelector("body").setAttribute("data-aos-easing",b.easing),document.querySelector("body").setAttribute("data-aos-duration",b.duration),document.querySelector("body").setAttribute("data-aos-delay",b.delay),"DOMContentLoaded"===b.startEvent&&["complete","interactive"].indexOf(document.readyState)>-1?g(!0):"load"===b.startEvent?window.addEventListener(b.startEvent,function(){g(!0)}):document.addEventListener(b.startEvent,function(){g(!0)}),window.addEventListener("resize",(0,l.default)(g,b.debounceDelay,!0)),window.addEventListener("orientationchange",(0,l.default)(g,b.debounceDelay,!0)),window.addEventListener("scroll",(0,a.default)(function(){(0,u.default)(h,b.once)},b.throttleDelay)),b.disableMutationObserver||s.default.ready("[data-aos]",m),h)},refresh:g,refreshHard:m}},function(t,e){},,,,,function(t,e){(function(e){"use strict";function n(t){var e=void 0===t?"undefined":o(t);return!!t&&("object"==e||"function"==e)}function r(t){if("number"==typeof t)return t;if("symbol"==(void 0===(e=t)?"undefined":o(e))||e&&"object"==(void 0===e?"undefined":o(e))&&g.call(e)==l)return a;if(n(t)){var e,r="function"==typeof t.valueOf?t.valueOf():t;t=n(r)?r+"":r}if("string"!=typeof t)return 0===t?t:+t;var i=u.test(t=t.replace(s,""));return i||f.test(t)?d(t.slice(2),i?2:8):c.test(t)?a:+t}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i="Expected a function",a=NaN,l="[object Symbol]",s=/^\s+|\s+$/g,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,f=/^0o[0-7]+$/i,d=parseInt,h="object"==(void 0===e?"undefined":o(e))&&e&&e.Object===Object&&e,p="object"==("undefined"==typeof self?"undefined":o(self))&&self&&self.Object===Object&&self,b=h||p||Function("return this")(),g=Object.prototype.toString,m=Math.max,y=Math.min,v=function(){return b.Date.now()};t.exports=function(t,e,o){var a=!0,l=!0;if("function"!=typeof t)throw TypeError(i);return n(o)&&(a="leading"in o?!!o.leading:a,l="trailing"in o?!!o.trailing:l),function(t,e,o){function a(e){var n=f,r=d;return f=d=void 0,x=e,p=t.apply(r,n)}function l(t){var n=t-g,r=t-x;return void 0===g||n>=e||n<0||k&&r>=h}function s(){var t,n,r,o=v();return l(o)?c(o):void(b=setTimeout(s,(t=o-g,n=o-x,r=e-t,k?y(r,h-n):r)))}function c(t){return b=void 0,M&&f?a(t):(f=d=void 0,p)}function u(){var t,n=v(),r=l(n);if(f=arguments,d=this,g=n,r){if(void 0===b)return x=t=g,b=setTimeout(s,e),w?a(t):p;if(k)return b=setTimeout(s,e),a(g)}return void 0===b&&(b=setTimeout(s,e)),p}var f,d,h,p,b,g,x=0,w=!1,k=!1,M=!0;if("function"!=typeof t)throw TypeError(i);return e=r(e)||0,n(o)&&(w=!!o.leading,h=(k="maxWait"in o)?m(r(o.maxWait)||0,e):h,M="trailing"in o?!!o.trailing:M),u.cancel=function(){void 0!==b&&clearTimeout(b),x=0,f=g=d=b=void 0},u.flush=function(){return void 0===b?p:c(v())},u}(t,e,{leading:a,maxWait:e,trailing:l})}}).call(e,function(){return this}())},function(t,e){(function(e){"use strict";function n(t){var e=void 0===t?"undefined":o(t);return!!t&&("object"==e||"function"==e)}function r(t){if("number"==typeof t)return t;if("symbol"==(void 0===(e=t)?"undefined":o(e))||e&&"object"==(void 0===e?"undefined":o(e))&&b.call(e)==a)return i;if(n(t)){var e,r="function"==typeof t.valueOf?t.valueOf():t;t=n(r)?r+"":r}if("string"!=typeof t)return 0===t?t:+t;var d=c.test(t=t.replace(l,""));return d||u.test(t)?f(t.slice(2),d?2:8):s.test(t)?i:+t}var o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i=NaN,a="[object Symbol]",l=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,u=/^0o[0-7]+$/i,f=parseInt,d="object"==(void 0===e?"undefined":o(e))&&e&&e.Object===Object&&e,h="object"==("undefined"==typeof self?"undefined":o(self))&&self&&self.Object===Object&&self,p=d||h||Function("return this")(),b=Object.prototype.toString,g=Math.max,m=Math.min,y=function(){return p.Date.now()};t.exports=function(t,e,o){function i(e){var n=u,r=f;return u=f=void 0,v=e,h=t.apply(r,n)}function a(t){var n=t-b,r=t-v;return void 0===b||n>=e||n<0||w&&r>=d}function l(){var t,n,r,o=y();return a(o)?s(o):void(p=setTimeout(l,(t=o-b,n=o-v,r=e-t,w?m(r,d-n):r)))}function s(t){return p=void 0,k&&u?i(t):(u=f=void 0,h)}function c(){var t,n=y(),r=a(n);if(u=arguments,f=this,b=n,r){if(void 0===p)return v=t=b,p=setTimeout(l,e),x?i(t):h;if(w)return p=setTimeout(l,e),i(b)}return void 0===p&&(p=setTimeout(l,e)),h}var u,f,d,h,p,b,v=0,x=!1,w=!1,k=!0;if("function"!=typeof t)throw TypeError("Expected a function");return e=r(e)||0,n(o)&&(x=!!o.leading,d=(w="maxWait"in o)?g(r(o.maxWait)||0,e):d,k="trailing"in o?!!o.trailing:k),c.cancel=function(){void 0!==p&&clearTimeout(p),v=0,u=b=f=p=void 0},c.flush=function(){return void 0===p?h:s(y())},c}}).call(e,function(){return this}())},function(t,e){"use strict";function n(){return window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver}function r(t){t&&t.forEach(function(t){var e=Array.prototype.slice.call(t.addedNodes),n=Array.prototype.slice.call(t.removedNodes);if(function t(e){var n=void 0,r=void 0;for(n=0;n<e.length;n+=1)if((r=e[n]).dataset&&r.dataset.aos||r.children&&t(r.children))return!0;return!1}(e.concat(n)))return o()})}Object.defineProperty(e,"__esModule",{value:!0});var o=function(){};e.default={isSupported:function(){return!!n()},ready:function(t,e){var i=window.document,a=new(n())(r);o=e,a.observe(i.documentElement,{childList:!0,subtree:!0,removedNodes:!0})}}},function(t,e){"use strict";function n(){return navigator.userAgent||navigator.vendor||window.opera||""}Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i,i=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i,a=/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino|android|ipad|playbook|silk/i,l=/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i;e.default=new(function(){function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function")}return r(t,[{key:"phone",value:function(){var t=n();return!(!o.test(t)&&!i.test(t.substr(0,4)))}},{key:"mobile",value:function(){var t=n();return!(!a.test(t)&&!l.test(t.substr(0,4)))}},{key:"tablet",value:function(){return this.mobile()&&!this.phone()}}]),t}())},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(t,e,n){var r=t.node.getAttribute("data-aos-once");e>t.position?t.node.classList.add("aos-animate"):void 0===r||"false"!==r&&(n||"true"===r)||t.node.classList.remove("aos-animate")};e.default=function(t,e){var r=window.pageYOffset,o=window.innerHeight;t.forEach(function(t,i){n(t,o+r,e)})}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=(r=n(12))&&r.__esModule?r:{default:r};e.default=function(t,e){return t.forEach(function(t,n){t.node.classList.add("aos-init"),t.position=(0,o.default)(t.node,e.offset)}),t}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=(r=n(13))&&r.__esModule?r:{default:r};e.default=function(t,e){var n=0,r=0,i=window.innerHeight,a={offset:t.getAttribute("data-aos-offset"),anchor:t.getAttribute("data-aos-anchor"),anchorPlacement:t.getAttribute("data-aos-anchor-placement")};switch(a.offset&&!isNaN(a.offset)&&(r=parseInt(a.offset)),a.anchor&&document.querySelectorAll(a.anchor)&&(t=document.querySelectorAll(a.anchor)[0]),n=(0,o.default)(t).top,a.anchorPlacement){case"top-bottom":break;case"center-bottom":n+=t.offsetHeight/2;break;case"bottom-bottom":n+=t.offsetHeight;break;case"top-center":n+=i/2;break;case"bottom-center":n+=i/2+t.offsetHeight;break;case"center-center":n+=i/2+t.offsetHeight/2;break;case"top-top":n+=i;break;case"bottom-top":n+=t.offsetHeight+i;break;case"center-top":n+=t.offsetHeight/2+i}return a.anchorPlacement||a.offset||isNaN(e)||(r=e),n+r}},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){for(var e=0,n=0;t&&!isNaN(t.offsetLeft)&&!isNaN(t.offsetTop);)e+=t.offsetLeft-("BODY"!=t.tagName?t.scrollLeft:0),n+=t.offsetTop-("BODY"!=t.tagName?t.scrollTop:0),t=t.offsetParent;return{top:n,left:e}}},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){return t=t||document.querySelectorAll("[data-aos]"),Array.prototype.map.call(t,function(t){return{node:t}})}}];function e(r){if(n[r])return n[r].exports;var o=n[r]={exports:{},id:r,loaded:!1};return t[r].call(o.exports,o,o.exports,e),o.loaded=!0,o.exports}var n={};return e.m=t,e.c=n,e.p="dist/",e(0)}()},21351:(t,e,n)=>{"use strict";let r;function o(t){return t+.5|0}n.d(e,{$:()=>ec,A:()=>tC,B:()=>tR,C:()=>el,D:()=>tk,E:()=>ex,F:()=>H,G:()=>eV,H:()=>ts,I:()=>eH,J:()=>eJ,K:()=>eU,L:()=>tF,M:()=>eD,N:()=>tb,O:()=>L,P:()=>tr,Q:()=>D,R:()=>eM,S:()=>tT,T:()=>to,U:()=>tx,V:()=>ee,W:()=>tP,X:()=>er,Y:()=>es,Z:()=>ed,_:()=>tL,a:()=>ek,a0:()=>ew,a1:()=>tD,a2:()=>tH,a3:()=>t3,a4:()=>Q,a5:()=>J,a6:()=>t4,a7:()=>tt,a8:()=>function t(e,n,r,o){return new Proxy({_cacheable:!1,_proxy:e,_context:n,_subProxy:r,_stack:new Set,_descriptors:ej(e,o),setContext:n=>t(e,n,r,o),override:i=>t(e.override(i),n,r,o)},{deleteProperty:(t,n)=>(delete t[n],delete e[n],!0),get:(e,n,r)=>eS(e,n,()=>(function(e,n,r){let{_proxy:o,_context:i,_subProxy:a,_descriptors:l}=e,s=o[n];return tt(s)&&l.isScriptable(n)&&(s=function(t,e,n,r){let{_proxy:o,_context:i,_subProxy:a,_stack:l}=n;if(l.has(t))throw Error("Recursion detected: "+Array.from(l).join("->")+"->"+t);l.add(t);let s=e(i,a||r);return l.delete(t),eP(t,s)&&(s=eR(o._scopes,o,t,s)),s}(n,s,e,r)),N(s)&&s.length&&(s=function(e,n,r,o){let{_proxy:i,_context:a,_subProxy:l,_descriptors:s}=r;if(void 0!==a.index&&o(e))return n[a.index%n.length];if(I(n[0])){let r=n,o=i._scopes.filter(t=>t!==r);for(let c of(n=[],r)){let r=eR(o,i,e,c);n.push(t(r,a,l&&l[e],s))}}return n}(n,s,e,l.isIndexable)),eP(n,s)&&(s=t(s,i,a&&a[n],l)),s})(e,n,r)),getOwnPropertyDescriptor:(t,n)=>t._descriptors.allKeys?Reflect.has(e,n)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,n),getPrototypeOf:()=>Reflect.getPrototypeOf(e),has:(t,n)=>Reflect.has(e,n),ownKeys:()=>Reflect.ownKeys(e),set:(t,n,r)=>(e[n]=r,delete t[n],!0)})},a9:()=>e_,aA:()=>e5,aB:()=>e2,aC:()=>tB,aD:()=>e8,aE:()=>ea,aF:()=>tM,aG:()=>R,aH:()=>ty,aI:()=>tp,aJ:()=>tm,aK:()=>th,aL:()=>tw,aM:()=>t8,aN:()=>tf,aO:()=>en,aP:()=>tA,aQ:()=>tE,aa:()=>ej,ab:()=>Z,ac:()=>C,ad:()=>tY,ae:()=>eK,af:()=>eo,ag:()=>te,ah:()=>nr,ai:()=>B,aj:()=>tn,ak:()=>tS,al:()=>t_,am:()=>em,an:()=>eY,ao:()=>e9,ap:()=>e7,aq:()=>e0,ar:()=>e1,as:()=>eG,at:()=>eu,au:()=>ef,av:()=>ei,aw:()=>eh,ax:()=>ey,ay:()=>ev,az:()=>e6,b:()=>N,c:()=>tU,d:()=>et,e:()=>tV,f:()=>U,g:()=>W,h:()=>G,i:()=>I,j:()=>eO,k:()=>A,l:()=>tI,m:()=>F,n:()=>Y,o:()=>t5,p:()=>tj,q:()=>tq,r:()=>tz,s:()=>td,t:()=>tv,u:()=>tW,v:()=>z,w:()=>t$,x:()=>tg,y:()=>eI,z:()=>eQ});let i=(t,e,n)=>Math.max(Math.min(t,n),e);function a(t){return i(o(2.55*t),0,255)}function l(t){return i(o(255*t),0,255)}function s(t){return i(o(t/2.55)/100,0,1)}function c(t){return i(o(100*t),0,100)}let u={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},f=[..."0123456789ABCDEF"],d=t=>f[15&t],h=t=>f[(240&t)>>4]+f[15&t],p=t=>(240&t)>>4==(15&t),b=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function g(t,e,n){let r=e*Math.min(n,1-n),o=(e,o=(e+t/30)%12)=>n-r*Math.max(Math.min(o-3,9-o,1),-1);return[o(0),o(8),o(4)]}function m(t,e,n){let r=(r,o=(r+t/60)%6)=>n-n*e*Math.max(Math.min(o,4-o,1),0);return[r(5),r(3),r(1)]}function y(t,e,n){let r,o=g(t,1,.5);for(e+n>1&&(r=1/(e+n),e*=r,n*=r),r=0;r<3;r++)o[r]*=1-e-n,o[r]+=e;return o}function v(t){let e,n,r,o=t.r/255,i=t.g/255,a=t.b/255,l=Math.max(o,i,a),s=Math.min(o,i,a),c=(l+s)/2;l!==s&&(r=l-s,n=c>.5?r/(2-l-s):r/(l+s),e=60*(e=o===l?(i-a)/r+6*(i<a):i===l?(a-o)/r+2:(o-i)/r+4)+.5);return[0|e,n||0,c]}function x(t,e,n,r){return(Array.isArray(e)?t(e[0],e[1],e[2]):t(e,n,r)).map(l)}function w(t){return(t%360+360)%360}let k={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},M={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"},O=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/,_=t=>t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055,j=t=>t<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4);function T(t,e,n){if(t){let r=v(t);r[e]=Math.max(0,Math.min(r[e]+r[e]*n,0===e?360:1)),t.r=(r=x(g,r,void 0,void 0))[0],t.g=r[1],t.b=r[2]}}function P(t,e){return t?Object.assign(e||{},t):t}function S(t){var e={r:0,g:0,b:0,a:255};return Array.isArray(t)?t.length>=3&&(e={r:t[0],g:t[1],b:t[2],a:255},t.length>3&&(e.a=l(t[3]))):(e=P(t,{r:0,g:0,b:0,a:1})).a=l(e.a),e}class E{constructor(t){let e;if(t instanceof E)return t;let n=typeof t;"object"===n?e=S(t):"string"===n&&(e=function(t){var e,n=t.length;return"#"===t[0]&&(4===n||5===n?e={r:255&17*u[t[1]],g:255&17*u[t[2]],b:255&17*u[t[3]],a:5===n?17*u[t[4]]:255}:(7===n||9===n)&&(e={r:u[t[1]]<<4|u[t[2]],g:u[t[3]]<<4|u[t[4]],b:u[t[5]]<<4|u[t[6]],a:9===n?u[t[7]]<<4|u[t[8]]:255})),e}(t)||function(t){r||((r=function(){let t,e,n,r,o,i={},a=Object.keys(M),l=Object.keys(k);for(t=0;t<a.length;t++){for(e=0,r=o=a[t];e<l.length;e++)n=l[e],o=o.replace(n,k[n]);n=parseInt(M[r],16),i[o]=[n>>16&255,n>>8&255,255&n]}return i}()).transparent=[0,0,0,0]);let e=r[t.toLowerCase()];return e&&{r:e[0],g:e[1],b:e[2],a:4===e.length?e[3]:255}}(t)||function(t){return"r"===t.charAt(0)?function(t){let e,n,r,o=O.exec(t),l=255;if(o){if(o[7]!==e){let t=+o[7];l=o[8]?a(t):i(255*t,0,255)}return e=+o[1],n=+o[3],r=+o[5],e=255&(o[2]?a(e):i(e,0,255)),{r:e,g:n=255&(o[4]?a(n):i(n,0,255)),b:r=255&(o[6]?a(r):i(r,0,255)),a:l}}}(t):function(t){let e,n=b.exec(t),r=255;if(!n)return;n[5]!==e&&(r=n[6]?a(+n[5]):l(+n[5]));let o=w(+n[2]),i=n[3]/100,s=n[4]/100;return{r:(e="hwb"===n[1]?x(y,o,i,s):"hsv"===n[1]?x(m,o,i,s):x(g,o,i,s))[0],g:e[1],b:e[2],a:r}}(t)}(t)),this._rgb=e,this._valid=!!e}get valid(){return this._valid}get rgb(){var t=P(this._rgb);return t&&(t.a=s(t.a)),t}set rgb(t){this._rgb=S(t)}rgbString(){var t;return this._valid?(t=this._rgb)&&(t.a<255?`rgba(${t.r}, ${t.g}, ${t.b}, ${s(t.a)})`:`rgb(${t.r}, ${t.g}, ${t.b})`):void 0}hexString(){var t,e;let n,r;return this._valid?(e=p((n=t=this._rgb).r)&&p(n.g)&&p(n.b)&&p(n.a)?d:h,t?"#"+e(t.r)+e(t.g)+e(t.b)+(r=t.a,r<255?e(r):""):void 0):void 0}hslString(){return this._valid?function(t){if(!t)return;let e=v(t),n=e[0],r=c(e[1]),o=c(e[2]);return t.a<255?`hsla(${n}, ${r}%, ${o}%, ${s(t.a)})`:`hsl(${n}, ${r}%, ${o}%)`}(this._rgb):void 0}mix(t,e){if(t){let n,r=this.rgb,o=t.rgb,i=e===n?.5:e,a=2*i-1,l=r.a-o.a,s=((a*l==-1?a:(a+l)/(1+a*l))+1)/2;n=1-s,r.r=255&s*r.r+n*o.r+.5,r.g=255&s*r.g+n*o.g+.5,r.b=255&s*r.b+n*o.b+.5,r.a=i*r.a+(1-i)*o.a,this.rgb=r}return this}interpolate(t,e){return t&&(this._rgb=function(t,e,n){let r=j(s(t.r)),o=j(s(t.g)),i=j(s(t.b));return{r:l(_(r+n*(j(s(e.r))-r))),g:l(_(o+n*(j(s(e.g))-o))),b:l(_(i+n*(j(s(e.b))-i))),a:t.a+n*(e.a-t.a)}}(this._rgb,t._rgb,e)),this}clone(){return new E(this.rgb)}alpha(t){return this._rgb.a=l(t),this}clearer(t){let e=this._rgb;return e.a*=1-t,this}greyscale(){let t=this._rgb,e=o(.3*t.r+.59*t.g+.11*t.b);return t.r=t.g=t.b=e,this}opaquer(t){let e=this._rgb;return e.a*=1+t,this}negate(){let t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return T(this._rgb,2,t),this}darken(t){return T(this._rgb,2,-t),this}saturate(t){return T(this._rgb,1,t),this}desaturate(t){return T(this._rgb,1,-t),this}rotate(t){var e,n;return e=this._rgb,(n=v(e))[0]=w(n[0]+t),e.r=(n=x(g,n,void 0,void 0))[0],e.g=n[1],e.b=n[2],this}}function R(){}let C=(()=>{let t=0;return()=>t++})();function A(t){return null==t}function N(t){if(Array.isArray&&Array.isArray(t))return!0;let e=Object.prototype.toString.call(t);return"[object"===e.slice(0,7)&&"Array]"===e.slice(-6)}function I(t){return null!==t&&"[object Object]"===Object.prototype.toString.call(t)}function W(t){return("number"==typeof t||t instanceof Number)&&isFinite(+t)}function L(t,e){return W(t)?t:e}function z(t,e){return void 0===t?e:t}let F=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100:t/e,Y=(t,e)=>"string"==typeof t&&t.endsWith("%")?parseFloat(t)/100*e:+t;function D(t,e,n){if(t&&"function"==typeof t.call)return t.apply(n,e)}function H(t,e,n,r){let o,i,a;if(N(t))if(i=t.length,r)for(o=i-1;o>=0;o--)e.call(n,t[o],o);else for(o=0;o<i;o++)e.call(n,t[o],o);else if(I(t))for(o=0,i=(a=Object.keys(t)).length;o<i;o++)e.call(n,t[a[o]],a[o])}function B(t,e){let n,r,o,i;if(!t||!e||t.length!==e.length)return!1;for(n=0,r=t.length;n<r;++n)if(o=t[n],i=e[n],o.datasetIndex!==i.datasetIndex||o.index!==i.index)return!1;return!0}function q(t){if(N(t))return t.map(q);if(I(t)){let e=Object.create(null),n=Object.keys(t),r=n.length,o=0;for(;o<r;++o)e[n[o]]=q(t[n[o]]);return e}return t}function $(t){return -1===["__proto__","prototype","constructor"].indexOf(t)}function X(t,e,n,r){if(!$(t))return;let o=e[t],i=n[t];I(o)&&I(i)?Q(o,i,r):e[t]=q(i)}function Q(t,e,n){let r,o=N(e)?e:[e],i=o.length;if(!I(t))return t;let a=(n=n||{}).merger||X;for(let e=0;e<i;++e){if(!I(r=o[e]))continue;let i=Object.keys(r);for(let e=0,o=i.length;e<o;++e)a(i[e],t,r,n)}return t}function Z(t,e){return Q(t,e,{merger:V})}function V(t,e,n){if(!$(t))return;let r=e[t],o=n[t];I(r)&&I(o)?Z(r,o):Object.prototype.hasOwnProperty.call(e,t)||(e[t]=q(o))}let K={"":t=>t,x:t=>t.x,y:t=>t.y};function U(t,e){return(K[e]||(K[e]=function(t){let e=function(t){let e=t.split("."),n=[],r="";for(let t of e)(r+=t).endsWith("\\")?r=r.slice(0,-1)+".":(n.push(r),r="");return n}(t);return t=>{for(let n of e){if(""===n)break;t=t&&t[n]}return t}}(e)))(t)}function J(t){return t.charAt(0).toUpperCase()+t.slice(1)}let G=t=>void 0!==t,tt=t=>"function"==typeof t,te=(t,e)=>{if(t.size!==e.size)return!1;for(let n of t)if(!e.has(n))return!1;return!0};function tn(t){return"mouseup"===t.type||"click"===t.type||"contextmenu"===t.type}let tr=Math.PI,to=2*tr,ti=to+tr,ta=1/0,tl=tr/180,ts=tr/2,tc=tr/4,tu=2*tr/3,tf=Math.log10,td=Math.sign;function th(t,e,n){return Math.abs(t-e)<n}function tp(t){let e=Math.round(t),n=Math.pow(10,Math.floor(tf(t=th(t,e,t/1e3)?e:t))),r=t/n;return(r<=1?1:r<=2?2:r<=5?5:10)*n}function tb(t){let e,n=[],r=Math.sqrt(t);for(e=1;e<r;e++)t%e==0&&(n.push(e),n.push(t/e));return r===(0|r)&&n.push(r),n.sort((t,e)=>t-e).pop(),n}function tg(t){return"symbol"!=typeof t&&("object"!=typeof t||null===t||!!(Symbol.toPrimitive in t||"toString"in t||"valueOf"in t))&&!isNaN(parseFloat(t))&&isFinite(t)}function tm(t,e){let n=Math.round(t);return n-e<=t&&n+e>=t}function ty(t,e,n){let r,o,i;for(r=0,o=t.length;r<o;r++)isNaN(i=t[r][n])||(e.min=Math.min(e.min,i),e.max=Math.max(e.max,i))}function tv(t){return tr/180*t}function tx(t){return 180/tr*t}function tw(t){if(!W(t))return;let e=1,n=0;for(;Math.round(t*e)/e!==t;)e*=10,n++;return n}function tk(t,e){let n=e.x-t.x,r=e.y-t.y,o=Math.sqrt(n*n+r*r),i=Math.atan2(r,n);return i<-.5*tr&&(i+=to),{angle:i,distance:o}}function tM(t,e){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function tO(t,e){return(t-e+ti)%to-tr}function t_(t){return(t%to+to)%to}function tj(t,e,n,r){let o=t_(t),i=t_(e),a=t_(n),l=t_(i-o),s=t_(a-o),c=t_(o-i),u=t_(o-a);return o===i||o===a||r&&i===a||l>s&&c<u}function tT(t,e,n){return Math.max(e,Math.min(n,t))}function tP(t){return tT(t,-32768,32767)}function tS(t,e,n,r=1e-6){return t>=Math.min(e,n)-r&&t<=Math.max(e,n)+r}function tE(t,e,n){let r;n=n||(n=>t[n]<e);let o=t.length-1,i=0;for(;o-i>1;)n(r=i+o>>1)?i=r:o=r;return{lo:i,hi:o}}let tR=(t,e,n,r)=>tE(t,n,r?r=>{let o=t[r][e];return o<n||o===n&&t[r+1][e]===n}:r=>t[r][e]<n),tC=(t,e,n)=>tE(t,n,r=>t[r][e]>=n);function tA(t,e,n){let r=0,o=t.length;for(;r<o&&t[r]<e;)r++;for(;o>r&&t[o-1]>n;)o--;return r>0||o<t.length?t.slice(r,o):t}let tN=["push","pop","shift","splice","unshift"];function tI(t,e){if(t._chartjs)return void t._chartjs.listeners.push(e);Object.defineProperty(t,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[e]}}),tN.forEach(e=>{let n="_onData"+J(e),r=t[e];Object.defineProperty(t,e,{configurable:!0,enumerable:!1,value(...e){let o=r.apply(this,e);return t._chartjs.listeners.forEach(t=>{"function"==typeof t[n]&&t[n](...e)}),o}})})}function tW(t,e){let n=t._chartjs;if(!n)return;let r=n.listeners,o=r.indexOf(e);-1!==o&&r.splice(o,1),r.length>0||(tN.forEach(e=>{delete t[e]}),delete t._chartjs)}function tL(t){let e=new Set(t);return e.size===t.length?t:Array.from(e)}let tz="undefined"==typeof window?function(t){return t()}:window.requestAnimationFrame;function tF(t,e){let n=[],r=!1;return function(...o){n=o,r||(r=!0,tz.call(window,()=>{r=!1,t.apply(e,n)}))}}function tY(t,e){let n;return function(...r){return e?(clearTimeout(n),n=setTimeout(t,e,r)):t.apply(this,r),e}}let tD=t=>"start"===t?"left":"end"===t?"right":"center",tH=(t,e,n)=>"start"===t?e:"end"===t?n:(e+n)/2,tB=(t,e,n,r)=>t===(r?"left":"right")?n:"center"===t?(e+n)/2:e;function tq(t,e,n){let r=e.length,o=0,i=r;if(t._sorted){let{iScale:a,vScale:l,_parsed:s}=t,c=t.dataset&&t.dataset.options?t.dataset.options.spanGaps:null,u=a.axis,{min:f,max:d,minDefined:h,maxDefined:p}=a.getUserBounds();if(h){if(o=Math.min(tR(s,u,f).lo,n?r:tR(e,u,a.getPixelForValue(f)).lo),c){let t=s.slice(0,o+1).reverse().findIndex(t=>!A(t[l.axis]));o-=Math.max(0,t)}o=tT(o,0,r-1)}if(p){let t=Math.max(tR(s,a.axis,d,!0).hi+1,n?0:tR(e,u,a.getPixelForValue(d),!0).hi+1);if(c){let e=s.slice(t-1).findIndex(t=>!A(t[l.axis]));t+=Math.max(0,e)}i=tT(t,o,r)-o}else i=r-o}return{start:o,count:i}}function t$(t){let{xScale:e,yScale:n,_scaleRanges:r}=t,o={xmin:e.min,xmax:e.max,ymin:n.min,ymax:n.max};if(!r)return t._scaleRanges=o,!0;let i=r.xmin!==e.min||r.xmax!==e.max||r.ymin!==n.min||r.ymax!==n.max;return Object.assign(r,o),i}let tX=t=>0===t||1===t,tQ=(t,e,n)=>-(Math.pow(2,10*(t-=1))*Math.sin((t-e)*to/n)),tZ=(t,e,n)=>Math.pow(2,-10*t)*Math.sin((t-e)*to/n)+1,tV={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>-t*(t-2),easeInOutQuad:t=>(t/=.5)<1?.5*t*t:-.5*(--t*(t-2)-1),easeInCubic:t=>t*t*t,easeOutCubic:t=>(t-=1)*t*t+1,easeInOutCubic:t=>(t/=.5)<1?.5*t*t*t:.5*((t-=2)*t*t+2),easeInQuart:t=>t*t*t*t,easeOutQuart:t=>-((t-=1)*t*t*t-1),easeInOutQuart:t=>(t/=.5)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2),easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>(t-=1)*t*t*t*t+1,easeInOutQuint:t=>(t/=.5)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2),easeInSine:t=>-Math.cos(t*ts)+1,easeOutSine:t=>Math.sin(t*ts),easeInOutSine:t=>-.5*(Math.cos(tr*t)-1),easeInExpo:t=>0===t?0:Math.pow(2,10*(t-1)),easeOutExpo:t=>1===t?1:-Math.pow(2,-10*t)+1,easeInOutExpo:t=>tX(t)?t:t<.5?.5*Math.pow(2,10*(2*t-1)):.5*(-Math.pow(2,-10*(2*t-1))+2),easeInCirc:t=>t>=1?t:-(Math.sqrt(1-t*t)-1),easeOutCirc:t=>Math.sqrt(1-(t-=1)*t),easeInOutCirc:t=>(t/=.5)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1),easeInElastic:t=>tX(t)?t:tQ(t,.075,.3),easeOutElastic:t=>tX(t)?t:tZ(t,.075,.3),easeInOutElastic:t=>tX(t)?t:t<.5?.5*tQ(2*t,.1125,.45):.5+.5*tZ(2*t-1,.1125,.45),easeInBack:t=>t*t*(2.70158*t-1.70158),easeOutBack:t=>(t-=1)*t*(2.70158*t********)+1,easeInOutBack(t){let e=1.70158;return(t/=.5)<1?.5*(t*t*(((e*=1.525)+1)*t-e)):.5*((t-=2)*t*(((e*=1.525)+1)*t+e)+2)},easeInBounce:t=>1-tV.easeOutBounce(1-t),easeOutBounce:t=>t<.36363636363636365?7.5625*t*t:t<.7272727272727273?7.5625*(t-=.5454545454545454)*t+.75:t<.9090909090909091?7.5625*(t-=.8181818181818182)*t+.9375:7.5625*(t-=.9545454545454546)*t+.984375,easeInOutBounce:t=>t<.5?.5*tV.easeInBounce(2*t):.5*tV.easeOutBounce(2*t-1)+.5};function tK(t){if(t&&"object"==typeof t){let e=t.toString();return"[object CanvasPattern]"===e||"[object CanvasGradient]"===e}return!1}function tU(t){return tK(t)?t:new E(t)}function tJ(t){return tK(t)?t:new E(t).saturate(.5).darken(.1).hexString()}let tG=["x","y","borderWidth","radius","tension"],t0=["color","borderColor","backgroundColor"],t1=new Map;function t5(t,e,n){return(function(t,e){let n=t+JSON.stringify(e=e||{}),r=t1.get(n);return r||(r=new Intl.NumberFormat(t,e),t1.set(n,r)),r})(e,n).format(t)}let t2={values:t=>N(t)?t:""+t,numeric(t,e,n){let r;if(0===t)return"0";let o=this.chart.options.locale,i=t;if(n.length>1){var a,l;let e,o=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(o<1e-4||o>1e15)&&(r="scientific"),a=t,Math.abs(e=(l=n).length>3?l[2].value-l[1].value:l[1].value-l[0].value)>=1&&a!==Math.floor(a)&&(e=a-Math.floor(a)),i=e}let s=tf(Math.abs(i)),c=isNaN(s)?1:Math.max(Math.min(-1*Math.floor(s),20),0),u={notation:r,minimumFractionDigits:c,maximumFractionDigits:c};return Object.assign(u,this.options.ticks.format),t5(t,o,u)},logarithmic(t,e,n){return 0===t?"0":[1,2,3,5,10,15].includes(n[e].significand||t/Math.pow(10,Math.floor(tf(t))))||e>.8*n.length?t2.numeric.call(this,t,e,n):""}};var t8={formatters:t2};let t3=Object.create(null),t4=Object.create(null);function t6(t,e){if(!e)return t;let n=e.split(".");for(let e=0,r=n.length;e<r;++e){let r=n[e];t=t[r]||(t[r]=Object.create(null))}return t}function t7(t,e,n){return"string"==typeof e?Q(t6(t,e),n):Q(t6(t,""),e)}class t9{constructor(t,e){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=t=>t.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(t,e)=>tJ(e.backgroundColor),this.hoverBorderColor=(t,e)=>tJ(e.borderColor),this.hoverColor=(t,e)=>tJ(e.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(e)}set(t,e){return t7(this,t,e)}get(t){return t6(this,t)}describe(t,e){return t7(t4,t,e)}override(t,e){return t7(t3,t,e)}route(t,e,n,r){let o=t6(this,t),i=t6(this,n),a="_"+e;Object.defineProperties(o,{[a]:{value:o[e],writable:!0},[e]:{enumerable:!0,get(){let t=this[a],e=i[r];return I(t)?Object.assign({},e,t):z(t,e)},set(t){this[a]=t}}})}apply(t){t.forEach(t=>t(this))}}var et=new t9({_scriptable:t=>!t.startsWith("on"),_indexable:t=>"events"!==t,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(t){t.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),t.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>"onProgress"!==t&&"onComplete"!==t&&"fn"!==t}),t.set("animations",{colors:{type:"color",properties:t0},numbers:{type:"number",properties:tG}}),t.describe("animations",{_fallback:"animation"}),t.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>0|t}}}})},function(t){t.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(t){t.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,e)=>e.lineWidth,tickColor:(t,e)=>e.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:t8.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),t.route("scale.ticks","color","","color"),t.route("scale.grid","color","","borderColor"),t.route("scale.border","color","","borderColor"),t.route("scale.title","color","","color"),t.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&"callback"!==t&&"parser"!==t,_indexable:t=>"borderDash"!==t&&"tickBorderDash"!==t&&"dash"!==t}),t.describe("scales",{_fallback:"scale"}),t.describe("scale.ticks",{_scriptable:t=>"backdropPadding"!==t&&"callback"!==t,_indexable:t=>"backdropPadding"!==t})}]);function ee(t,e,n,r,o){let i=e[o];return i||(i=e[o]=t.measureText(o).width,n.push(o)),i>r&&(r=i),r}function en(t,e,n,r){let o,i,a,l,s,c=(r=r||{}).data=r.data||{},u=r.garbageCollect=r.garbageCollect||[];r.font!==e&&(c=r.data={},u=r.garbageCollect=[],r.font=e),t.save(),t.font=e;let f=0,d=n.length;for(o=0;o<d;o++)if(null==(l=n[o])||N(l)){if(N(l))for(i=0,a=l.length;i<a;i++)null==(s=l[i])||N(s)||(f=ee(t,c,u,f,s))}else f=ee(t,c,u,f,l);t.restore();let h=u.length/2;if(h>n.length){for(o=0;o<h;o++)delete c[u[o]];u.splice(0,h)}return f}function er(t,e,n){let r=t.currentDevicePixelRatio,o=0!==n?Math.max(n/2,.5):0;return Math.round((e-o)*r)/r+o}function eo(t,e){(e||t)&&((e=e||t.getContext("2d")).save(),e.resetTransform(),e.clearRect(0,0,t.width,t.height),e.restore())}function ei(t,e,n,r){ea(t,e,n,r,null)}function ea(t,e,n,r,o){let i,a,l,s,c,u,f,d,h=e.pointStyle,p=e.rotation,b=e.radius,g=(p||0)*tl;if(h&&"object"==typeof h&&("[object HTMLImageElement]"===(i=h.toString())||"[object HTMLCanvasElement]"===i)){t.save(),t.translate(n,r),t.rotate(g),t.drawImage(h,-h.width/2,-h.height/2,h.width,h.height),t.restore();return}if(!isNaN(b)&&!(b<=0)){switch(t.beginPath(),h){default:o?t.ellipse(n,r,o/2,b,0,0,to):t.arc(n,r,b,0,to),t.closePath();break;case"triangle":u=o?o/2:b,t.moveTo(n+Math.sin(g)*u,r-Math.cos(g)*b),g+=tu,t.lineTo(n+Math.sin(g)*u,r-Math.cos(g)*b),g+=tu,t.lineTo(n+Math.sin(g)*u,r-Math.cos(g)*b),t.closePath();break;case"rectRounded":c=.516*b,a=Math.cos(g+tc)*(s=b-c),f=Math.cos(g+tc)*(o?o/2-c:s),l=Math.sin(g+tc)*s,d=Math.sin(g+tc)*(o?o/2-c:s),t.arc(n-f,r-l,c,g-tr,g-ts),t.arc(n+d,r-a,c,g-ts,g),t.arc(n+f,r+l,c,g,g+ts),t.arc(n-d,r+a,c,g+ts,g+tr),t.closePath();break;case"rect":if(!p){s=Math.SQRT1_2*b,u=o?o/2:s,t.rect(n-u,r-s,2*u,2*s);break}g+=tc;case"rectRot":f=Math.cos(g)*(o?o/2:b),a=Math.cos(g)*b,l=Math.sin(g)*b,d=Math.sin(g)*(o?o/2:b),t.moveTo(n-f,r-l),t.lineTo(n+d,r-a),t.lineTo(n+f,r+l),t.lineTo(n-d,r+a),t.closePath();break;case"crossRot":g+=tc;case"cross":f=Math.cos(g)*(o?o/2:b),a=Math.cos(g)*b,l=Math.sin(g)*b,d=Math.sin(g)*(o?o/2:b),t.moveTo(n-f,r-l),t.lineTo(n+f,r+l),t.moveTo(n+d,r-a),t.lineTo(n-d,r+a);break;case"star":f=Math.cos(g)*(o?o/2:b),a=Math.cos(g)*b,l=Math.sin(g)*b,d=Math.sin(g)*(o?o/2:b),t.moveTo(n-f,r-l),t.lineTo(n+f,r+l),t.moveTo(n+d,r-a),t.lineTo(n-d,r+a),g+=tc,f=Math.cos(g)*(o?o/2:b),a=Math.cos(g)*b,l=Math.sin(g)*b,d=Math.sin(g)*(o?o/2:b),t.moveTo(n-f,r-l),t.lineTo(n+f,r+l),t.moveTo(n+d,r-a),t.lineTo(n-d,r+a);break;case"line":a=o?o/2:Math.cos(g)*b,l=Math.sin(g)*b,t.moveTo(n-a,r-l),t.lineTo(n+a,r+l);break;case"dash":t.moveTo(n,r),t.lineTo(n+Math.cos(g)*(o?o/2:b),r+Math.sin(g)*b);break;case!1:t.closePath()}t.fill(),e.borderWidth>0&&t.stroke()}}function el(t,e,n){return n=n||.5,!e||t&&t.x>e.left-n&&t.x<e.right+n&&t.y>e.top-n&&t.y<e.bottom+n}function es(t,e){t.save(),t.beginPath(),t.rect(e.left,e.top,e.right-e.left,e.bottom-e.top),t.clip()}function ec(t){t.restore()}function eu(t,e,n,r,o){if(!e)return t.lineTo(n.x,n.y);if("middle"===o){let r=(e.x+n.x)/2;t.lineTo(r,e.y),t.lineTo(r,n.y)}else"after"===o!=!!r?t.lineTo(e.x,n.y):t.lineTo(n.x,e.y);t.lineTo(n.x,n.y)}function ef(t,e,n,r){if(!e)return t.lineTo(n.x,n.y);t.bezierCurveTo(r?e.cp1x:e.cp2x,r?e.cp1y:e.cp2y,r?n.cp2x:n.cp1x,r?n.cp2y:n.cp1y,n.x,n.y)}function ed(t,e,n,r,o,i={}){let a,l,s=N(e)?e:[e],c=i.strokeWidth>0&&""!==i.strokeColor;for(t.save(),t.font=o.string,i.translation&&t.translate(i.translation[0],i.translation[1]),A(i.rotation)||t.rotate(i.rotation),i.color&&(t.fillStyle=i.color),i.textAlign&&(t.textAlign=i.textAlign),i.textBaseline&&(t.textBaseline=i.textBaseline),a=0;a<s.length;++a)l=s[a],i.backdrop&&function(t,e){let n=t.fillStyle;t.fillStyle=e.color,t.fillRect(e.left,e.top,e.width,e.height),t.fillStyle=n}(t,i.backdrop),c&&(i.strokeColor&&(t.strokeStyle=i.strokeColor),A(i.strokeWidth)||(t.lineWidth=i.strokeWidth),t.strokeText(l,n,r,i.maxWidth)),t.fillText(l,n,r,i.maxWidth),function(t,e,n,r,o){if(o.strikethrough||o.underline){let i=t.measureText(r),a=e-i.actualBoundingBoxLeft,l=e+i.actualBoundingBoxRight,s=n-i.actualBoundingBoxAscent,c=n+i.actualBoundingBoxDescent,u=o.strikethrough?(s+c)/2:c;t.strokeStyle=t.fillStyle,t.beginPath(),t.lineWidth=o.decorationWidth||2,t.moveTo(a,u),t.lineTo(l,u),t.stroke()}}(t,n,r,l,i),r+=Number(o.lineHeight);t.restore()}function eh(t,e){let{x:n,y:r,w:o,h:i,radius:a}=e;t.arc(n+a.topLeft,r+a.topLeft,a.topLeft,1.5*tr,tr,!0),t.lineTo(n,r+i-a.bottomLeft),t.arc(n+a.bottomLeft,r+i-a.bottomLeft,a.bottomLeft,tr,ts,!0),t.lineTo(n+o-a.bottomRight,r+i),t.arc(n+o-a.bottomRight,r+i-a.bottomRight,a.bottomRight,ts,0,!0),t.lineTo(n+o,r+a.topRight),t.arc(n+o-a.topRight,r+a.topRight,a.topRight,0,-ts,!0),t.lineTo(n+a.topLeft,r)}let ep=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,eb=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/,eg=t=>+t||0;function em(t,e){let n={},r=I(e),o=r?Object.keys(e):e,i=I(t)?r?n=>z(t[n],t[e[n]]):e=>t[e]:()=>t;for(let t of o)n[t]=eg(i(t));return n}function ey(t){return em(t,{top:"y",right:"x",bottom:"y",left:"x"})}function ev(t){return em(t,["topLeft","topRight","bottomLeft","bottomRight"])}function ex(t){let e=ey(t);return e.width=e.left+e.right,e.height=e.top+e.bottom,e}function ew(t,e){t=t||{},e=e||et.font;let n=z(t.size,e.size);"string"==typeof n&&(n=parseInt(n,10));let r=z(t.style,e.style);r&&!(""+r).match(eb)&&(console.warn('Invalid font style specified: "'+r+'"'),r=void 0);let o={family:z(t.family,e.family),lineHeight:function(t,e){let n=(""+t).match(ep);if(!n||"normal"===n[1])return 1.2*e;switch(t=+n[2],n[3]){case"px":return t;case"%":t/=100}return e*t}(z(t.lineHeight,e.lineHeight),n),size:n,style:r,weight:z(t.weight,e.weight),string:""};return o.string=!o||A(o.size)||A(o.family)?null:(o.style?o.style+" ":"")+(o.weight?o.weight+" ":"")+o.size+"px "+o.family,o}function ek(t,e,n,r){let o,i,a,l=!0;for(o=0,i=t.length;o<i;++o)if(void 0!==(a=t[o])&&(void 0!==e&&"function"==typeof a&&(a=a(e),l=!1),void 0!==n&&N(a)&&(a=a[n%a.length],l=!1),void 0!==a))return r&&!l&&(r.cacheable=!1),a}function eM(t,e,n){let{min:r,max:o}=t,i=Y(e,(o-r)/2),a=(t,e)=>n&&0===t?0:t+e;return{min:a(r,-Math.abs(i)),max:a(o,i)}}function eO(t,e){return Object.assign(Object.create(t),e)}function e_(t,e=[""],n,r,o=()=>t[0]){let i=n||t;return void 0===r&&(r=eA("_fallback",t)),new Proxy({[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:t,_rootScopes:i,_fallback:r,_getTarget:o,override:n=>e_([n,...t],e,i,r)},{deleteProperty:(e,n)=>(delete e[n],delete e._keys,delete t[0][n],!0),get:(n,r)=>eS(n,r,()=>(function(t,e,n,r){let o;for(let i of e)if(void 0!==(o=eA(eT(i,t),n)))return eP(t,o)?eR(n,r,t,o):o})(r,e,t,n)),getOwnPropertyDescriptor:(t,e)=>Reflect.getOwnPropertyDescriptor(t._scopes[0],e),getPrototypeOf:()=>Reflect.getPrototypeOf(t[0]),has:(t,e)=>eN(t).includes(e),ownKeys:t=>eN(t),set(t,e,n){let r=t._storage||(t._storage=o());return t[e]=r[e]=n,delete t._keys,!0}})}function ej(t,e={scriptable:!0,indexable:!0}){let{_scriptable:n=e.scriptable,_indexable:r=e.indexable,_allKeys:o=e.allKeys}=t;return{allKeys:o,scriptable:n,indexable:r,isScriptable:tt(n)?n:()=>n,isIndexable:tt(r)?r:()=>r}}let eT=(t,e)=>t?t+J(e):e,eP=(t,e)=>I(e)&&"adapters"!==t&&(null===Object.getPrototypeOf(e)||e.constructor===Object);function eS(t,e,n){if(Object.prototype.hasOwnProperty.call(t,e)||"constructor"===e)return t[e];let r=n();return t[e]=r,r}let eE=(t,e)=>!0===t?e:"string"==typeof t?U(e,t):void 0;function eR(t,e,n,r){var o;let i=e._rootScopes,a=(o=e._fallback,tt(o)?o(n,r):o),l=[...t,...i],s=new Set;s.add(r);let c=eC(s,l,n,a||n,r);return null!==c&&(void 0===a||a===n||null!==(c=eC(s,l,a,c,r)))&&e_(Array.from(s),[""],i,a,()=>(function(t,e,n){let r=t._getTarget();e in r||(r[e]={});let o=r[e];return N(o)&&I(n)?n:o||{}})(e,n,r))}function eC(t,e,n,r,o){for(;n;)n=function(t,e,n,r,o){for(let a of e){let e=eE(n,a);if(e){var i;t.add(e);let a=(i=e._fallback,tt(i)?i(n,o):i);if(void 0!==a&&a!==n&&a!==r)return a}else if(!1===e&&void 0!==r&&n!==r)return null}return!1}(t,e,n,r,o);return n}function eA(t,e){for(let n of e){if(!n)continue;let e=n[t];if(void 0!==e)return e}}function eN(t){let e=t._keys;return e||(e=t._keys=function(t){let e=new Set;for(let n of t)for(let t of Object.keys(n).filter(t=>!t.startsWith("_")))e.add(t);return Array.from(e)}(t._scopes)),e}function eI(t,e,n,r){let o,i,a,{iScale:l}=t,{key:s="r"}=this._parsing,c=Array(r);for(o=0;o<r;++o)a=e[i=o+n],c[o]={r:l.parse(U(a,s),i)};return c}let eW=Number.EPSILON||1e-14,eL=(t,e)=>e<t.length&&!t[e].skip&&t[e],ez=t=>"x"===t?"y":"x";function eF(t,e,n){return Math.max(Math.min(t,n),e)}function eY(t,e,n,r,o){let i,a,l,s;if(e.spanGaps&&(t=t.filter(t=>!t.skip)),"monotone"===e.cubicInterpolationMode)!function(t,e="x"){let n,r,o,i=ez(e),a=t.length,l=Array(a).fill(0),s=Array(a),c=eL(t,0);for(n=0;n<a;++n)if(r=o,o=c,c=eL(t,n+1),o){if(c){let t=c[e]-o[e];l[n]=0!==t?(c[i]-o[i])/t:0}s[n]=r?c?td(l[n-1])!==td(l[n])?0:(l[n-1]+l[n])/2:l[n-1]:l[n]}!function(t,e,n){let r,o,i,a,l,s=t.length,c=eL(t,0);for(let u=0;u<s-1;++u)if(l=c,c=eL(t,u+1),l&&c){if(th(e[u],0,eW)){n[u]=n[u+1]=0;continue}(a=Math.pow(r=n[u]/e[u],2)+Math.pow(o=n[u+1]/e[u],2))<=9||(i=3/Math.sqrt(a),n[u]=r*i*e[u],n[u+1]=o*i*e[u])}}(t,l,s),function(t,e,n="x"){let r,o,i,a=ez(n),l=t.length,s=eL(t,0);for(let c=0;c<l;++c){if(o=i,i=s,s=eL(t,c+1),!i)continue;let l=i[n],u=i[a];o&&(r=(l-o[n])/3,i[`cp1${n}`]=l-r,i[`cp1${a}`]=u-r*e[c]),s&&(r=(s[n]-l)/3,i[`cp2${n}`]=l+r,i[`cp2${a}`]=u+r*e[c])}}(t,s,e)}(t,o);else{let n=r?t[t.length-1]:t[0];for(i=0,a=t.length;i<a;++i)s=function(t,e,n,r){let o=t.skip?e:t,i=n.skip?e:n,a=tM(e,o),l=tM(i,e),s=a/(a+l),c=l/(a+l);s=isNaN(s)?0:s,c=isNaN(c)?0:c;let u=r*s,f=r*c;return{previous:{x:e.x-u*(i.x-o.x),y:e.y-u*(i.y-o.y)},next:{x:e.x+f*(i.x-o.x),y:e.y+f*(i.y-o.y)}}}(n,l=t[i],t[Math.min(i+1,a-!r)%a],e.tension),l.cp1x=s.previous.x,l.cp1y=s.previous.y,l.cp2x=s.next.x,l.cp2y=s.next.y,n=l}e.capBezierPoints&&function(t,e){let n,r,o,i,a,l=el(t[0],e);for(n=0,r=t.length;n<r;++n)a=i,i=l,l=n<r-1&&el(t[n+1],e),i&&(o=t[n],a&&(o.cp1x=eF(o.cp1x,e.left,e.right),o.cp1y=eF(o.cp1y,e.top,e.bottom)),l&&(o.cp2x=eF(o.cp2x,e.left,e.right),o.cp2y=eF(o.cp2y,e.top,e.bottom)))}(t,n)}function eD(){return"undefined"!=typeof window&&"undefined"!=typeof document}function eH(t){let e=t.parentNode;return e&&"[object ShadowRoot]"===e.toString()&&(e=e.host),e}function eB(t,e,n){let r;return"string"==typeof t?(r=parseInt(t,10),-1!==t.indexOf("%")&&(r=r/100*e.parentNode[n])):r=t,r}let eq=t=>t.ownerDocument.defaultView.getComputedStyle(t,null),e$=["top","right","bottom","left"];function eX(t,e,n){let r={};n=n?"-"+n:"";for(let o=0;o<4;o++){let i=e$[o];r[i]=parseFloat(t[e+"-"+i+n])||0}return r.width=r.left+r.right,r.height=r.top+r.bottom,r}function eQ(t,e){if("native"in t)return t;let{canvas:n,currentDevicePixelRatio:r}=e,o=eq(n),i="border-box"===o.boxSizing,a=eX(o,"padding"),l=eX(o,"border","width"),{x:s,y:c,box:u}=function(t,e){let n,r,o,i=t.touches,a=i&&i.length?i[0]:t,{offsetX:l,offsetY:s}=a,c=!1;if(o=t.target,(l>0||s>0)&&(!o||!o.shadowRoot))n=l,r=s;else{let t=e.getBoundingClientRect();n=a.clientX-t.left,r=a.clientY-t.top,c=!0}return{x:n,y:r,box:c}}(t,n),f=a.left+(u&&l.left),d=a.top+(u&&l.top),{width:h,height:p}=e;return i&&(h-=a.width+l.width,p-=a.height+l.height),{x:Math.round((s-f)/h*n.width/r),y:Math.round((c-d)/p*n.height/r)}}let eZ=t=>Math.round(10*t)/10;function eV(t,e,n,r){let o=eq(t),i=eX(o,"margin"),a=eB(o.maxWidth,t,"clientWidth")||ta,l=eB(o.maxHeight,t,"clientHeight")||ta,s=function(t,e,n){let r,o;if(void 0===e||void 0===n){let i=t&&eH(t);if(i){let t=i.getBoundingClientRect(),a=eq(i),l=eX(a,"border","width"),s=eX(a,"padding");e=t.width-s.width-l.width,n=t.height-s.height-l.height,r=eB(a.maxWidth,i,"clientWidth"),o=eB(a.maxHeight,i,"clientHeight")}else e=t.clientWidth,n=t.clientHeight}return{width:e,height:n,maxWidth:r||ta,maxHeight:o||ta}}(t,e,n),{width:c,height:u}=s;if("content-box"===o.boxSizing){let t=eX(o,"border","width"),e=eX(o,"padding");c-=e.width+t.width,u-=e.height+t.height}return c=Math.max(0,c-i.width),u=Math.max(0,r?c/r:u-i.height),c=eZ(Math.min(c,a,s.maxWidth)),u=eZ(Math.min(u,l,s.maxHeight)),c&&!u&&(u=eZ(c/2)),(void 0!==e||void 0!==n)&&r&&s.height&&u>s.height&&(c=eZ(Math.floor((u=s.height)*r))),{width:c,height:u}}function eK(t,e,n){let r=e||1,o=Math.floor(t.height*r),i=Math.floor(t.width*r);t.height=Math.floor(t.height),t.width=Math.floor(t.width);let a=t.canvas;return a.style&&(n||!a.style.height&&!a.style.width)&&(a.style.height=`${t.height}px`,a.style.width=`${t.width}px`),(t.currentDevicePixelRatio!==r||a.height!==o||a.width!==i)&&(t.currentDevicePixelRatio=r,a.height=o,a.width=i,t.ctx.setTransform(r,0,0,r,0,0),!0)}let eU=function(){let t=!1;try{let e={get passive(){return t=!0,!1}};eD()&&(window.addEventListener("test",null,e),window.removeEventListener("test",null,e))}catch(t){}return t}();function eJ(t,e){let n=eq(t).getPropertyValue(e),r=n&&n.match(/^(\d+)(\.\d+)?px$/);return r?+r[1]:void 0}function eG(t,e,n,r){return{x:t.x+n*(e.x-t.x),y:t.y+n*(e.y-t.y)}}function e0(t,e,n,r){return{x:t.x+n*(e.x-t.x),y:"middle"===r?n<.5?t.y:e.y:"after"===r?n<1?t.y:e.y:n>0?e.y:t.y}}function e1(t,e,n,r){let o={x:t.cp2x,y:t.cp2y},i={x:e.cp1x,y:e.cp1y},a=eG(t,o,n),l=eG(o,i,n),s=eG(i,e,n),c=eG(a,l,n),u=eG(l,s,n);return eG(c,u,n)}function e5(t,e,n){var r;return t?(r=n,{x:t=>e+e+r-t,setWidth(t){r=t},textAlign:t=>"center"===t?t:"right"===t?"left":"right",xPlus:(t,e)=>t-e,leftForLtr:(t,e)=>t-e}):{x:t=>t,setWidth(t){},textAlign:t=>t,xPlus:(t,e)=>t+e,leftForLtr:(t,e)=>t}}function e2(t,e){let n,r;("ltr"===e||"rtl"===e)&&(r=[(n=t.canvas.style).getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",e,"important"),t.prevTextDirection=r)}function e8(t,e){void 0!==e&&(delete t.prevTextDirection,t.canvas.style.setProperty("direction",e[0],e[1]))}function e3(t){return"angle"===t?{between:tj,compare:tO,normalize:t_}:{between:tS,compare:(t,e)=>t-e,normalize:t=>t}}function e4({start:t,end:e,count:n,loop:r,style:o}){return{start:t%n,end:e%n,loop:r&&(e-t+1)%n==0,style:o}}function e6(t,e,n){let r,o,i;if(!n)return[t];let{property:a,start:l,end:s}=n,c=e.length,{compare:u,between:f,normalize:d}=e3(a),{start:h,end:p,loop:b,style:g}=function(t,e,n){let r,{property:o,start:i,end:a}=n,{between:l,normalize:s}=e3(o),c=e.length,{start:u,end:f,loop:d}=t;if(d){for(u+=c,f+=c,r=0;r<c&&l(s(e[u%c][o]),i,a);++r)u--,f--;u%=c,f%=c}return f<u&&(f+=c),{start:u,end:f,loop:d,style:t.style}}(t,e,n),m=[],y=!1,v=null,x=()=>y||f(l,i,r)&&0!==u(l,i),w=()=>!y||0===u(s,r)||f(s,i,r);for(let t=h,n=h;t<=p;++t)(o=e[t%c]).skip||(r=d(o[a]))!==i&&(y=f(r,l,s),null===v&&x()&&(v=0===u(r,l)?t:n),null!==v&&w()&&(m.push(e4({start:v,end:t,loop:b,count:c,style:g})),v=null),n=t,i=r);return null!==v&&m.push(e4({start:v,end:p,loop:b,count:c,style:g})),m}function e7(t,e){let n=[],r=t.segments;for(let o=0;o<r.length;o++){let i=e6(r[o],t.points,e);i.length&&n.push(...i)}return n}function e9(t,e){let n=t.points,r=t.options.spanGaps,o=n.length;if(!o)return[];let i=!!t._loop,{start:a,end:l}=function(t,e,n,r){let o=0,i=e-1;if(n&&!r)for(;o<e&&!t[o].skip;)o++;for(;o<e&&t[o].skip;)o++;for(o%=e,n&&(i+=o);i>o&&t[i%e].skip;)i--;return{start:o,end:i%=e}}(n,o,i,r);if(!0===r)return nt(t,[{start:a,end:l,loop:i}],n,e);let s=l<a?l+o:l,c=!!t._fullLoop&&0===a&&l===o-1;return nt(t,function(t,e,n,r){let o,i=t.length,a=[],l=e,s=t[e];for(o=e+1;o<=n;++o){let n=t[o%i];n.skip||n.stop?s.skip||(r=!1,a.push({start:e%i,end:(o-1)%i,loop:r}),e=l=n.stop?o:null):(l=o,s.skip&&(e=o)),s=n}return null!==l&&a.push({start:e%i,end:l%i,loop:r}),a}(n,a,s,c),n,e)}function nt(t,e,n,r){return r&&r.setContext&&n?function(t,e,n,r){let o=t._chart.getContext(),i=ne(t.options),{_datasetIndex:a,options:{spanGaps:l}}=t,s=n.length,c=[],u=i,f=e[0].start,d=f;function h(t,e,r,o){let i=l?-1:1;if(t!==e){for(t+=s;n[t%s].skip;)t-=i;for(;n[e%s].skip;)e+=i;t%s!=e%s&&(c.push({start:t%s,end:e%s,loop:r,style:o}),u=o,f=e%s)}}for(let t of e){let e,i=n[(f=l?f:t.start)%s];for(d=f+1;d<=t.end;d++){let l=n[d%s];(function(t,e){if(!e)return!1;let n=[],r=function(t,e){return tK(e)?(n.includes(e)||n.push(e),n.indexOf(e)):e};return JSON.stringify(t,r)!==JSON.stringify(e,r)})(e=ne(r.setContext(eO(o,{type:"segment",p0:i,p1:l,p0DataIndex:(d-1)%s,p1DataIndex:d%s,datasetIndex:a}))),u)&&h(f,d-1,t.loop,u),i=l,u=e}f<d-1&&h(f,d-1,t.loop,u)}return c}(t,e,n,r):e}function ne(t){return{backgroundColor:t.backgroundColor,borderCapStyle:t.borderCapStyle,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderJoinStyle:t.borderJoinStyle,borderWidth:t.borderWidth,borderColor:t.borderColor}}function nn(t,e,n){return t.options.clip?t[n]:e[n]}function nr(t,e){let n=e._clip;if(n.disabled)return!1;let r=function(t,e){let{xScale:n,yScale:r}=t;return n&&r?{left:nn(n,e,"left"),right:nn(n,e,"right"),top:nn(r,e,"top"),bottom:nn(r,e,"bottom")}:e}(e,t.chartArea);return{left:!1===n.left?0:r.left-(!0===n.left?0:n.left),right:!1===n.right?t.width:r.right+(!0===n.right?0:n.right),top:!1===n.top?0:r.top-(!0===n.top?0:n.top),bottom:!1===n.bottom?t.height:r.bottom+(!0===n.bottom?0:n.bottom)}}},35695:(t,e,n)=>{"use strict";var r=n(18999);n.o(r,"usePathname")&&n.d(e,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(e,{useRouter:function(){return r.useRouter}})},49537:()=>{},60760:(t,e,n)=>{"use strict";n.d(e,{N:()=>y});var r=n(95155),o=n(12115),i=n(90869),a=n(82885),l=n(97494),s=n(80845),c=n(27351),u=n(51508);class f extends o.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,n=(0,c.s)(t)&&t.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(t){let{children:e,isPresent:n,anchorX:i,root:a}=t,l=(0,o.useId)(),s=(0,o.useRef)(null),c=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,o.useContext)(u.Q);return(0,o.useInsertionEffect)(()=>{let{width:t,height:e,top:r,left:o,right:u}=c.current;if(n||!s.current||!t||!e)return;s.current.dataset.motionPopId=l;let f=document.createElement("style");d&&(f.nonce=d);let h=null!=a?a:document.head;return h.appendChild(f),f.sheet&&f.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===i?"left: ".concat(o):"right: ".concat(u),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{h.removeChild(f),h.contains(f)&&h.removeChild(f)}},[n]),(0,r.jsx)(f,{isPresent:n,childRef:s,sizeRef:c,children:o.cloneElement(e,{ref:s})})}let h=t=>{let{children:e,initial:n,isPresent:i,onExitComplete:l,custom:c,presenceAffectsLayout:u,mode:f,anchorX:h,root:b}=t,g=(0,a.M)(p),m=(0,o.useId)(),y=!0,v=(0,o.useMemo)(()=>(y=!1,{id:m,initial:n,isPresent:i,custom:c,onExitComplete:t=>{for(let e of(g.set(t,!0),g.values()))if(!e)return;l&&l()},register:t=>(g.set(t,!1),()=>g.delete(t))}),[i,g,l]);return u&&y&&(v={...v}),(0,o.useMemo)(()=>{g.forEach((t,e)=>g.set(e,!1))},[i]),o.useEffect(()=>{i||g.size||!l||l()},[i]),"popLayout"===f&&(e=(0,r.jsx)(d,{isPresent:i,anchorX:h,root:b,children:e})),(0,r.jsx)(s.t.Provider,{value:v,children:e})};function p(){return new Map}var b=n(32082);let g=t=>t.key||"";function m(t){let e=[];return o.Children.forEach(t,t=>{(0,o.isValidElement)(t)&&e.push(t)}),e}let y=t=>{let{children:e,custom:n,initial:s=!0,onExitComplete:c,presenceAffectsLayout:u=!0,mode:f="sync",propagate:d=!1,anchorX:p="left",root:y}=t,[v,x]=(0,b.xQ)(d),w=(0,o.useMemo)(()=>m(e),[e]),k=d&&!v?[]:w.map(g),M=(0,o.useRef)(!0),O=(0,o.useRef)(w),_=(0,a.M)(()=>new Map),[j,T]=(0,o.useState)(w),[P,S]=(0,o.useState)(w);(0,l.E)(()=>{M.current=!1,O.current=w;for(let t=0;t<P.length;t++){let e=g(P[t]);k.includes(e)?_.delete(e):!0!==_.get(e)&&_.set(e,!1)}},[P,k.length,k.join("-")]);let E=[];if(w!==j){let t=[...w];for(let e=0;e<P.length;e++){let n=P[e],r=g(n);k.includes(r)||(t.splice(e,0,n),E.push(n))}return"wait"===f&&E.length&&(t=E),S(m(t)),T(w),null}let{forceRender:R}=(0,o.useContext)(i.L);return(0,r.jsx)(r.Fragment,{children:P.map(t=>{let e=g(t),o=(!d||!!v)&&(w===P||k.includes(e));return(0,r.jsx)(h,{isPresent:o,initial:(!M.current||!!s)&&void 0,custom:n,presenceAffectsLayout:u,mode:f,root:y,onExitComplete:o?void 0:()=>{if(!_.has(e))return;_.set(e,!0);let t=!0;_.forEach(e=>{e||(t=!1)}),t&&(null==R||R(),S(O.current),d&&(null==x||x()),c&&c())},anchorX:p,children:t},e)})})}},64065:(t,e,n)=>{"use strict";n.d(e,{N1:()=>u,nu:()=>d,yP:()=>f});var r=n(12115),o=n(32502);let i="label";function a(t,e){"function"==typeof t?t(e):t&&(t.current=e)}function l(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:i,r=[];t.datasets=e.map(e=>{let o=t.datasets.find(t=>t[n]===e[n]);return!o||!e.data||r.includes(o)?{...e}:(r.push(o),Object.assign(o,e),o)})}let s=(0,r.forwardRef)(function(t,e){let{height:n=150,width:s=300,redraw:c=!1,datasetIdKey:u,type:f,data:d,options:h,plugins:p=[],fallbackContent:b,updateMode:g,...m}=t,y=(0,r.useRef)(null),v=(0,r.useRef)(null),x=()=>{y.current&&(v.current=new o.t1(y.current,{type:f,data:function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i,n={labels:[],datasets:[]};return n.labels=t.labels,l(n,t.datasets,e),n}(d,u),options:h&&{...h},plugins:p}),a(e,v.current))},w=()=>{a(e,null),v.current&&(v.current.destroy(),v.current=null)};return(0,r.useEffect)(()=>{!c&&v.current&&h&&function(t,e){let n=t.options;n&&e&&Object.assign(n,e)}(v.current,h)},[c,h]),(0,r.useEffect)(()=>{!c&&v.current&&(v.current.config.data.labels=d.labels)},[c,d.labels]),(0,r.useEffect)(()=>{!c&&v.current&&d.datasets&&l(v.current.config.data,d.datasets,u)},[c,d.datasets]),(0,r.useEffect)(()=>{v.current&&(c?(w(),setTimeout(x)):v.current.update(g))},[c,h,d.labels,d.datasets,g]),(0,r.useEffect)(()=>{v.current&&(w(),setTimeout(x))},[f]),(0,r.useEffect)(()=>(x(),()=>w()),[]),r.createElement("canvas",{ref:y,role:"img",height:n,width:s,...m},b)});function c(t,e){return o.t1.register(e),(0,r.forwardRef)((e,n)=>r.createElement(s,{...e,ref:n,type:t}))}let u=c("line",o.ZT),f=c("bar",o.A6),d=c("doughnut",o.ju)}}]);