"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7153],{8619:(e,t,n)=>{n.d(t,{d:()=>i});var r=n(60098),o=n(12115),s=n(51508),l=n(82885);function i(e){let t=(0,l.M)(()=>(0,r.OQ)(e)),{isStatic:n}=(0,o.useContext)(s.Q);if(n){let[,n]=(0,o.useState)(e);(0,o.useEffect)(()=>t.on("change",n),[])}return t}},37602:(e,t,n)=>{n.d(t,{z:()=>a});var r=n(64803),o=n(30532),s=n(69515);function l(e){return"number"==typeof e?e:parseFloat(e)}var i=n(12115),u=n(51508),c=n(8619),f=n(58829);function a(e,t={}){let{isStatic:n}=(0,i.useContext)(u.Q),p=()=>(0,r.S)(e)?e.get():e;if(n)return(0,f.G)(p);let d=(0,c.d)(p());return(0,i.useInsertionEffect)(()=>(function(e,t,n){let i,u,c=e.get(),f=null,a=c,p="string"==typeof c?c.replace(/[\d.-]/g,""):void 0,d=()=>{f&&(f.stop(),f=null)},h=()=>{d(),f=new o.s({keyframes:[l(e.get()),l(a)],velocity:e.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:i})};return e.attach((t,n)=>(a=t,i=e=>{var t,r;return n((t=e,(r=p)?t+r:t))},s.Gt.postRender(h),e.get()),d),(0,r.S)(t)&&(u=t.on("change",t=>{var n,r;return e.set((n=t,(r=p)?n+r:n))}),e.on("destroy",u)),u})(d,e,t),[d,JSON.stringify(t)]),d}},58829:(e,t,n)=>{n.d(t,{G:()=>f});var r=n(6775),o=n(82885),s=n(69515),l=n(97494),i=n(8619);function u(e,t){let n=(0,i.d)(t()),r=()=>n.set(t());return r(),(0,l.E)(()=>{let t=()=>s.Gt.preRender(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,s.WG)(r)}}),n}var c=n(60098);function f(e,t,n,o){if("function"==typeof e){c.bt.current=[],e();let t=u(c.bt.current,e);return c.bt.current=void 0,t}let s="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),n=t?0:-1,o=e[0+n],s=e[1+n],l=e[2+n],i=e[3+n],u=(0,r.G)(s,l,i);return t?u(o):u}(t,n,o);return Array.isArray(e)?a(e,s):a([e],([e])=>s(e))}function a(e,t){let n=(0,o.M)(()=>[]);return u(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}},60760:(e,t,n)=>{n.d(t,{N:()=>y});var r=n(95155),o=n(12115),s=n(90869),l=n(82885),i=n(97494),u=n(80845),c=n(27351),f=n(51508);class a extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:n,anchorX:s,root:l}=e,i=(0,o.useId)(),u=(0,o.useRef)(null),c=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=(0,o.useContext)(f.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:o,right:f}=c.current;if(n||!u.current||!e||!t)return;u.current.dataset.motionPopId=i;let a=document.createElement("style");p&&(a.nonce=p);let d=null!=l?l:document.head;return d.appendChild(a),a.sheet&&a.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===s?"left: ".concat(o):"right: ".concat(f),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{d.removeChild(a),d.contains(a)&&d.removeChild(a)}},[n]),(0,r.jsx)(a,{isPresent:n,childRef:u,sizeRef:c,children:o.cloneElement(t,{ref:u})})}let d=e=>{let{children:t,initial:n,isPresent:s,onExitComplete:i,custom:c,presenceAffectsLayout:f,mode:a,anchorX:d,root:g}=e,m=(0,l.M)(h),v=(0,o.useId)(),y=!0,x=(0,o.useMemo)(()=>(y=!1,{id:v,initial:n,isPresent:s,custom:c,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;i&&i()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[s,m,i]);return f&&y&&(x={...x}),(0,o.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[s]),o.useEffect(()=>{s||m.size||!i||i()},[s]),"popLayout"===a&&(t=(0,r.jsx)(p,{isPresent:s,anchorX:d,root:g,children:t})),(0,r.jsx)(u.t.Provider,{value:x,children:t})};function h(){return new Map}var g=n(32082);let m=e=>e.key||"";function v(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let y=e=>{let{children:t,custom:n,initial:u=!0,onExitComplete:c,presenceAffectsLayout:f=!0,mode:a="sync",propagate:p=!1,anchorX:h="left",root:y}=e,[x,E]=(0,g.xQ)(p),C=(0,o.useMemo)(()=>v(t),[t]),R=p&&!x?[]:C.map(m),w=(0,o.useRef)(!0),M=(0,o.useRef)(C),P=(0,l.M)(()=>new Map),[S,b]=(0,o.useState)(C),[j,k]=(0,o.useState)(C);(0,i.E)(()=>{w.current=!1,M.current=C;for(let e=0;e<j.length;e++){let t=m(j[e]);R.includes(t)?P.delete(t):!0!==P.get(t)&&P.set(t,!1)}},[j,R.length,R.join("-")]);let G=[];if(C!==S){let e=[...C];for(let t=0;t<j.length;t++){let n=j[t],r=m(n);R.includes(r)||(e.splice(t,0,n),G.push(n))}return"wait"===a&&G.length&&(e=G),k(v(e)),b(C),null}let{forceRender:A}=(0,o.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:j.map(e=>{let t=m(e),o=(!p||!!x)&&(C===j||R.includes(t));return(0,r.jsx)(d,{isPresent:o,initial:(!w.current||!!u)&&void 0,custom:n,presenceAffectsLayout:f,mode:a,root:y,onExitComplete:o?void 0:()=>{if(!P.has(t))return;P.set(t,!0);let e=!0;P.forEach(t=>{t||(e=!1)}),e&&(null==A||A(),k(M.current),p&&(null==E||E()),c&&c())},anchorX:h,children:e},t)})})}}}]);