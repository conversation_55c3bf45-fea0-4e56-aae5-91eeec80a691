"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4193],{6654:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let a=s(12115);function l(e,t){let s=(0,a.useRef)(null),l=(0,a.useRef)(null);return(0,a.useCallback)(a=>{if(null===a){let e=s.current;e&&(s.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(s.current=n(e,a)),t&&(l.current=n(t,a))},[e,t])}function n(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let s=e(t);return"function"==typeof s?s:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9590:(e,t,s)=>{s.r(t),s.d(t,{HeaderLogo:()=>c,NavigationLogo:()=>o,SplashLogo:()=>u,default:()=>i});var a=s(95155),l=s(68289),n=s(66766);let r={xs:{width:24,height:24,className:"w-6 h-6"},sm:{width:32,height:32,className:"w-8 h-8"},md:{width:48,height:48,className:"w-12 h-12"},lg:{width:64,height:64,className:"w-16 h-16"},xl:{width:80,height:80,className:"w-20 h-20"},splash:{width:128,height:128,className:"w-32 h-32"}};function i(e){let{size:t="md",className:s="",animate:i=!1}=e,u=r[t],c=()=>(0,a.jsx)(n.default,{src:"/logo.svg",alt:"Better Interest",width:u.width,height:u.height,className:"".concat(u.className," object-contain ").concat(s),priority:!0});return i?(0,a.jsx)(l.P.div,{whileHover:{scale:1.05},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},className:"flex items-center justify-center",children:(0,a.jsx)(c,{})}):(0,a.jsx)(c,{})}function u(e){let{className:t=""}=e;return(0,a.jsx)(l.P.div,{animate:{scale:[1,1.05,1],rotateY:[0,3,-3,0],filter:["drop-shadow(0 0 30px rgba(255,255,255,0.4))","drop-shadow(0 0 40px rgba(255,255,255,0.6))","drop-shadow(0 0 30px rgba(255,255,255,0.4))"]},transition:{duration:4,repeat:1/0,ease:"easeInOut"},className:"flex items-center justify-center ".concat(t),children:(0,a.jsx)(i,{size:"splash"})})}function c(e){let{className:t=""}=e;return(0,a.jsx)(i,{size:"lg",animate:!0,className:t})}function o(e){let{className:t=""}=e;return(0,a.jsx)(i,{size:"sm",animate:!0,className:t})}}}]);