"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7223],{27223:(e,t,i)=>{i.r(t),i.d(t,{default:()=>c});var n=i(95155),a=i(60760),r=i(68289),s=i(12115);function l(e){let{isVisible:t,variant:i="spinner",size:a="md",color:s="green",text:l}=e;if(!t)return null;let c={sm:"w-6 h-6",md:"w-8 h-8",lg:"w-12 h-12"},o={green:"text-green-500",white:"text-white",gray:"text-gray-500"};return(0,n.jsxs)(r.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"flex flex-col items-center justify-center space-y-3",children:[(()=>{switch(i){case"dots":return(0,n.jsx)("div",{className:"flex space-x-1",children:[0,1,2].map(e=>(0,n.jsx)(r.P.div,{animate:{scale:[1,1.2,1],opacity:[.5,1,.5]},transition:{duration:.8,repeat:1/0,delay:.2*e},className:"w-2 h-2 rounded-full bg-current ".concat(o[s])},e))});case"pulse":return(0,n.jsx)(r.P.div,{animate:{scale:[1,1.1,1],opacity:[.7,1,.7]},transition:{duration:1.5,repeat:1/0,ease:"easeInOut"},className:"".concat(c[a]," rounded-full bg-current ").concat(o[s])});case"wave":return(0,n.jsx)("div",{className:"flex items-end space-x-1",children:[0,1,2,3,4].map(e=>(0,n.jsx)(r.P.div,{animate:{scaleY:[1,2,1]},transition:{duration:1,repeat:1/0,delay:.1*e},className:"w-1 h-4 bg-current ".concat(o[s]," rounded-full")},e))});default:return(0,n.jsx)(r.P.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"".concat(c[a]," border-2 border-current border-t-transparent rounded-full ").concat(o[s])})}})(),l&&(0,n.jsx)(r.P.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"text-sm font-medium ".concat(o[s]),children:l})]})}function c(e){let{children:t,isLoading:i=!1,direction:c="slide-right",duration:o=.3,className:u=""}=e,[d,p]=(0,s.useState)(!i);(0,s.useEffect)(()=>{if(i)p(!1);else{let e=setTimeout(()=>p(!0),100);return()=>clearTimeout(e)}},[i]);let f=(()=>{switch(c){case"slide-left":return{initial:{x:-100,opacity:0},animate:{x:0,opacity:1},exit:{x:100,opacity:0}};case"slide-up":return{initial:{y:100,opacity:0},animate:{y:0,opacity:1},exit:{y:-100,opacity:0}};case"slide-down":return{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},exit:{y:100,opacity:0}};case"fade":return{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}};case"scale":return{initial:{scale:.9,opacity:0},animate:{scale:1,opacity:1},exit:{scale:1.1,opacity:0}};default:return{initial:{x:100,opacity:0},animate:{x:0,opacity:1},exit:{x:-100,opacity:0}}}})();return(0,n.jsx)("div",{className:"relative min-h-screen ".concat(u),children:(0,n.jsx)(a.N,{mode:"wait",children:i?(0,n.jsx)(r.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},className:"absolute inset-0 flex items-center justify-center bg-white z-50",children:(0,n.jsx)(l,{isVisible:!0,variant:"dots",color:"green",text:"Loading..."})},"loader"):d?(0,n.jsx)(r.P.div,{initial:f.initial,animate:f.animate,exit:f.exit,transition:{duration:o,ease:"easeInOut"},className:"w-full",children:t},"content"):null})})}},60760:(e,t,i)=>{i.d(t,{N:()=>g});var n=i(95155),a=i(12115),r=i(90869),s=i(82885),l=i(97494),c=i(80845),o=i(27351),u=i(51508);class d extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,i=(0,o.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:i,anchorX:r,root:s}=e,l=(0,a.useId)(),c=(0,a.useRef)(null),o=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=(0,a.useContext)(u.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:a,right:u}=o.current;if(i||!c.current||!e||!t)return;c.current.dataset.motionPopId=l;let d=document.createElement("style");p&&(d.nonce=p);let f=null!=s?s:document.head;return f.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===r?"left: ".concat(a):"right: ".concat(u),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{f.removeChild(d),f.contains(d)&&f.removeChild(d)}},[i]),(0,n.jsx)(d,{isPresent:i,childRef:c,sizeRef:o,children:a.cloneElement(t,{ref:c})})}let f=e=>{let{children:t,initial:i,isPresent:r,onExitComplete:l,custom:o,presenceAffectsLayout:u,mode:d,anchorX:f,root:m}=e,x=(0,s.M)(h),y=(0,a.useId)(),g=!0,v=(0,a.useMemo)(()=>(g=!1,{id:y,initial:i,isPresent:r,custom:o,onExitComplete:e=>{for(let t of(x.set(e,!0),x.values()))if(!t)return;l&&l()},register:e=>(x.set(e,!1),()=>x.delete(e))}),[r,x,l]);return u&&g&&(v={...v}),(0,a.useMemo)(()=>{x.forEach((e,t)=>x.set(t,!1))},[r]),a.useEffect(()=>{r||x.size||!l||l()},[r]),"popLayout"===d&&(t=(0,n.jsx)(p,{isPresent:r,anchorX:f,root:m,children:t})),(0,n.jsx)(c.t.Provider,{value:v,children:t})};function h(){return new Map}var m=i(32082);let x=e=>e.key||"";function y(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let g=e=>{let{children:t,custom:i,initial:c=!0,onExitComplete:o,presenceAffectsLayout:u=!0,mode:d="sync",propagate:p=!1,anchorX:h="left",root:g}=e,[v,w]=(0,m.xQ)(p),j=(0,a.useMemo)(()=>y(t),[t]),P=p&&!v?[]:j.map(x),N=(0,a.useRef)(!0),E=(0,a.useRef)(j),b=(0,s.M)(()=>new Map),[C,R]=(0,a.useState)(j),[M,I]=(0,a.useState)(j);(0,l.E)(()=>{N.current=!1,E.current=j;for(let e=0;e<M.length;e++){let t=x(M[e]);P.includes(t)?b.delete(t):!0!==b.get(t)&&b.set(t,!1)}},[M,P.length,P.join("-")]);let k=[];if(j!==C){let e=[...j];for(let t=0;t<M.length;t++){let i=M[t],n=x(i);P.includes(n)||(e.splice(t,0,i),k.push(i))}return"wait"===d&&k.length&&(e=k),I(y(e)),R(j),null}let{forceRender:L}=(0,a.useContext)(r.L);return(0,n.jsx)(n.Fragment,{children:M.map(e=>{let t=x(e),a=(!p||!!v)&&(j===M||P.includes(t));return(0,n.jsx)(f,{isPresent:a,initial:(!N.current||!!c)&&void 0,custom:i,presenceAffectsLayout:u,mode:d,root:g,onExitComplete:a?void 0:()=>{if(!b.has(t))return;b.set(t,!0);let e=!0;b.forEach(t=>{t||(e=!1)}),e&&(null==L||L(),I(E.current),p&&(null==w||w()),o&&o())},anchorX:h,children:e},t)})})}}}]);