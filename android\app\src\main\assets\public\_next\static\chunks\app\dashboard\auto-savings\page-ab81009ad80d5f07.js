(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5751],{13428:(e,t,a)=>{"use strict";a.d(t,{r:()=>i});var r=a(98030),s=a(35695),n=a(12115);function i(){let{redirect:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{user:t,isLoading:a,isAuthenticated:i}=(0,r.A)(),l=(0,s.useRouter)();return(0,n.useEffect)(()=>{!a&&i&&(null==t?void 0:t.kycStatus)!=="APPROVED"&&e&&l.push("/dashboard/kyc")},[t,a,i,e,l]),(null==t?void 0:t.kycStatus)==="APPROVED"}},19958:(e,t,a)=>{"use strict";a.d(t,{YO:()=>x,uk:()=>d});var r=a(95155),s=a(12115),n=a(8619),i=a(37602),l=a(58829),o=a(68289),c=a(57740);function d(e){let{children:t,className:a="",intensity:d="medium",glowEffect:x=!0,hoverScale:u=!0,borderGradient:m=!1,elevation:g=2,onClick:p}=e,{theme:h}=(0,c.DP)(),b=(0,c.Yx)(h),v=(0,s.useRef)(null),f=(0,n.d)(0),y=(0,n.d)(0),j=(0,i.z)(f),N=(0,i.z)(y),w=(0,l.G)(N,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),k=(0,l.G)(j,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),S=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a={1:"light"===h?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===h?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===h?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)",4:"light"===h?"0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)":"0 14px 28px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.8)",5:"light"===h?"0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)":"0 19px 38px rgba(0, 0, 0, 0.7), 0 15px 12px rgba(0, 0, 0, 0.9)"};return a[t?Math.min(e+2,5):e]||a[2]},A="\n    relative rounded-xl overflow-hidden transition-all duration-300 group\n    ".concat(b.bg.card,"\n    ").concat(b.border.primary,"\n    ").concat(m?"border-2 border-transparent bg-gradient-to-r from-green-400/20 to-blue-400/20 p-[1px]":"border","\n    ").concat(a,"\n  "),D=(0,r.jsxs)(o.P.div,{ref:v,className:A,style:{rotateY:k,rotateX:w,transformStyle:"preserve-3d",boxShadow:S(g)},onMouseMove:e=>{if(!v.current)return;let t=v.current.getBoundingClientRect(),a=t.width,r=t.height,s=(e.clientX-t.left)/a-.5,n=(e.clientY-t.top)/r-.5;f.set(s),y.set(n)},onMouseLeave:()=>{f.set(0),y.set(0)},whileHover:u?{scale:1.02,boxShadow:S(g,!0),transition:{duration:.2,ease:"easeOut"}}:{},transition:{type:"spring",stiffness:300,damping:30},onClick:p,children:[x&&(0,r.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl blur-xl",initial:{scale:.8},whileHover:{scale:1.1},transition:{duration:.3}}),(0,r.jsx)(o.P.div,{className:"absolute inset-0 bg-green-400/10 opacity-0 group-hover:opacity-20 rounded-xl",initial:{scale:0},whileHover:{scale:1},transition:{duration:.4,ease:"easeOut"}}),(0,r.jsx)("div",{className:"relative z-10 ".concat(m?"".concat(b.bg.card," rounded-xl"):""),children:t}),(0,r.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ".concat("dark"===h?"via-white/5":"via-white/10"),initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.6,ease:"easeInOut"}})]});return(0,r.jsx)("div",{className:"group",children:D})}function x(e){let{title:t,value:a,subtitle:s,icon:n,color:i="green",className:l=""}=e,{theme:o}=(0,c.DP)(),x=(0,c.Yx)(o);return(0,r.jsx)(d,{className:"p-6 ".concat(l),glowEffect:!0,borderGradient:!0,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium ".concat(x.text.secondary),children:t}),(0,r.jsx)("p",{className:"text-2xl font-bold ".concat(x.text.primary," mt-1"),children:a}),s&&(0,r.jsx)("p",{className:"text-xs ".concat(x.text.tertiary," mt-1"),children:s})]}),n&&(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat({green:"from-green-400 to-green-600",blue:"from-blue-400 to-blue-600",purple:"from-purple-400 to-purple-600",yellow:"from-yellow-400 to-yellow-600"}[i]," flex items-center justify-center"),children:(0,r.jsx)(n,{className:"w-6 h-6 text-white"})})]})})}},31898:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m});var r=a(95155),s=a(12115),n=a(10351),i=a(58111),l=a(11846),o=a(87101),c=a(13741),d=a(19958),x=a(13428);let u=[{id:"1",name:"Daily Coffee Fund",description:"Save ₦500 every day instead of buying coffee",amount:500,frequency:"daily",trigger:"fixed",isActive:!0,totalSaved:45e3,nextExecution:new Date(Date.now()+864e5),createdDate:new Date(Date.now()-7776e6)},{id:"2",name:"Round-up Savings",description:"Round up purchases to nearest ₦100 and save the difference",amount:0,frequency:"daily",trigger:"roundup",isActive:!0,totalSaved:12750,nextExecution:new Date(Date.now()+432e5),createdDate:new Date(Date.now()-5184e6)},{id:"3",name:"Weekly Emergency Fund",description:"Save 10% of weekly income automatically",amount:15e3,frequency:"weekly",trigger:"percentage",isActive:!0,totalSaved:18e4,nextExecution:new Date(Date.now()+2592e5),createdDate:new Date(Date.now()-10368e6)},{id:"4",name:"Monthly Investment",description:"Automatically invest ₦50,000 every month",amount:5e4,frequency:"monthly",trigger:"fixed",isActive:!1,totalSaved:2e5,nextExecution:new Date(Date.now()+1296e6),createdDate:new Date(Date.now()-1296e7)}];function m(){let[e,t]=(0,s.useState)("active"),a=(0,x.r)({redirect:!1}),m=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN",minimumFractionDigits:0}).format(e),g=e=>{console.log("Toggle rule ".concat(e))},p=u.filter(t=>"active"===e?t.isActive:"paused"!==e||!t.isActive),h=u.reduce((e,t)=>e+t.totalSaved,0),b=u.filter(e=>e.isActive).length,v=u.length;return(0,r.jsx)(l.A,{title:"Auto Savings",children:(0,r.jsxs)("div",{className:"space-y-8",children:[!a&&(0,r.jsx)(o.I,{}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-theme font-inter",children:"Auto Savings"}),(0,r.jsx)("p",{className:"text-theme-secondary mt-2 font-inter",children:"Set up automatic savings rules to build wealth effortlessly"})]}),(0,r.jsx)(c.$n,{leftIcon:n.GGD,className:"font-inter",children:"Create Rule"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsx)(i.hI,{title:"Total Auto Saved",value:m(h),subtitle:"All time",icon:n.FrA,color:"green",trend:{value:25.3,isPositive:!0}}),(0,r.jsx)(i.hI,{title:"Active Rules",value:"".concat(b,"/").concat(v),subtitle:"Currently running",icon:n.aze,color:"blue"}),(0,r.jsx)(i.hI,{title:"Monthly Average",value:m(h/4),subtitle:"Auto savings",icon:n.ARf,color:"purple",trend:{value:18.7,isPositive:!0}}),(0,r.jsx)(i.hI,{title:"Next Execution",value:"2 hours",subtitle:"Round-up savings",icon:n.Ohp,color:"yellow"})]}),(0,r.jsx)("div",{className:"flex space-x-1 bg-theme-secondary p-1 rounded-lg w-fit",children:[{key:"active",label:"Active Rules"},{key:"paused",label:"Paused Rules"},{key:"all",label:"All Rules"}].map(a=>(0,r.jsx)("button",{onClick:()=>t(a.key),className:"px-4 py-2 rounded-md text-sm font-medium font-inter transition-all duration-200 ".concat(e===a.key?"bg-brand text-white shadow-md":"text-theme-secondary hover:text-theme"),children:a.label},a.key))}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:p.map(e=>{let t=(e=>{switch(e){case"fixed":return n.wIk;case"roundup":return n.jEl;case"percentage":return n.ARf;default:return n.FrA}})(e.trigger);return(0,r.jsx)(d.uk,{elevation:2,children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 rounded-lg bg-brand/10",children:(0,r.jsx)(t,{className:"w-5 h-5 text-brand"})}),(0,r.jsx)("div",{children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter",children:e.name})})]}),(0,r.jsx)("button",{onClick:()=>g(e.id),className:"p-1 rounded-lg hover:bg-theme-secondary transition-colors",children:e.isActive?(0,r.jsx)(n.Q04,{className:"w-6 h-6 text-brand"}):(0,r.jsx)(n.Hzw,{className:"w-6 h-6 text-theme-secondary"})})]}),(0,r.jsx)("p",{className:"text-sm text-theme-secondary font-inter mb-4",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-theme font-inter",children:"roundup"===e.trigger?"Variable":m(e.amount)}),(0,r.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat((e=>{switch(e){case"daily":return"text-green-600 bg-green-600/10";case"weekly":return"text-blue-600 bg-blue-600/10";case"monthly":return"text-purple-600 bg-purple-600/10";default:return"text-gray-600 bg-gray-600/10"}})(e.frequency)),children:e.frequency})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:"Total Saved"}),(0,r.jsx)("p",{className:"text-lg font-semibold text-brand",children:m(e.totalSaved)})]})]}),(0,r.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.Ohp,{className:"w-4 h-4 text-brand"}),(0,r.jsx)("span",{className:"text-sm text-theme-secondary font-inter",children:"Next Execution"})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-theme",children:e.isActive?(e=>{let t=new Date,a=Math.floor((e.getTime()-t.getTime())/36e5),r=Math.floor(a/24);return r>0?"".concat(r," day").concat(r>1?"s":""):a>0?"".concat(a," hour").concat(a>1?"s":""):"Soon"})(e.nextExecution):"Paused"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.wIk,{className:"w-4 h-4 text-brand"}),(0,r.jsx)("span",{className:"text-sm text-theme-secondary font-inter",children:"Created"})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-theme",children:e.createdDate.toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(n.jEl,{className:"w-4 h-4 text-brand"}),(0,r.jsx)("span",{className:"text-sm text-theme-secondary font-inter",children:"Trigger Type"})]}),(0,r.jsx)("span",{className:"text-sm font-medium text-theme capitalize",children:e.trigger})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(c.$n,{fullWidth:!0,variant:e.isActive?"outline":"primary",size:"sm",leftIcon:e.isActive?n.GHw:n.aze,onClick:()=>g(e.id),className:"font-inter",children:e.isActive?"Pause":"Resume"}),(0,r.jsx)(c.$n,{variant:"ghost",size:"sm",leftIcon:n.VSk,className:"font-inter",children:"Edit"})]})]})},e.id)})}),0===p.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)(n.FrA,{className:"w-16 h-16 text-theme-secondary mx-auto mb-4"}),(0,r.jsxs)("h3",{className:"text-lg font-semibold text-theme font-inter mb-2",children:["No ",e," rules found"]}),(0,r.jsx)("p",{className:"text-theme-secondary font-inter mb-6",children:"active"===e?"Create your first auto-savings rule to start building wealth automatically":"paused"===e?"No paused rules. All your auto-savings are currently active!":"Set up automatic savings rules to make saving effortless"}),"paused"!==e&&(0,r.jsx)(c.$n,{leftIcon:n.GGD,className:"font-inter",children:"Create Auto-Savings Rule"})]}),"active"===e&&p.length>0&&(0,r.jsx)(d.uk,{elevation:1,children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-theme font-inter mb-4",children:"Quick Setup Suggestions"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"p-4 rounded-lg bg-theme-secondary",children:[(0,r.jsx)(n.jEl,{className:"w-8 h-8 text-brand mb-2"}),(0,r.jsx)("h4",{className:"font-medium text-theme font-inter mb-1",children:"Round-up Savings"}),(0,r.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:"Save spare change from every purchase"})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg bg-theme-secondary",children:[(0,r.jsx)(n.wIk,{className:"w-8 h-8 text-brand mb-2"}),(0,r.jsx)("h4",{className:"font-medium text-theme font-inter mb-1",children:"Fixed Amount"}),(0,r.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:"Save a fixed amount regularly"})]}),(0,r.jsxs)("div",{className:"p-4 rounded-lg bg-theme-secondary",children:[(0,r.jsx)(n.ARf,{className:"w-8 h-8 text-brand mb-2"}),(0,r.jsx)("h4",{className:"font-medium text-theme font-inter mb-1",children:"Percentage Based"}),(0,r.jsx)("p",{className:"text-sm text-theme-secondary font-inter",children:"Save a percentage of your income"})]})]})]})})]})})}},58111:(e,t,a)=>{"use strict";a.d(t,{Lz:()=>d,gz:()=>x,hI:()=>c});var r=a(95155),s=a(12115),n=a(68289),i=a(57740);let l={green:{bg:"bg-green-500/20",text:"text-green-400",border:"border-green-500/30",icon:"text-green-400"},blue:{bg:"bg-blue-500/20",text:"text-blue-400",border:"border-blue-500/30",icon:"text-blue-400"},red:{bg:"bg-red-500/20",text:"text-red-400",border:"border-red-500/30",icon:"text-red-400"},yellow:{bg:"bg-yellow-500/20",text:"text-yellow-400",border:"border-yellow-500/30",icon:"text-yellow-400"},purple:{bg:"bg-purple-500/20",text:"text-purple-400",border:"border-purple-500/30",icon:"text-purple-400"}};function o(e){var t,a,o,c;let{title:d,value:x,subtitle:u,icon:m,trend:g,color:p="green",onClick:h,className:b=""}=e,v=l[p]||l.green,{theme:f}=(0,i.DP)(),y=(0,i.Yx)(f),j=(0,s.useRef)(null),N=e=>{let t={1:"light"===f?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===f?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===f?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)"};return t[e]||t[1]};return(0,r.jsxs)(n.P.div,{ref:j,initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-4,boxShadow:N(3),transition:{duration:.2,ease:"easeOut"}},className:"\n        relative overflow-hidden rounded-xl p-6 transition-all duration-300 group\n        ".concat(h?"cursor-pointer":"","\n        ").concat("light"===f?"bg-white border border-gray-200 hover:border-gray-300":"bg-gray-900/80 border border-gray-700 hover:border-gray-600","\n        ").concat(b,"\n      "),style:{boxShadow:N(1)},onClick:h,children:[h&&(0,r.jsx)(n.P.div,{className:"absolute inset-0 bg-green-400 opacity-0 group-hover:opacity-5 transition-opacity duration-300",initial:{scale:0,opacity:0},whileHover:{scale:1,opacity:.05},transition:{duration:.3}}),(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==v||null==(t=v.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent opacity-60")}),(0,r.jsxs)("div",{className:"flex items-start justify-between relative z-10",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium mb-3 ".concat(y.text.secondary),children:d}),(0,r.jsx)("p",{className:"text-3xl font-bold mb-2 ".concat(y.text.primary),style:{fontFamily:"Inter, system-ui, sans-serif"},children:x}),u&&(0,r.jsx)("p",{className:"text-sm ".concat(y.text.tertiary," mb-2"),children:u}),g&&(0,r.jsxs)(n.P.div,{className:"flex items-center mt-3",initial:{opacity:0,x:-10},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,r.jsxs)("span",{className:"text-sm font-semibold px-2 py-1 rounded-full ".concat(g.isPositive?"text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30":"text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30"),children:[g.isPositive?"↗":"↘"," ",g.isPositive?"+":"",g.value,"%"]}),(0,r.jsx)("span",{className:"text-xs ml-2 ".concat(y.text.tertiary),children:"vs last month"})]})]}),m&&(0,r.jsx)(n.P.div,{className:"p-4 rounded-xl ".concat((null==v?void 0:v.bg)||"bg-brand/20"," ").concat((null==v?void 0:v.border)||"border-brand"," border-2 shadow-lg"),whileHover:{scale:1.05,rotate:5},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==v||null==(a=v.bg)?void 0:a.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==v||null==(o=v.bg)?void 0:o.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==v||null==(c=v.bg)?void 0:c.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,r.jsx)(m,{className:"w-7 h-7 ".concat((null==v?void 0:v.icon)||"text-brand")})})]})]})}function c(e){let{title:t,value:a,subtitle:s,icon:n,color:i="green",trend:l}=e;return(0,r.jsx)(o,{title:t,value:a,subtitle:s,icon:n,color:i,trend:l})}function d(e){var t,a,s,o;let{title:c,subtitle:d,icon:x,color:u="blue",onClick:m}=e,g=l[u]||l.blue,{theme:p}=(0,i.DP)(),h=(0,i.Yx)(p);return(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},whileHover:{y:-6,scale:1.02,boxShadow:"light"===p?"0 8px 25px rgba(0, 0, 0, 0.15), 0 16px 40px rgba(0, 0, 0, 0.1)":"0 8px 25px rgba(0, 0, 0, 0.4), 0 16px 40px rgba(0, 0, 0, 0.3)",transition:{duration:.2,ease:"easeOut"}},whileTap:{scale:.98},className:"\n        relative overflow-hidden rounded-xl p-6 cursor-pointer transition-all duration-300 group\n        ".concat("light"===p?"bg-gradient-to-br from-white to-gray-50 border border-gray-200 hover:border-gray-300":"bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 hover:border-gray-600","\n      "),style:{boxShadow:"light"===p?"0 2px 8px rgba(0, 0, 0, 0.1), 0 4px 16px rgba(0, 0, 0, 0.05)":"0 2px 8px rgba(0, 0, 0, 0.3), 0 4px 16px rgba(0, 0, 0, 0.2)"},onClick:m,children:[(0,r.jsx)(n.P.div,{className:"absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300 ".concat((null==g?void 0:g.bg)||"bg-brand/10"),initial:{scale:0},whileHover:{scale:1},transition:{duration:.3}}),(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r ".concat((null==g||null==(t=g.bg)?void 0:t.replace("bg-","from-"))||"from-brand"," to-transparent")}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 relative z-10",children:[(0,r.jsx)(n.P.div,{className:"p-4 rounded-xl ".concat((null==g?void 0:g.bg)||"bg-brand/20"," shadow-lg"),whileHover:{scale:1.1,rotate:10},transition:{duration:.2},style:{boxShadow:"0 4px 12px ".concat((null==g||null==(a=g.bg)?void 0:a.includes("green"))?"rgba(34, 197, 94, 0.3)":(null==g||null==(s=g.bg)?void 0:s.includes("blue"))?"rgba(59, 130, 246, 0.3)":(null==g||null==(o=g.bg)?void 0:o.includes("purple"))?"rgba(147, 51, 234, 0.3)":"rgba(34, 197, 94, 0.3)")},children:(0,r.jsx)(x,{className:"w-6 h-6 ".concat((null==g?void 0:g.icon)||"text-brand")})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg ".concat(h.text.primary," mb-1"),children:c}),(0,r.jsx)("p",{className:"text-sm ".concat(h.text.secondary),children:d})]}),(0,r.jsx)(n.P.div,{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat((null==g?void 0:g.bg)||"bg-brand/20"," opacity-70"),whileHover:{scale:1.2,opacity:1},transition:{duration:.2},children:(0,r.jsx)("svg",{className:"w-4 h-4 ".concat((null==g?void 0:g.icon)||"text-brand"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]})]})}function x(e){let{title:t,current:a,target:s,unit:i="",color:o="green"}=e,c=Math.min(a/s*100,100),d=l[o]||l.green;return(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gray-900/50 border border-gray-800 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-white font-semibold mb-4",children:t}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,r.jsxs)("span",{className:(null==d?void 0:d.text)||"text-brand",children:[c.toFixed(1),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-800 rounded-full h-2",children:(0,r.jsx)(n.P.div,{initial:{width:0},animate:{width:"".concat(c,"%")},transition:{duration:1,ease:"easeOut"},className:"h-2 rounded-full bg-gradient-to-r from-".concat(o,"-400 to-").concat(o,"-600")})}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-gray-400",children:[a.toLocaleString(),i," / ",s.toLocaleString(),i]}),(0,r.jsxs)("span",{className:"text-gray-400",children:[(s-a).toLocaleString(),i," remaining"]})]})]})]})}},87101:(e,t,a)=>{"use strict";a.d(t,{I:()=>i});var r=a(95155);a(12115);var s=a(10351),n=a(35695);function i(e){let{className:t=""}=e,a=(0,n.useRouter)();return(0,r.jsxs)("div",{className:"bg-red-600/10 border border-red-600 rounded-lg p-6 mb-6 flex items-center space-x-3 ".concat(t),children:[(0,r.jsx)(s.p45,{className:"w-6 h-6 text-red-500"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-red-500 font-semibold",children:"Complete Your KYC Verification"}),(0,r.jsx)("p",{className:"text-red-300 text-sm",children:"Verify your identity to unlock higher savings limits and additional features."})]}),(0,r.jsx)("button",{onClick:()=>a.push("/dashboard/kyc"),className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",children:"Verify Now"})]})}},98585:(e,t,a)=>{Promise.resolve().then(a.bind(a,31898))}},e=>{e.O(0,[844,5236,6874,6766,4955,3289,1846,8441,5964,7358],()=>e(e.s=98585)),_N_E=e.O()}]);