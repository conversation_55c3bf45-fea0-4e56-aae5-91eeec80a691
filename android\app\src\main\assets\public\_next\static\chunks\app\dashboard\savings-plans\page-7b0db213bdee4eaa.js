(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5900],{13428:(e,t,a)=>{"use strict";a.d(t,{r:()=>o});var r=a(98030),n=a(35695),s=a(12115);function o(){let{redirect:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{user:t,isLoading:a,isAuthenticated:o}=(0,r.A)(),i=(0,n.useRouter)();return(0,s.useEffect)(()=>{!a&&o&&(null==t?void 0:t.kycStatus)!=="APPROVED"&&e&&i.push("/dashboard/kyc")},[t,a,o,e,i]),(null==t?void 0:t.kycStatus)==="APPROVED"}},19958:(e,t,a)=>{"use strict";a.d(t,{YO:()=>u,uk:()=>d});var r=a(95155),n=a(12115),s=a(8619),o=a(37602),i=a(58829),l=a(68289),c=a(57740);function d(e){let{children:t,className:a="",intensity:d="medium",glowEffect:u=!0,hoverScale:g=!0,borderGradient:h=!1,elevation:x=2,onClick:m}=e,{theme:p}=(0,c.DP)(),y=(0,c.Yx)(p),f=(0,n.useRef)(null),v=(0,s.d)(0),b=(0,s.d)(0),w=(0,o.z)(v),j=(0,o.z)(b),N=(0,i.G)(j,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),A=(0,i.G)(w,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),S=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],a={1:"light"===p?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===p?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===p?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)",4:"light"===p?"0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)":"0 14px 28px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.8)",5:"light"===p?"0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)":"0 19px 38px rgba(0, 0, 0, 0.7), 0 15px 12px rgba(0, 0, 0, 0.9)"};return a[t?Math.min(e+2,5):e]||a[2]},P="\n    relative rounded-xl overflow-hidden transition-all duration-300 group\n    ".concat(y.bg.card,"\n    ").concat(y.border.primary,"\n    ").concat(h?"border-2 border-transparent bg-gradient-to-r from-green-400/20 to-blue-400/20 p-[1px]":"border","\n    ").concat(a,"\n  "),k=(0,r.jsxs)(l.P.div,{ref:f,className:P,style:{rotateY:A,rotateX:N,transformStyle:"preserve-3d",boxShadow:S(x)},onMouseMove:e=>{if(!f.current)return;let t=f.current.getBoundingClientRect(),a=t.width,r=t.height,n=(e.clientX-t.left)/a-.5,s=(e.clientY-t.top)/r-.5;v.set(n),b.set(s)},onMouseLeave:()=>{v.set(0),b.set(0)},whileHover:g?{scale:1.02,boxShadow:S(x,!0),transition:{duration:.2,ease:"easeOut"}}:{},transition:{type:"spring",stiffness:300,damping:30},onClick:m,children:[u&&(0,r.jsx)(l.P.div,{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl blur-xl",initial:{scale:.8},whileHover:{scale:1.1},transition:{duration:.3}}),(0,r.jsx)(l.P.div,{className:"absolute inset-0 bg-green-400/10 opacity-0 group-hover:opacity-20 rounded-xl",initial:{scale:0},whileHover:{scale:1},transition:{duration:.4,ease:"easeOut"}}),(0,r.jsx)("div",{className:"relative z-10 ".concat(h?"".concat(y.bg.card," rounded-xl"):""),children:t}),(0,r.jsx)(l.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ".concat("dark"===p?"via-white/5":"via-white/10"),initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.6,ease:"easeInOut"}})]});return(0,r.jsx)("div",{className:"group",children:k})}function u(e){let{title:t,value:a,subtitle:n,icon:s,color:o="green",className:i=""}=e,{theme:l}=(0,c.DP)(),u=(0,c.Yx)(l);return(0,r.jsx)(d,{className:"p-6 ".concat(i),glowEffect:!0,borderGradient:!0,children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium ".concat(u.text.secondary),children:t}),(0,r.jsx)("p",{className:"text-2xl font-bold ".concat(u.text.primary," mt-1"),children:a}),n&&(0,r.jsx)("p",{className:"text-xs ".concat(u.text.tertiary," mt-1"),children:n})]}),s&&(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat({green:"from-green-400 to-green-600",blue:"from-blue-400 to-blue-600",purple:"from-purple-400 to-purple-600",yellow:"from-yellow-400 to-yellow-600"}[o]," flex items-center justify-center"),children:(0,r.jsx)(s,{className:"w-6 h-6 text-white"})})]})})}},30504:(e,t,a)=>{Promise.resolve().then(a.bind(a,40223))},40223:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>b});var r=a(95155),n=a(12115),s=a(11846),o=a(66440),i=a(14585),l=a(75399),c=a(19958),d=a(62039),u=a(68289),g=a(57740);function h(e){let{children:t,as:a="h1",variant:n="hero",intensity:s="medium",color:o="default",className:i="",animate:l=!0}=e,{theme:c}=(0,g.DP)(),h="\n    ".concat((()=>{switch(n){case"hero":return"text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold font-inter leading-tight";case"heading":return"text-2xl md:text-3xl lg:text-4xl font-bold font-inter leading-tight";case"subheading":return"text-xl md:text-2xl font-semibold font-inter leading-relaxed";case"body":return"text-base md:text-lg font-medium font-inter leading-relaxed";default:return"text-4xl font-bold font-inter"}})(),"\n    ").concat((()=>{if("gradient"===o)return"bg-gradient-to-r from-green-400 via-green-500 to-green-600 bg-clip-text text-transparent";switch(o){case"green":return"light"===c?"text-green-600":"text-green-400";case"white":return"text-white";default:return"light"===c?"text-gray-900":"text-white"}})(),"\n    ").concat((()=>{if("gradient"===o)return"";if("light"===c)switch(s){case"light":return"text-shadow-sm";case"medium":default:return"text-shadow-md";case"strong":return"text-shadow-3d-light"}switch(s){case"light":return"text-shadow-sm";case"medium":return"green"===o?"text-shadow-3d-green":"text-shadow-lg";case"strong":return"green"===o?"text-shadow-3d-green":"text-shadow-3d";default:return"text-shadow-lg"}})(),"\n    ").concat(i,"\n  ").trim(),x=l?{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6,ease:d.vT}}:{};return(0,r.jsx)(u.P.div,{...x,children:(0,r.jsx)(a,{className:h,children:t})})}function x(e){return(0,r.jsx)(h,{...e,variant:"hero"})}function m(e){return(0,r.jsx)(h,{...e,variant:"subheading"})}var p=a(10351),y=a(41277),f=a(13428),v=a(87101);let b=function(){let e=(0,f.r)({redirect:!1}),{theme:t}=(0,g.DP)(),a=(0,g.Yx)(t),[d,u]=(0,n.useState)([]),[h,b]=(0,n.useState)(!1),[w,j]=(0,n.useState)(!1),[N,A]=(0,n.useState)({name:"",planType:"INDIVIDUAL",targetAmount:"",contributionAmount:"",frequency:"MONTHLY",duration:"12"}),[S,P]=(0,n.useState)(null);(0,n.useEffect)(()=>{!async function(){j(!0);try{console.log("[SAVINGS PLANS PAGE] Fetching savings summary...");let e=await fetch("".concat("http://localhost:8080/api","/api/savings/summary"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("auth_token"))}}),t=null;e.ok?t=await e.json():console.error("[SAVINGS PLANS PAGE] Failed to fetch savings summary:",e.status,await e.text()),P(t),console.log("[SAVINGS PLANS PAGE] Fetching user savings plans...");let a=await y.T.getUserSavingsPlans();console.log("[SAVINGS PLANS PAGE] Fetched plans:",a);let r=a.map(e=>{var t,a,r,n,s,o,i,l;return{id:e.id||e._id||"",name:e.name||e.title||"",planType:e.planType||"INDIVIDUAL",targetAmount:null!=(t=e.targetAmount)?t:0,currentAmount:null!=(r=null!=(a=e.currentAmount)?a:e.savedAmount)?r:0,contributionAmount:null!=(s=null!=(n=e.contributionAmount)?n:e.depositAmount)?s:0,frequency:e.frequency||e.depositFrequency||"MONTHLY",status:e.status||"ACTIVE",startDate:e.startDate||e.createdAt||"",endDate:e.endDate||e.targetDate||"",interestRate:null!=(o=e.interestRate)?o:0,progress:e.targetAmount>0?Math.round((null!=(l=null!=(i=e.currentAmount)?i:e.savedAmount)?l:0)/e.targetAmount*100):0}});u(r)}catch(e){console.error("[SAVINGS PLANS PAGE] Failed to fetch data:",e)}finally{j(!1)}}()},[]);let k=async e=>{e.preventDefault(),j(!0);try{let e={name:N.name,planType:N.planType,targetAmount:parseFloat(N.targetAmount),contributionAmount:parseFloat(N.contributionAmount),frequency:N.frequency,duration:parseInt(N.duration),autoDebit:!1,startDate:new Date().toISOString().split("T")[0]};console.log("[SAVINGS PLANS PAGE] Creating savings plan with payload:",e);let t=await y.T.createSavingsPlan(e);console.log("[SAVINGS PLANS PAGE] Created plan:",t),u(e=>[...e,{...t,progress:t.targetAmount>0?Math.round(t.currentAmount/t.targetAmount*100):0}]),b(!1),A({name:"",planType:"INDIVIDUAL",targetAmount:"",contributionAmount:"",frequency:"MONTHLY",duration:"12"})}catch(e){console.error("[SAVINGS PLANS PAGE] Failed to create plan:",e)}finally{j(!1)}},E=e=>{let{name:t,value:a}=e.target;A(e=>({...e,[t]:a}))},T=[{key:"name",title:"Plan Name",sortable:!0},{key:"planType",title:"Type",render:e=>(0,r.jsx)(l.Wh,{status:e,variant:"TARGET"===e?"info":"GOAL"===e?"warning":"default"})},{key:"currentAmount",title:"Current Amount",render:e=>"₦".concat(e.toLocaleString())},{key:"targetAmount",title:"Target Amount",render:e=>"₦".concat(e.toLocaleString())},{key:"progress",title:"Progress",render:e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-16 bg-gray-700 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"".concat(e,"%")}})}),(0,r.jsxs)("span",{className:"text-sm",children:[e,"%"]})]})},{key:"status",title:"Status",render:e=>(0,r.jsx)(l.Wh,{status:e,variant:"ACTIVE"===e?"success":"PAUSED"===e?"warning":"COMPLETED"===e?"info":"danger"})},{key:"id",title:"Actions",render:(e,t)=>(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(l.rA,{onClick:()=>console.log("View",e),variant:"default",children:(0,r.jsx)(p.Vap,{className:"w-4 h-4"})}),(0,r.jsx)(l.rA,{onClick:()=>console.log("Edit",e),variant:"primary",children:(0,r.jsx)(p.SG1,{className:"w-4 h-4"})}),(0,r.jsx)(l.rA,{onClick:()=>console.log("ACTIVE"===t.status?"Pause":"Resume",e),variant:"default",children:"ACTIVE"===t.status?(0,r.jsx)(p.GHw,{className:"w-4 h-4"}):(0,r.jsx)(p.aze,{className:"w-4 h-4"})}),(0,r.jsx)(l.rA,{onClick:()=>console.log("Delete",e),variant:"danger",children:(0,r.jsx)(p.IXo,{className:"w-4 h-4"})})]})}],C=S&&"number"==typeof S.totalTargetAmount?S.totalTargetAmount:0,I=S&&"number"==typeof S.activePlans?S.activePlans:0,O=S&&"number"==typeof S.completedPlans?S.completedPlans:0,G=d.filter(e=>"MONTHLY"===e.frequency).reduce((e,t)=>e+(t.contributionAmount||0),0);return(0,r.jsx)(s.A,{title:"Individual Savings",children:(0,r.jsxs)("div",{className:"space-y-8",children:[!e&&(0,r.jsx)(v.I,{}),(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-2xl ".concat(a.bg.card," border ").concat(a.border.accent," p-8"),children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500/10 to-blue-500/10"}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)(x,{as:"h1",color:"gradient",intensity:"strong",className:"mb-4",children:"Savings Plans"}),(0,r.jsx)(m,{as:"p",className:"".concat(a.text.secondary," mb-6"),children:"Manage your individual and group savings plans with better interest rates."})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsx)(c.YO,{title:"Total Savings",value:"₦".concat(C.toLocaleString()),subtitle:"Across all plans",icon:p.z8N,color:"green"}),(0,r.jsx)(c.YO,{title:"Active Plans",value:I.toString(),subtitle:"".concat(O," completed"),icon:p.x_j,color:"blue"}),(0,r.jsx)(c.YO,{title:"Monthly Contribution",value:"₦".concat(G.toLocaleString()),subtitle:"Total scheduled",icon:p.ARf,color:"purple"})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Your Savings Plans"}),(0,r.jsxs)("button",{onClick:()=>b(!0),className:"flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors",children:[(0,r.jsx)(p.GGD,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Create Plan"})]})]}),(0,r.jsx)(l.Ay,{data:d,columns:T,searchable:!0,searchPlaceholder:"Search plans...",emptyMessage:"No savings plans found. Create your first plan to get started!"}),(0,r.jsx)(o.k5,{isOpen:h,onClose:()=>b(!1),title:"Create Savings Plan",onSubmit:k,submitText:"Create Plan",isLoading:w,children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(i.ZQ,{label:"Plan Name",name:"name",value:N.name,onChange:E,placeholder:"e.g., Emergency Fund",required:!0}),(0,r.jsx)(i.pp,{label:"Plan Type",name:"planType",value:N.planType,onChange:E,options:[{value:"INDIVIDUAL",label:"Individual Savings"},{value:"TARGET",label:"Target Savings"},{value:"GROUP",label:"Group Savings"}],required:!0}),(0,r.jsx)(i.ZQ,{label:"Target Amount",name:"targetAmount",type:"number",value:N.targetAmount,onChange:E,placeholder:"1000000",required:!0}),(0,r.jsx)(i.ZQ,{label:"Contribution Amount",name:"contributionAmount",type:"number",value:N.contributionAmount,onChange:E,placeholder:"50000",required:!0}),(0,r.jsx)(i.pp,{label:"Frequency",name:"frequency",value:N.frequency,onChange:E,options:[{value:"DAILY",label:"Daily"},{value:"WEEKLY",label:"Weekly"},{value:"MONTHLY",label:"Monthly"}],required:!0}),(0,r.jsx)(i.ZQ,{label:"Duration (months)",name:"duration",type:"number",value:N.duration,onChange:E,placeholder:"12",required:!0})]})})]})})}},41277:(e,t,a)=>{"use strict";a.d(t,{T:()=>s});let r="http://localhost:8080/api";class n{getAuthHeaders(){let e=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}}async getSavingsPlans(){let e=await fetch("".concat(r,"/savings"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to fetch savings plans");return e.json()}async getUserSavingsPlans(){let e=await fetch("".concat(r,"/api/savings/my"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to fetch user savings plans");return(await e.json()).plans||[]}async getSavingsPlan(e){let t=await fetch("".concat(r,"/savings/").concat(e),{headers:this.getAuthHeaders()});if(!t.ok)throw Error("Failed to fetch savings plan");return t.json()}async joinSavingsPlan(e){let t=await fetch("".concat(r,"/savings/join"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({planId:e})});if(!t.ok)throw Error((await t.json()).message||"Failed to join savings plan");return t.json()}async createSavingsPlan(e){let t=await fetch("".concat(r,"/savings/create"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to create savings plan");return t.json()}async updateSavingsPlan(e,t){let a=await fetch("".concat(r,"/savings/").concat(e),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!a.ok)throw Error((await a.json()).message||"Failed to update savings plan");return a.json()}async pauseSavingsPlan(e){if(!(await fetch("".concat(r,"/savings/plans/").concat(e,"/pause"),{method:"POST",headers:this.getAuthHeaders()})).ok)throw Error("Failed to pause savings plan")}async resumeSavingsPlan(e){if(!(await fetch("".concat(r,"/savings/plans/").concat(e,"/resume"),{method:"POST",headers:this.getAuthHeaders()})).ok)throw Error("Failed to resume savings plan")}async deleteSavingsPlan(e){if(!(await fetch("".concat(r,"/savings/").concat(e),{method:"DELETE",headers:this.getAuthHeaders()})).ok)throw Error("Failed to delete savings plan")}async getSavingsGoals(){let e=await fetch("".concat(r,"/savings/goals"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to fetch savings goals");return e.json()}async getSavingsGoal(e){let t=await fetch("".concat(r,"/savings/goals/").concat(e),{headers:this.getAuthHeaders()});if(!t.ok)throw Error("Failed to fetch savings goal");return t.json()}async createSavingsGoal(e){let t=await fetch("".concat(r,"/savings/goals"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to create savings goal");return t.json()}async updateSavingsGoal(e,t){let a=await fetch("".concat(r,"/savings/goals/").concat(e),{method:"PUT",headers:this.getAuthHeaders(),body:JSON.stringify(t)});if(!a.ok)throw Error((await a.json()).message||"Failed to update savings goal");return a.json()}async deleteSavingsGoal(e){if(!(await fetch("".concat(r,"/savings/goals/").concat(e),{method:"DELETE",headers:this.getAuthHeaders()})).ok)throw Error("Failed to delete savings goal")}async getSavingsTransactions(e,t){let a=new URLSearchParams;e&&a.append("planId",e),t&&a.append("goalId",t);let n=await fetch("".concat(r,"/savings/transactions?").concat(a),{headers:this.getAuthHeaders()});if(!n.ok)throw Error("Failed to fetch transactions");return n.json()}async makeContribution(e,t){let a=await fetch("".concat(r,"/savings/plans/").concat(e,"/contribute"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({amount:t})});if(!a.ok)throw Error((await a.json()).message||"Failed to make contribution");return a.json()}async makeGoalContribution(e,t){let a=await fetch("".concat(r,"/savings/goals/").concat(e,"/contribute"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({amount:t})});if(!a.ok)throw Error((await a.json()).message||"Failed to make goal contribution");return a.json()}async getSavingsStats(){let e=await fetch("".concat(r,"/savings/stats"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to fetch savings statistics");return e.json()}async calculateInterest(e,t,a,n){let s=await fetch("".concat(r,"/savings/calculate-interest"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify({principal:e,rate:t,time:a,frequency:n})});if(!s.ok)throw Error("Failed to calculate interest");return s.json()}calculateCompoundInterest(e,t,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:12;return e*Math.pow(1+t/100/r,a/12*r)}calculateMonthlyContribution(e,t,a){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=e-t;if(0===r)return n/a;let s=r/100/12;return n*s/(Math.pow(1+s,a)-1)}formatCurrency(e){return new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e)}calculateProgress(e,t){return Math.min(e/t*100,100)}}let s=new n},75399:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>d,Wh:()=>l,XI:()=>i,rA:()=>c});var r=a(95155),n=a(68289),s=a(12115),o=a(10351);function i(e){let{data:t,columns:a,loading:i=!1,searchable:l=!1,searchPlaceholder:c="Search...",onSearch:d,emptyMessage:u="No data available",className:g=""}=e,[h,x]=s.useState({key:null,direction:"asc"}),[m,p]=s.useState(""),y=s.useMemo(()=>h.key?[...t].sort((e,t)=>{let a=e[h.key],r=t[h.key];return a<r?"asc"===h.direction?-1:1:a>r?"asc"===h.direction?1:-1:0}):t,[t,h]);return i?(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg ".concat(g),children:[l&&(0,r.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:c,className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500",disabled:!0})]})}),(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Loading..."})]})]}):(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg overflow-hidden ".concat(g),children:[l&&(0,r.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:c,value:m,onChange:e=>{var t;p(t=e.target.value),null==d||d(t)},className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"})]})}),0===y.length?(0,r.jsx)("div",{className:"p-8 text-center",children:(0,r.jsx)("p",{className:"text-gray-400",children:u})}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-800/50",children:(0,r.jsx)("tr",{children:a.map(e=>(0,r.jsx)("th",{className:"\n                      px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\n                      ".concat(e.sortable?"cursor-pointer hover:text-white":"","\n                      ").concat(e.width?e.width:"","\n                    "),onClick:()=>{var t;let a;return e.sortable&&(t=e.key,a="asc",void(h.key===t&&"asc"===h.direction&&(a="desc"),x({key:t,direction:a})))},children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:e.title}),e.sortable&&(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(o.wAb,{className:"w-3 h-3 ".concat(h.key===e.key&&"asc"===h.direction?"text-green-400":"text-gray-500")}),(0,r.jsx)(o.fK4,{className:"w-3 h-3 -mt-1 ".concat(h.key===e.key&&"desc"===h.direction?"text-green-400":"text-gray-500")})]})]})},String(e.key)))})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-800",children:y.map((e,t)=>(0,r.jsx)(n.P.tr,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.05*t},className:"hover:bg-gray-800/30 transition-colors",children:a.map(t=>(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:t.render?t.render(e[t.key],e):String(e[t.key]||"-")},String(t.key)))},t))})]})})]})}function l(e){let{status:t,variant:a="default"}=e;return(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat({default:"bg-gray-500/20 text-gray-400",success:"bg-green-500/20 text-green-400",warning:"bg-yellow-500/20 text-yellow-400",danger:"bg-red-500/20 text-red-400",info:"bg-blue-500/20 text-blue-400"}[a]),children:t})}function c(e){let{onClick:t,children:a,variant:n="default",size:s="sm"}=e;return(0,r.jsx)("button",{onClick:t,className:"rounded transition-colors ".concat({default:"text-gray-400 hover:text-white",primary:"text-green-400 hover:text-green-300",danger:"text-red-400 hover:text-red-300"}[n]," ").concat({sm:"p-1",md:"p-2"}[s]),children:a})}let d=i},87101:(e,t,a)=>{"use strict";a.d(t,{I:()=>o});var r=a(95155);a(12115);var n=a(10351),s=a(35695);function o(e){let{className:t=""}=e,a=(0,s.useRouter)();return(0,r.jsxs)("div",{className:"bg-red-600/10 border border-red-600 rounded-lg p-6 mb-6 flex items-center space-x-3 ".concat(t),children:[(0,r.jsx)(n.p45,{className:"w-6 h-6 text-red-500"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-red-500 font-semibold",children:"Complete Your KYC Verification"}),(0,r.jsx)("p",{className:"text-red-300 text-sm",children:"Verify your identity to unlock higher savings limits and additional features."})]}),(0,r.jsx)("button",{onClick:()=>a.push("/dashboard/kyc"),className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",children:"Verify Now"})]})}}},e=>{e.O(0,[844,5236,6874,6766,2574,3289,1846,6560,8441,5964,7358],()=>e(e.s=30504)),_N_E=e.O()}]);