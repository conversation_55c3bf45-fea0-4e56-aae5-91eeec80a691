"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8940],{29925:(e,t,r)=>{r.d(t,{dK:()=>l});var a=r(95155);r(12115);var s=r(68289),n=r(10351);function l(e){let{currentPage:t,totalPages:r,onPageChange:l,showInfo:i=!1,totalItems:c,itemsPerPage:o,className:d="",size:x="md",maxVisiblePages:h=7}=e,m={sm:{button:"px-2 py-1 text-sm",spacing:"space-x-1"},md:{button:"px-3 py-2 text-sm",spacing:"space-x-2"},lg:{button:"px-4 py-3 text-base",spacing:"space-x-3"}}[x],g=(()=>{if(r<=h)return Array.from({length:r},(e,t)=>t+1);let e=Math.max(1,t-Math.floor(h/2)),a=Math.min(r,e+h-1);a-e+1<h&&(e=Math.max(1,a-h+1));let s=[];e>1&&(s.push(1),e>2&&s.push("..."));for(let t=e;t<=a;t++)s.push(t);return a<r&&(a<r-1&&s.push("..."),s.push(r)),s})(),u=e=>{e>=1&&e<=r&&e!==t&&l(e)},y=c&&o?{start:(t-1)*o+1,end:Math.min(t*o,c)}:null;return r<=1?i&&y?(0,a.jsxs)("div",{className:"text-sm text-gray-400 ".concat(d),children:["Showing ",y.start," to ",y.end," of ",c," results"]}):null:(0,a.jsxs)("div",{className:"flex items-center justify-between ".concat(d),children:[i&&y&&(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:["Showing ",y.start," to ",y.end," of ",c," results"]}),(0,a.jsxs)("nav",{className:"flex items-center ".concat(m.spacing),children:[(0,a.jsxs)(s.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>u(t-1),disabled:1===t,className:"\n            ".concat(m.button,"\n            flex items-center justify-center rounded-lg border border-gray-600 bg-gray-800 text-gray-300\n            hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\n            transition-colors duration-200\n          "),children:[(0,a.jsx)(n.irw,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"ml-1 hidden sm:inline",children:"Previous"})]}),(0,a.jsx)("div",{className:"flex items-center ".concat(m.spacing),children:g.map((e,r)=>{if("..."===e)return(0,a.jsx)("span",{className:"".concat(m.button," flex items-center justify-center text-gray-400"),children:(0,a.jsx)(n.$Ri,{className:"w-4 h-4"})},"ellipsis-".concat(r));let l=e===t;return(0,a.jsx)(s.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>u(e),className:"\n                  ".concat(m.button,"\n                  flex items-center justify-center rounded-lg border transition-colors duration-200\n                  ").concat(l?"border-green-500 bg-green-600 text-white":"border-gray-600 bg-gray-800 text-gray-300 hover:bg-gray-700 hover:text-white","\n                "),children:e},e)})}),(0,a.jsxs)(s.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>u(t+1),disabled:t===r,className:"\n            ".concat(m.button,"\n            flex items-center justify-center rounded-lg border border-gray-600 bg-gray-800 text-gray-300\n            hover:bg-gray-700 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed\n            transition-colors duration-200\n          "),children:[(0,a.jsx)("span",{className:"mr-1 hidden sm:inline",children:"Next"}),(0,a.jsx)(n.fOo,{className:"w-4 h-4"})]})]})]})}},66440:(e,t,r)=>{r.d(t,{Ay:()=>d,aF:()=>c,k5:()=>o});var a=r(95155),s=r(60760),n=r(68289);r(12115);var l=r(10351);let i={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"};function c(e){let{isOpen:t,onClose:r,title:c,children:o,size:d="md",showCloseButton:x=!0}=e;return(0,a.jsx)(s.N,{children:t&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,a.jsx)(n.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:r}),(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,a.jsxs)(n.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"\n                relative w-full ".concat(i[d]," \n                bg-gray-900 border border-gray-800 rounded-lg shadow-xl\n              "),onClick:e=>e.stopPropagation(),children:[(c||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800",children:[c&&(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:c}),x&&(0,a.jsx)("button",{onClick:r,className:"p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,a.jsx)(l.yGN,{className:"w-5 h-5 text-gray-400"})})]}),(0,a.jsx)("div",{className:"p-6",children:o})]})})]})})}function o(e){let{isOpen:t,onClose:r,title:s,children:n,onSubmit:l,submitText:i="Submit",isLoading:o=!1,size:d="md"}=e;return(0,a.jsx)(c,{isOpen:t,onClose:r,title:s,size:d,children:(0,a.jsxs)("form",{onSubmit:l,className:"space-y-6",children:[n,(0,a.jsxs)("div",{className:"flex space-x-3 justify-end pt-4 border-t border-gray-800",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 text-gray-300 hover:text-white transition-colors",disabled:o,children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",disabled:o,className:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2",children:[o&&(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,a.jsx)("span",{children:i})]})]})]})})}let d=c},75399:(e,t,r)=>{r.d(t,{Ay:()=>d,Wh:()=>c,XI:()=>i,rA:()=>o});var a=r(95155),s=r(68289),n=r(12115),l=r(10351);function i(e){let{data:t,columns:r,loading:i=!1,searchable:c=!1,searchPlaceholder:o="Search...",onSearch:d,emptyMessage:x="No data available",className:h=""}=e,[m,g]=n.useState({key:null,direction:"asc"}),[u,y]=n.useState(""),b=n.useMemo(()=>m.key?[...t].sort((e,t)=>{let r=e[m.key],a=t[m.key];return r<a?"asc"===m.direction?-1:1:r>a?"asc"===m.direction?1:-1:0}):t,[t,m]);return i?(0,a.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg ".concat(h),children:[c&&(0,a.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",placeholder:o,className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500",disabled:!0})]})}),(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Loading..."})]})]}):(0,a.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg overflow-hidden ".concat(h),children:[c&&(0,a.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",placeholder:o,value:u,onChange:e=>{var t;y(t=e.target.value),null==d||d(t)},className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"})]})}),0===b.length?(0,a.jsx)("div",{className:"p-8 text-center",children:(0,a.jsx)("p",{className:"text-gray-400",children:x})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-800/50",children:(0,a.jsx)("tr",{children:r.map(e=>(0,a.jsx)("th",{className:"\n                      px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\n                      ".concat(e.sortable?"cursor-pointer hover:text-white":"","\n                      ").concat(e.width?e.width:"","\n                    "),onClick:()=>{var t;let r;return e.sortable&&(t=e.key,r="asc",void(m.key===t&&"asc"===m.direction&&(r="desc"),g({key:t,direction:r})))},children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:e.title}),e.sortable&&(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(l.wAb,{className:"w-3 h-3 ".concat(m.key===e.key&&"asc"===m.direction?"text-green-400":"text-gray-500")}),(0,a.jsx)(l.fK4,{className:"w-3 h-3 -mt-1 ".concat(m.key===e.key&&"desc"===m.direction?"text-green-400":"text-gray-500")})]})]})},String(e.key)))})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-800",children:b.map((e,t)=>(0,a.jsx)(s.P.tr,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.05*t},className:"hover:bg-gray-800/30 transition-colors",children:r.map(t=>(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:t.render?t.render(e[t.key],e):String(e[t.key]||"-")},String(t.key)))},t))})]})})]})}function c(e){let{status:t,variant:r="default"}=e;return(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat({default:"bg-gray-500/20 text-gray-400",success:"bg-green-500/20 text-green-400",warning:"bg-yellow-500/20 text-yellow-400",danger:"bg-red-500/20 text-red-400",info:"bg-blue-500/20 text-blue-400"}[r]),children:t})}function o(e){let{onClick:t,children:r,variant:s="default",size:n="sm"}=e;return(0,a.jsx)("button",{onClick:t,className:"rounded transition-colors ".concat({default:"text-gray-400 hover:text-white",primary:"text-green-400 hover:text-green-300",danger:"text-red-400 hover:text-red-300"}[s]," ").concat({sm:"p-1",md:"p-2"}[n]),children:r})}let d=i}}]);