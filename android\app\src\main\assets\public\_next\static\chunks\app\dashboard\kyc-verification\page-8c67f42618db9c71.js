(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6225],{24439:(e,s,a)=>{Promise.resolve().then(a.bind(a,46720))},46720:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>N});var t=a(95155),l=a(68289),r=a(12115),i=a(10351),n=a(11846),c=a(52814),d=a(13741),o=a(17703),m=a(93915),x=a(66440),u=a(30353),h=a(64198),g=a(24630);let j=[{value:"NATIONAL_ID",label:"National ID Card",icon:"\uD83C\uDD94",required:!0},{value:"PASSPORT",label:"International Passport",icon:"\uD83D\uDCD8",required:!1},{value:"DRIVERS_LICENSE",label:"Driver's License",icon:"\uD83D\uDE97",required:!1},{value:"VOTERS_CARD",label:"Voter's Card",icon:"\uD83D\uDDF3️",required:!1},{value:"UTILITY_BILL",label:"Utility Bill",icon:"\uD83D\uDCC4",required:!0},{value:"BANK_STATEMENT",label:"Bank Statement",icon:"\uD83C\uDFE6",required:!1},{value:"SELFIE",label:"Selfie with ID",icon:"\uD83E\uDD33",required:!0},{value:"SIGNATURE",label:"Signature Sample",icon:"✍️",required:!0}],p=[{level:"BASIC",name:"Basic Verification",description:"Basic account verification with limited features",requirements:["NATIONAL_ID","SELFIE"],limits:{deposit:1e5,withdrawal:5e4,monthly:5e5},color:"bg-green-600"},{level:"INTERMEDIATE",name:"Intermediate Verification",description:"Enhanced verification with higher limits",requirements:["NATIONAL_ID","UTILITY_BILL","SELFIE","SIGNATURE"],limits:{deposit:1e6,withdrawal:5e5,monthly:5e6},color:"bg-blue-600"},{level:"ADVANCED",name:"Advanced Verification",description:"Full verification with unlimited access",requirements:["NATIONAL_ID","UTILITY_BILL","BANK_STATEMENT","SELFIE","SIGNATURE"],limits:{deposit:"Unlimited",withdrawal:"Unlimited",monthly:"Unlimited"},color:"bg-purple-600"}];function N(){var e,s;let[a,N]=(0,r.useState)(null),[b,v]=(0,r.useState)(!0),[f,y]=(0,r.useState)(1),[w,C]=(0,r.useState)("BASIC"),[E,I]=(0,r.useState)(!1),[S,P]=(0,r.useState)(""),[A,D]=(0,r.useState)(!1),[O,T]=(0,r.useState)({firstName:"",lastName:"",middleName:"",dateOfBirth:"",gender:"MALE",nationality:"Nigerian",stateOfOrigin:"",localGovernment:"",occupation:"",employerName:"",annualIncome:""}),[k,R]=(0,r.useState)({email:"",phoneNumber:"",alternatePhoneNumber:""}),[L,q]=(0,r.useState)({street:"",city:"",state:"",postalCode:"",country:"Nigeria",landmark:""}),[_,B]=(0,r.useState)([]);(0,r.useEffect)(()=>{Y()},[]);let Y=async()=>{try{v(!0);let e=await g.$p.getUserKYC();e&&(N(e),T(e.personalInfo),R(e.contactInfo),q(e.addressInfo),B(e.documents),C(e.level),e.personalInfo.firstName?e.addressInfo.street?0===e.documents.length?y(3):y(4):y(2):y(1))}catch(e){console.error("Failed to load KYC data:",e)}finally{v(!1)}},F=async()=>{try{if(!O.firstName||!O.lastName||!O.dateOfBirth)return void h.P0.error("Please fill in all required fields");(null==a?void 0:a.id)&&await g.$p.updateKYC(a.id,{personalInfo:O}),h.P0.success("Personal information saved"),y(2)}catch(e){h.P0.error(e.message||"Failed to save personal information")}},V=async()=>{try{if(!L.street||!L.city||!L.state)return void h.P0.error("Please fill in all required fields");(null==a?void 0:a.id)&&await g.$p.updateKYC(a.id,{addressInfo:L}),h.P0.success("Address information saved"),y(3)}catch(e){h.P0.error(e.message||"Failed to save address information")}},U=async e=>{try{if(D(!0),!(null==a?void 0:a.id))return void h.P0.error("Please complete personal information first");let s=await g.$p.uploadDocument({kycId:a.id,type:S,name:e.name,file:e,description:"".concat(S," document")});B([..._,s]),I(!1),h.P0.success("Document uploaded successfully")}catch(e){h.P0.error(e.message||"Failed to upload document")}finally{D(!1)}},G=async()=>{try{var e;let s=(null==(e=p.find(e=>e.level===w))?void 0:e.requirements)||[],t=_.map(e=>e.type),l=s.filter(e=>!t.includes(e));if(l.length>0)return void h.P0.error("Missing required documents: ".concat(l.join(", ")));if(!(null==a?void 0:a.id))return void h.P0.error("Please complete all previous steps first");await g.$p.submitKYC(a.id),h.P0.success("KYC submitted for review"),Y()}catch(e){h.P0.error(e.message||"Failed to submit KYC")}},M=e=>"Unlimited"===e?e:new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(Number(e)),K=e=>{switch(e){case"APPROVED":return"success";case"PENDING":return"warning";case"UNDER_REVIEW":return"info";case"REJECTED":return"error";default:return"default"}},$=e=>{let s=j.find(s=>s.value===e);return(null==s?void 0:s.icon)||"\uD83D\uDCC4"};return b?(0,t.jsx)(n.A,{title:"KYC Verification",children:(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})})}):(0,t.jsx)(n.A,{title:"KYC Verification",children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white",children:"KYC Verification"}),(0,t.jsx)("p",{className:"text-gray-400 mt-2",children:"Complete your identity verification to unlock all features"})]}),a&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(e=>{switch(e){case"APPROVED":return(0,t.jsx)(i.YrT,{className:"text-green-500"});case"PENDING":return(0,t.jsx)(i.Ohp,{className:"text-yellow-500"});case"UNDER_REVIEW":return(0,t.jsx)(i.jTZ,{className:"text-blue-500"});case"REJECTED":return(0,t.jsx)(i.yGN,{className:"text-red-500"});default:return(0,t.jsx)(i.Ohp,{className:"text-gray-500"})}})(a.status),(0,t.jsx)(c.E,{variant:K(a.status),children:a.status.replace("_"," ")})]})]}),a&&(0,t.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Current Status"}),(0,t.jsx)("p",{className:"text-gray-400",children:"APPROVED"===a.status?"Your ".concat(a.level," verification is approved"):"REJECTED"===a.status?"Your verification was rejected. Please review and resubmit.":"Your verification is being processed"})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)(c.E,{variant:K(a.status),className:"mb-2",children:[a.level," Level"]}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:a.submittedAt&&"Submitted: ".concat(new Date(a.submittedAt).toLocaleDateString())})]})]}),a.rejectionReason&&(0,t.jsx)("div",{className:"mt-4 p-4 bg-red-600/20 border border-red-600 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)(i.eHT,{className:"text-red-400 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-red-400",children:"Rejection Reason"}),(0,t.jsx)("p",{className:"text-sm text-gray-300 mt-1",children:a.rejectionReason})]})]})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Choose Verification Level"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:p.map(e=>(0,t.jsx)(l.P.div,{whileHover:{scale:1.02},className:"cursor-pointer",onClick:()=>C(e.level),children:(0,t.jsxs)(o.Z,{className:"p-6 border-2 transition-colors ".concat(w===e.level?"border-green-500 bg-green-600/10":"border-gray-700 bg-gray-800 hover:border-gray-600"),children:[(0,t.jsx)("div",{className:"w-12 h-12 ".concat(e.color," rounded-lg flex items-center justify-center mb-4"),children:(0,t.jsx)(i.pcC,{className:"text-white text-xl"})}),(0,t.jsx)("h4",{className:"font-semibold text-white mb-2",children:e.name}),(0,t.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:e.description}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Daily Deposit:"}),(0,t.jsx)("span",{className:"text-white",children:M(e.limits.deposit)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Daily Withdrawal:"}),(0,t.jsx)("span",{className:"text-white",children:M(e.limits.withdrawal)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Monthly Limit:"}),(0,t.jsx)("span",{className:"text-white",children:M(e.limits.monthly)})]})]}),(0,t.jsxs)("div",{className:"mt-4 pt-4 border-t border-gray-700",children:[(0,t.jsx)("p",{className:"text-xs text-gray-400 mb-2",children:"Required Documents:"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-1",children:e.requirements.map(e=>(0,t.jsxs)(c.E,{variant:"default",className:"text-xs",children:[$(e)," ",e.replace("_"," ")]},e))})]})]})},e.level))})]}),(0,t.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Verification Progress"}),(0,t.jsxs)("span",{className:"text-sm text-gray-400",children:["Step ",f," of 4"]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4 mb-6",children:[1,2,3,4].map(e=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat(e<=f?"bg-green-600 text-white":"bg-gray-700 text-gray-400"),children:e<f?(0,t.jsx)(i.YrT,{}):e}),e<4&&(0,t.jsx)("div",{className:"w-16 h-1 ".concat(e<f?"bg-green-600":"bg-gray-700")})]},e))}),(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-center text-sm",children:[(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-gray-400",children:"Personal Info"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-gray-400",children:"Address Info"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-gray-400",children:"Documents"})}),(0,t.jsx)("div",{children:(0,t.jsx)("p",{className:"text-gray-400",children:"Review & Submit"})})]})]}),1===f&&(0,t.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(i.JXP,{className:"mr-2"}),"Personal Information"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)(m.p,{label:"First Name",value:O.firstName,onChange:e=>T({...O,firstName:e.target.value}),required:!0}),(0,t.jsx)(m.p,{label:"Last Name",value:O.lastName,onChange:e=>T({...O,lastName:e.target.value}),required:!0}),(0,t.jsx)(m.p,{label:"Middle Name",value:O.middleName,onChange:e=>T({...O,middleName:e.target.value})}),(0,t.jsx)(m.p,{label:"Date of Birth",type:"date",value:O.dateOfBirth,onChange:e=>T({...O,dateOfBirth:e.target.value}),required:!0}),(0,t.jsx)(u.l,{label:"Gender",value:O.gender,onChange:e=>T({...O,gender:e.target.value}),options:[{value:"",label:"Select Gender"},{value:"MALE",label:"Male"},{value:"FEMALE",label:"Female"},{value:"OTHER",label:"Other"}],required:!0}),(0,t.jsx)(m.p,{label:"Nationality",value:O.nationality,onChange:e=>T({...O,nationality:e.target.value}),required:!0}),(0,t.jsx)(m.p,{label:"State of Origin",value:O.stateOfOrigin,onChange:e=>T({...O,stateOfOrigin:e.target.value})}),(0,t.jsx)(m.p,{label:"Local Government",value:O.localGovernment,onChange:e=>T({...O,localGovernment:e.target.value})}),(0,t.jsx)(m.p,{label:"Occupation",value:O.occupation,onChange:e=>T({...O,occupation:e.target.value})}),(0,t.jsx)(m.p,{label:"Employer Name",value:O.employerName,onChange:e=>T({...O,employerName:e.target.value})}),(0,t.jsx)(u.l,{label:"Annual Income",value:O.annualIncome,onChange:e=>T({...O,annualIncome:e.target.value}),options:[{value:"",label:"Select Income Range"},{value:"BELOW_500K",label:"Below ₦500,000"},{value:"500K_1M",label:"₦500,000 - ₦1,000,000"},{value:"1M_5M",label:"₦1,000,000 - ₦5,000,000"},{value:"5M_10M",label:"₦5,000,000 - ₦10,000,000"},{value:"ABOVE_10M",label:"Above ₦10,000,000"}]})]}),(0,t.jsx)("div",{className:"flex justify-end mt-6",children:(0,t.jsx)(d.$n,{onClick:F,className:"bg-green-600 hover:bg-green-700",children:"Continue to Address"})})]}),2===f&&(0,t.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(i.V5Y,{className:"mr-2"}),"Address Information"]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsx)("div",{className:"md:col-span-2",children:(0,t.jsx)(m.p,{label:"Street Address",value:L.street,onChange:e=>q({...L,street:e.target.value}),required:!0})}),(0,t.jsx)(m.p,{label:"City",value:L.city,onChange:e=>q({...L,city:e.target.value}),required:!0}),(0,t.jsx)(m.p,{label:"State",value:L.state,onChange:e=>q({...L,state:e.target.value}),required:!0}),(0,t.jsx)(m.p,{label:"Postal Code",value:L.postalCode,onChange:e=>q({...L,postalCode:e.target.value})}),(0,t.jsx)(m.p,{label:"Country",value:L.country,onChange:e=>q({...L,country:e.target.value}),required:!0}),(0,t.jsx)("div",{className:"md:col-span-2",children:(0,t.jsx)(m.p,{label:"Landmark (Optional)",value:L.landmark,onChange:e=>q({...L,landmark:e.target.value}),placeholder:"Nearest landmark or notable location"})})]}),(0,t.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,t.jsx)(d.$n,{variant:"outline",onClick:()=>y(1),children:"Back to Personal Info"}),(0,t.jsx)(d.$n,{onClick:V,className:"bg-green-600 hover:bg-green-700",children:"Continue to Documents"})]})]}),3===f&&(0,t.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(i.QuH,{className:"mr-2"}),"Document Upload"]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6",children:j.map(e=>{var s;let a=null==(s=p.find(e=>e.level===w))?void 0:s.requirements.includes(e.value),l=_.some(s=>s.type===e.value);return(0,t.jsxs)("div",{className:"p-4 border rounded-lg cursor-pointer transition-colors ".concat(l?"border-green-500 bg-green-600/10":a?"border-yellow-500 bg-yellow-600/10 hover:border-yellow-400":"border-gray-600 bg-gray-700 hover:border-gray-500"),onClick:()=>{P(e.value),I(!0)},children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),l&&(0,t.jsx)(i.YrT,{className:"text-green-500"}),a&&!l&&(0,t.jsx)(i.eHT,{className:"text-yellow-500"})]}),(0,t.jsx)("h4",{className:"font-medium text-white text-sm",children:e.label}),(0,t.jsxs)("p",{className:"text-xs text-gray-400 mt-1",children:[a?"Required":"Optional"," • ",l?"Uploaded":"Not uploaded"]})]},e.value)})}),_.length>0&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h4",{className:"font-medium text-white mb-3",children:"Uploaded Documents"}),(0,t.jsx)("div",{className:"space-y-2",children:_.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-700 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-lg",children:$(e.type)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-white font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-400",children:e.type.replace("_"," ")})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.E,{variant:K(e.status),children:e.status}),(0,t.jsx)(d.$n,{size:"sm",variant:"outline",children:(0,t.jsx)(i.Vap,{})})]})]},e.id))})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)(d.$n,{variant:"outline",onClick:()=>y(2),children:"Back to Address"}),(0,t.jsx)(d.$n,{onClick:()=>y(4),className:"bg-green-600 hover:bg-green-700",disabled:0===_.length,children:"Review & Submit"})]})]}),4===f&&(0,t.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-white mb-4 flex items-center",children:[(0,t.jsx)(i.YrT,{className:"mr-2"}),"Review & Submit"]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-white mb-3",children:"Personal Information"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Name:"}),(0,t.jsxs)("span",{className:"text-white",children:[O.firstName," ",O.lastName]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Date of Birth:"}),(0,t.jsx)("span",{className:"text-white",children:O.dateOfBirth})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Gender:"}),(0,t.jsx)("span",{className:"text-white",children:O.gender})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Nationality:"}),(0,t.jsx)("span",{className:"text-white",children:O.nationality})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-white mb-3",children:"Address Information"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Street:"}),(0,t.jsx)("span",{className:"text-white",children:L.street})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"City:"}),(0,t.jsx)("span",{className:"text-white",children:L.city})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"State:"}),(0,t.jsx)("span",{className:"text-white",children:L.state})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-400",children:"Country:"}),(0,t.jsx)("span",{className:"text-white",children:L.country})]})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-white mb-3",children:["Documents (",_.length,")"]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:_.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2 p-2 bg-gray-700 rounded",children:[(0,t.jsx)("span",{children:$(e.type)}),(0,t.jsx)("span",{className:"text-sm text-white",children:e.type.replace("_"," ")})]},e.id))})]}),(0,t.jsx)("div",{className:"p-4 bg-blue-600/20 border border-blue-600 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)(i.S8s,{className:"text-blue-400 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-400",children:"Important Notice"}),(0,t.jsx)("p",{className:"text-sm text-gray-300 mt-1",children:"By submitting this KYC application, you confirm that all information provided is accurate and complete. False information may result in account suspension."})]})]})})]}),(0,t.jsxs)("div",{className:"flex justify-between mt-6",children:[(0,t.jsx)(d.$n,{variant:"outline",onClick:()=>y(3),children:"Back to Documents"}),(0,t.jsxs)(d.$n,{onClick:G,className:"bg-green-600 hover:bg-green-700",children:[(0,t.jsx)(i.pcC,{className:"mr-2"}),"Submit for Review"]})]})]}),(0,t.jsx)(x.aF,{isOpen:E,onClose:()=>I(!1),title:"Upload Document",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("span",{className:"text-2xl",children:null==(e=j.find(e=>e.value===S))?void 0:e.icon})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-white",children:null==(s=j.find(e=>e.value===S))?void 0:s.label})]}),(0,t.jsxs)("div",{className:"border-2 border-dashed border-gray-600 rounded-lg p-8 text-center",children:[(0,t.jsx)(i.B88,{className:"mx-auto text-4xl text-gray-400 mb-4"}),(0,t.jsx)("p",{className:"text-gray-400 mb-4",children:"Drag and drop your file here, or click to browse"}),(0,t.jsx)("input",{type:"file",accept:"image/*,.pdf",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];a&&U(a)},className:"hidden",id:"document-upload"}),(0,t.jsxs)("label",{htmlFor:"document-upload",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg cursor-pointer hover:bg-green-700",children:[(0,t.jsx)(i.PoE,{className:"mr-2"}),"Choose File"]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-400",children:[(0,t.jsx)("p",{children:"• Supported formats: JPG, PNG, PDF"}),(0,t.jsx)("p",{children:"• Maximum file size: 5MB"}),(0,t.jsx)("p",{children:"• Ensure document is clear and readable"})]}),A&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mr-3"}),(0,t.jsx)("span",{className:"text-white",children:"Uploading document..."})]})]})})]})})}},52814:(e,s,a)=>{"use strict";a.d(s,{A:()=>r,E:()=>l});var t=a(95155);function l(e){let{children:s,variant:a="default",size:l="md",className:r=""}=e,i="\n    ".concat("\n    inline-flex items-center justify-center font-medium rounded-full\n    transition-all duration-200\n  "," \n    ").concat({default:"bg-gray-700 text-gray-300 border border-gray-600",success:"bg-green-500/20 text-green-400 border border-green-500/30",warning:"bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",error:"bg-red-500/20 text-red-400 border border-red-500/30",info:"bg-blue-500/20 text-blue-400 border border-blue-500/30"}[a]," \n    ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[l]," \n    ").concat(r,"\n  ");return(0,t.jsx)("span",{className:i,children:s})}a(12115);let r=l},66440:(e,s,a)=>{"use strict";a.d(s,{Ay:()=>o,aF:()=>c,k5:()=>d});var t=a(95155),l=a(60760),r=a(68289);a(12115);var i=a(10351);let n={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"};function c(e){let{isOpen:s,onClose:a,title:c,children:d,size:o="md",showCloseButton:m=!0}=e;return(0,t.jsx)(l.N,{children:s&&(0,t.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,t.jsx)(r.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:a}),(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,t.jsxs)(r.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"\n                relative w-full ".concat(n[o]," \n                bg-gray-900 border border-gray-800 rounded-lg shadow-xl\n              "),onClick:e=>e.stopPropagation(),children:[(c||m)&&(0,t.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800",children:[c&&(0,t.jsx)("h2",{className:"text-xl font-semibold text-white",children:c}),m&&(0,t.jsx)("button",{onClick:a,className:"p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,t.jsx)(i.yGN,{className:"w-5 h-5 text-gray-400"})})]}),(0,t.jsx)("div",{className:"p-6",children:d})]})})]})})}function d(e){let{isOpen:s,onClose:a,title:l,children:r,onSubmit:i,submitText:n="Submit",isLoading:d=!1,size:o="md"}=e;return(0,t.jsx)(c,{isOpen:s,onClose:a,title:l,size:o,children:(0,t.jsxs)("form",{onSubmit:i,className:"space-y-6",children:[r,(0,t.jsxs)("div",{className:"flex space-x-3 justify-end pt-4 border-t border-gray-800",children:[(0,t.jsx)("button",{type:"button",onClick:a,className:"px-4 py-2 text-gray-300 hover:text-white transition-colors",disabled:d,children:"Cancel"}),(0,t.jsxs)("button",{type:"submit",disabled:d,className:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2",children:[d&&(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,t.jsx)("span",{children:n})]})]})]})})}let o=c}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,1846,411,8441,5964,7358],()=>e(e.s=24439)),_N_E=e.O()}]);