(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1281],{13428:(e,t,s)=>{"use strict";s.d(t,{r:()=>n});var a=s(98030),r=s(35695),l=s(12115);function n(){let{redirect:e=!0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{user:t,isLoading:s,isAuthenticated:n}=(0,a.A)(),i=(0,r.useRouter)();return(0,l.useEffect)(()=>{!s&&n&&(null==t?void 0:t.kycStatus)!=="APPROVED"&&e&&i.push("/dashboard/kyc")},[t,s,n,e,i]),(null==t?void 0:t.kycStatus)==="APPROVED"}},31460:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var a=s(95155),r=s(12115),l=s(10351),n=s(11846),i=s(87101),d=s(52814),c=s(13741),o=s(17703),x=s(93915),m=s(66440),u=s(30353),h=s(75399),p=s(64198),y=s(13428),g=s(24630);let b={CARD:"\uD83D\uDCB3",BANK_TRANSFER:"\uD83C\uDFE6",USSD:"\uD83D\uDCF1",QR_CODE:"\uD83D\uDCF1",MOBILE_MONEY:"\uD83D\uDCF1"};function j(){let e=(0,y.r)({redirect:!1}),[t,s]=(0,r.useState)([]),[j,N]=(0,r.useState)(!0),[f,v]=(0,r.useState)(!1),[w,D]=(0,r.useState)(!1),[k,C]=(0,r.useState)({amount:0,paymentMethod:"CARD",currency:"NGN",description:""}),[A,S]=(0,r.useState)({status:void 0,paymentMethod:void 0,dateFrom:"",dateTo:"",page:1,limit:20}),[E,P]=(0,r.useState)({totalDeposits:0,totalAmount:0,successfulDeposits:0,pendingDeposits:0});(0,r.useEffect)(()=>{R(),M()},[A]);let R=async()=>{try{N(!0);let e=await g.Pd.getUserDeposits(A);s(e.deposits)}catch(e){p.oR.error("Failed to load deposits")}finally{N(!1)}},M=async()=>{try{let e=await g.Pd.getDepositStats();P(e)}catch(e){console.error("Failed to load deposit stats:",e)}},F=async()=>{try{if(!k.amount||k.amount<100)return void p.oR.error("Minimum deposit amount is ₦100");D(!0);let e=await g.Pd.initiateDeposit(k);e.paymentData.authorizationUrl&&window.open(e.paymentData.authorizationUrl,"_blank"),v(!1),C({amount:0,paymentMethod:"CARD",currency:"NGN",description:""}),p.oR.success("Deposit initiated successfully"),R()}catch(e){p.oR.error(e.message||"Failed to initiate deposit")}finally{D(!1)}},O=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e),T=[{key:"reference",title:"Reference",render:e=>(0,a.jsx)("span",{className:"font-mono text-sm text-gray-300",children:e})},{key:"amount",title:"Amount",render:e=>(0,a.jsx)("span",{className:"font-semibold text-white",children:O(e)})},{key:"paymentMethod",title:"Method",render:e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:b[e]||"\uD83D\uDCB3"}),(0,a.jsx)("span",{className:"text-gray-300",children:e.replace("_"," ")})]})},{key:"status",title:"Status",render:(e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(e=>{switch(e){case"COMPLETED":return(0,a.jsx)(l.YrT,{className:"text-green-500"});case"PENDING":return(0,a.jsx)(l.Ohp,{className:"text-yellow-500"});case"FAILED":return(0,a.jsx)(l.yGN,{className:"text-red-500"});default:return(0,a.jsx)(l.Ohp,{className:"text-gray-500"})}})(e),(0,a.jsx)(d.E,{variant:(e=>{switch(e){case"COMPLETED":return"success";case"PENDING":return"warning";case"FAILED":return"error";default:return"default"}})(e),children:e})]})},{key:"fees",title:"Fees",render:e=>(0,a.jsx)("span",{className:"text-gray-400",children:O(e||0)})},{key:"createdAt",title:"Date",render:e=>(0,a.jsx)("span",{className:"text-gray-400",children:new Date(e).toLocaleDateString("en-NG")})}];return(0,a.jsxs)(n.A,{title:"Deposits",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[!e&&(0,a.jsx)(i.I,{}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Deposits"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Add money to your account"})]}),(0,a.jsxs)(c.$n,{onClick:()=>v(!0),className:"bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(l.GGD,{className:"mr-2"}),"Make Deposit"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Deposits"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:E.totalDeposits})]}),(0,a.jsx)(l.z8N,{className:"text-green-500 text-2xl"})]})}),(0,a.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Amount"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:O(E.totalAmount)})]}),(0,a.jsx)(l.ARf,{className:"text-blue-500 text-2xl"})]})}),(0,a.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Successful"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:E.successfulDeposits})]}),(0,a.jsx)(l.YrT,{className:"text-green-500 text-2xl"})]})}),(0,a.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Pending"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:E.pendingDeposits})]}),(0,a.jsx)(l.Ohp,{className:"text-yellow-500 text-2xl"})]})})]}),(0,a.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsx)(u.l,{value:A.status||"",onChange:e=>S({...A,status:e.target.value||void 0}),options:[{value:"",label:"All Status"},{value:"PENDING",label:"Pending"},{value:"COMPLETED",label:"Completed"},{value:"FAILED",label:"Failed"},{value:"CANCELLED",label:"Cancelled"}]}),(0,a.jsx)(u.l,{value:A.paymentMethod||"",onChange:e=>S({...A,paymentMethod:e.target.value||void 0}),options:[{value:"",label:"All Methods"},{value:"CARD",label:"Card Payment"},{value:"BANK_TRANSFER",label:"Bank Transfer"},{value:"USSD",label:"USSD"},{value:"QR_CODE",label:"QR Code"}]}),(0,a.jsx)(x.p,{type:"date",value:A.dateFrom,onChange:e=>S({...A,dateFrom:e.target.value}),placeholder:"From Date"}),(0,a.jsx)(x.p,{type:"date",value:A.dateTo,onChange:e=>S({...A,dateTo:e.target.value}),placeholder:"To Date"})]})}),(0,a.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Recent Deposits"}),(0,a.jsxs)(c.$n,{variant:"outline",size:"sm",children:[(0,a.jsx)(l.a4x,{className:"mr-2"}),"Export"]})]})}),(0,a.jsx)(h.Ay,{data:t,columns:T,loading:j,emptyMessage:"No deposits found"})]})]}),(0,a.jsx)(m.aF,{isOpen:f,onClose:()=>v(!1),title:"Make a Deposit",size:"md",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(x.p,{label:"Amount",type:"number",value:k.amount,onChange:e=>C({...k,amount:Number(e.target.value)}),placeholder:"Enter amount",min:"100",required:!0}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"Minimum deposit: ₦100"})]}),(0,a.jsx)(u.l,{label:"Payment Method",value:k.paymentMethod,onChange:e=>C({...k,paymentMethod:e}),options:[{value:"CARD",label:"\uD83D\uDCB3 Card Payment"},{value:"BANK_TRANSFER",label:"\uD83C\uDFE6 Bank Transfer"},{value:"USSD",label:"\uD83D\uDCF1 USSD"},{value:"QR_CODE",label:"\uD83D\uDCF1 QR Code"}]}),(0,a.jsx)(x.p,{label:"Description (Optional)",value:k.description,onChange:e=>C({...k,description:e.target.value}),placeholder:"What is this deposit for?"}),k.amount>0&&(0,a.jsxs)("div",{className:"p-4 bg-gray-700 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-white mb-2",children:"Transaction Summary"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Amount:"}),(0,a.jsx)("span",{className:"text-white",children:O(k.amount)})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Fees:"}),(0,a.jsx)("span",{className:"text-white",children:O(g.Pd.calculateFees(k.amount,k.paymentMethod).fees)})]}),(0,a.jsxs)("div",{className:"flex justify-between border-t border-gray-600 pt-2",children:[(0,a.jsx)("span",{className:"text-white font-medium",children:"You will receive:"}),(0,a.jsx)("span",{className:"text-green-400 font-medium",children:O(g.Pd.calculateFees(k.amount,k.paymentMethod).netAmount)})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(c.$n,{variant:"outline",onClick:()=>v(!1),children:"Cancel"}),(0,a.jsx)(c.$n,{onClick:F,className:"bg-green-600 hover:bg-green-700",disabled:w||!k.amount||k.amount<100,children:w?(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.lZI,{className:"mr-2"}),"Proceed to Payment"]})})]})]})})]})}},52814:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,E:()=>r});var a=s(95155);function r(e){let{children:t,variant:s="default",size:r="md",className:l=""}=e,n="\n    ".concat("\n    inline-flex items-center justify-center font-medium rounded-full\n    transition-all duration-200\n  "," \n    ").concat({default:"bg-gray-700 text-gray-300 border border-gray-600",success:"bg-green-500/20 text-green-400 border border-green-500/30",warning:"bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",error:"bg-red-500/20 text-red-400 border border-red-500/30",info:"bg-blue-500/20 text-blue-400 border border-blue-500/30"}[s]," \n    ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[r]," \n    ").concat(l,"\n  ");return(0,a.jsx)("span",{className:n,children:t})}s(12115);let l=r},66440:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,aF:()=>d,k5:()=>c});var a=s(95155),r=s(60760),l=s(68289);s(12115);var n=s(10351);let i={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"};function d(e){let{isOpen:t,onClose:s,title:d,children:c,size:o="md",showCloseButton:x=!0}=e;return(0,a.jsx)(r.N,{children:t&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:s}),(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"\n                relative w-full ".concat(i[o]," \n                bg-gray-900 border border-gray-800 rounded-lg shadow-xl\n              "),onClick:e=>e.stopPropagation(),children:[(d||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800",children:[d&&(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:d}),x&&(0,a.jsx)("button",{onClick:s,className:"p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,a.jsx)(n.yGN,{className:"w-5 h-5 text-gray-400"})})]}),(0,a.jsx)("div",{className:"p-6",children:c})]})})]})})}function c(e){let{isOpen:t,onClose:s,title:r,children:l,onSubmit:n,submitText:i="Submit",isLoading:c=!1,size:o="md"}=e;return(0,a.jsx)(d,{isOpen:t,onClose:s,title:r,size:o,children:(0,a.jsxs)("form",{onSubmit:n,className:"space-y-6",children:[l,(0,a.jsxs)("div",{className:"flex space-x-3 justify-end pt-4 border-t border-gray-800",children:[(0,a.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-300 hover:text-white transition-colors",disabled:c,children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",disabled:c,className:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2",children:[c&&(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,a.jsx)("span",{children:i})]})]})]})})}let o=d},75399:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,Wh:()=>d,XI:()=>i,rA:()=>c});var a=s(95155),r=s(68289),l=s(12115),n=s(10351);function i(e){let{data:t,columns:s,loading:i=!1,searchable:d=!1,searchPlaceholder:c="Search...",onSearch:o,emptyMessage:x="No data available",className:m=""}=e,[u,h]=l.useState({key:null,direction:"asc"}),[p,y]=l.useState(""),g=l.useMemo(()=>u.key?[...t].sort((e,t)=>{let s=e[u.key],a=t[u.key];return s<a?"asc"===u.direction?-1:1:s>a?"asc"===u.direction?1:-1:0}):t,[t,u]);return i?(0,a.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg ".concat(m),children:[d&&(0,a.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",placeholder:c,className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500",disabled:!0})]})}),(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Loading..."})]})]}):(0,a.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg overflow-hidden ".concat(m),children:[d&&(0,a.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,a.jsx)("input",{type:"text",placeholder:c,value:p,onChange:e=>{var t;y(t=e.target.value),null==o||o(t)},className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"})]})}),0===g.length?(0,a.jsx)("div",{className:"p-8 text-center",children:(0,a.jsx)("p",{className:"text-gray-400",children:x})}):(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full",children:[(0,a.jsx)("thead",{className:"bg-gray-800/50",children:(0,a.jsx)("tr",{children:s.map(e=>(0,a.jsx)("th",{className:"\n                      px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\n                      ".concat(e.sortable?"cursor-pointer hover:text-white":"","\n                      ").concat(e.width?e.width:"","\n                    "),onClick:()=>{var t;let s;return e.sortable&&(t=e.key,s="asc",void(u.key===t&&"asc"===u.direction&&(s="desc"),h({key:t,direction:s})))},children:(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)("span",{children:e.title}),e.sortable&&(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)(n.wAb,{className:"w-3 h-3 ".concat(u.key===e.key&&"asc"===u.direction?"text-green-400":"text-gray-500")}),(0,a.jsx)(n.fK4,{className:"w-3 h-3 -mt-1 ".concat(u.key===e.key&&"desc"===u.direction?"text-green-400":"text-gray-500")})]})]})},String(e.key)))})}),(0,a.jsx)("tbody",{className:"divide-y divide-gray-800",children:g.map((e,t)=>(0,a.jsx)(r.P.tr,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.05*t},className:"hover:bg-gray-800/30 transition-colors",children:s.map(t=>(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:t.render?t.render(e[t.key],e):String(e[t.key]||"-")},String(t.key)))},t))})]})})]})}function d(e){let{status:t,variant:s="default"}=e;return(0,a.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat({default:"bg-gray-500/20 text-gray-400",success:"bg-green-500/20 text-green-400",warning:"bg-yellow-500/20 text-yellow-400",danger:"bg-red-500/20 text-red-400",info:"bg-blue-500/20 text-blue-400"}[s]),children:t})}function c(e){let{onClick:t,children:s,variant:r="default",size:l="sm"}=e;return(0,a.jsx)("button",{onClick:t,className:"rounded transition-colors ".concat({default:"text-gray-400 hover:text-white",primary:"text-green-400 hover:text-green-300",danger:"text-red-400 hover:text-red-300"}[r]," ").concat({sm:"p-1",md:"p-2"}[l]),children:s})}let o=i},84871:(e,t,s)=>{Promise.resolve().then(s.bind(s,31460))},87101:(e,t,s)=>{"use strict";s.d(t,{I:()=>n});var a=s(95155);s(12115);var r=s(10351),l=s(35695);function n(e){let{className:t=""}=e,s=(0,l.useRouter)();return(0,a.jsxs)("div",{className:"bg-red-600/10 border border-red-600 rounded-lg p-6 mb-6 flex items-center space-x-3 ".concat(t),children:[(0,a.jsx)(r.p45,{className:"w-6 h-6 text-red-500"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"text-red-500 font-semibold",children:"Complete Your KYC Verification"}),(0,a.jsx)("p",{className:"text-red-300 text-sm",children:"Verify your identity to unlock higher savings limits and additional features."})]}),(0,a.jsx)("button",{onClick:()=>s.push("/dashboard/kyc"),className:"px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors",children:"Verify Now"})]})}}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,1846,411,8441,5964,7358],()=>e(e.s=84871)),_N_E=e.O()}]);