(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4859],{9598:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(95155),a=t(6874),n=t.n(a),o=t(35695),i=t(12115),l=t(10351),d=t(31246),c=t(64198);let m=i.lazy(()=>t.e(2266).then(t.bind(t,62266))),u=i.lazy(()=>t.e(7223).then(t.bind(t,27223)));function x(){let e=(0,o.useRouter)(),[r,t]=(0,i.useState)({email:"",password:""}),[a,x]=(0,i.useState)(!1),[h,p]=(0,i.useState)(!1),[g,b]=(0,i.useState)(""),[y,f]=(0,i.useState)(!1);i.useEffect(()=>{f((()=>{let e=navigator.userAgent.toLowerCase();return[/android/i,/iphone/i,/ipad/i,/mobile/i].some(r=>r.test(e))||window.innerWidth<768})())},[]);let j=e=>{let{name:r,value:s}=e.target;t(e=>({...e,[r]:s})),g&&b("")},v=async t=>{t.preventDefault(),p(!0),b("");try{let t=await fetch("".concat("http://localhost:8080/api","/api/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r.email,password:r.password})}),s=await t.json();s.success?(localStorage.setItem("auth_token",s.data.token),localStorage.setItem("user_data",JSON.stringify({id:s.data.user.id,firstName:s.data.user.firstName,lastName:s.data.user.lastName,email:s.data.user.email,role:s.data.user.role,balance:s.data.user.balance})),c.P0.success("Login successful!"),"admin"===s.data.user.role?e.push("/admin/dashboard"):e.push("/dashboard")):b(s.message||"Invalid email or password")}catch(e){console.error("Login error:",e),b("Login failed. Please check your connection and try again.")}finally{p(!1)}};return y?(0,s.jsx)(i.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(u,{isLoading:h,children:(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black",children:[(0,s.jsxs)("div",{className:"relative z-10 pt-16 pb-4 px-6 flex items-center",children:[(0,s.jsx)(m,{href:"/",variant:"default",className:"mr-4"}),(0,s.jsx)("h1",{className:"text-white text-lg font-semibold",children:"Sign In"})]}),(0,s.jsxs)("div",{className:"px-6 py-4 mt-8",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-white mb-4",children:"Welcome Back"}),(0,s.jsx)("p",{className:"text-white/70 text-lg",children:"Sign in to continue to your account"})]}),g&&(0,s.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-6",children:(0,s.jsx)("p",{className:"text-red-400 text-sm",children:g})}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-3 text-white/90",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.pHD,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"email",name:"email",value:r.email,onChange:j,className:"w-full pl-12 pr-4 py-4 bg-gray-800/50 backdrop-blur-sm border border-gray-600/50 rounded-2xl focus:border-green-500/60 focus:outline-none transition-all text-white placeholder-gray-400 text-lg",placeholder:"<EMAIL>",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-3 text-white/90",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.F5$,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:a?"text":"password",name:"password",value:r.password,onChange:j,className:"w-full pl-12 pr-14 py-4 bg-gray-800/50 backdrop-blur-sm border border-gray-600/50 rounded-2xl focus:border-green-500/60 focus:outline-none transition-all text-white placeholder-gray-400 text-lg",placeholder:"••••••••",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!a),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors p-1",children:a?(0,s.jsx)(l._NO,{size:20}):(0,s.jsx)(l.Vap,{size:20})})]})]}),(0,s.jsx)("button",{type:"submit",disabled:h,className:"w-full py-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-2xl font-semibold text-lg hover:from-green-600 hover:to-green-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg",children:h?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign In"})]}),(0,s.jsx)("div",{className:"text-center mt-8",children:(0,s.jsxs)("p",{className:"text-white/80",children:["Don't have an account?"," ",(0,s.jsx)(n(),{href:"/auth/signup",className:"text-white font-semibold underline",children:"Sign up"})]})})]})]})})}):(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black text-white",children:[(0,s.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,s.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(34,197,94,0.1),transparent_50%)]"}),(0,s.jsx)("div",{className:"absolute top-1/4 left-1/4 w-64 h-64 bg-green-400/5 rounded-full blur-3xl"}),(0,s.jsx)("div",{className:"absolute bottom-1/4 right-1/4 w-48 h-48 bg-orange-400/5 rounded-full blur-3xl"})]}),(0,s.jsx)("div",{className:"relative z-10 flex items-center justify-center min-h-screen p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold mb-4 text-white",children:"Welcome Back"}),(0,s.jsx)("p",{className:"text-gray-400 text-lg mb-8",children:"Sign in to your savings account"}),g&&(0,s.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6 max-w-md mx-auto",children:(0,s.jsx)("p",{className:"text-red-400 text-sm",children:g})}),(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6 max-w-md mx-auto",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"Email"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.pHD,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:"email",name:"email",value:r.email,onChange:j,className:"w-full pl-10 pr-4 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none transition-colors text-white placeholder-gray-400",placeholder:"<EMAIL>",required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2 text-white",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.F5$,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,s.jsx)("input",{type:a?"text":"password",name:"password",value:r.password,onChange:j,className:"w-full pl-10 pr-12 py-3 bg-gray-900/50 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none transition-colors text-white placeholder-gray-400",placeholder:"••••••••",required:!0}),(0,s.jsx)("button",{type:"button",onClick:()=>x(!a),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors",children:a?(0,s.jsx)(l._NO,{}):(0,s.jsx)(l.Vap,{})})]})]}),(0,s.jsx)(d.jn,{type:"submit",disabled:h,className:"w-full",children:h?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign In"})]}),(0,s.jsx)("div",{className:"text-center mt-8",children:(0,s.jsxs)("p",{className:"text-gray-400",children:["Don't have an account?"," ",(0,s.jsx)(n(),{href:"/auth/signup",className:"text-green-400 hover:text-green-300 font-medium transition-colors",children:"Sign up"})]})})]})})]})}},31246:(e,r,t)=>{"use strict";t.d(r,{Ay:()=>d,jn:()=>i,rp:()=>l});var s=t(95155);t(12115);var a=t(68289);let n=()=>(0,s.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 74 74",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("circle",{cx:37,cy:37,r:"35.5",stroke:"currentColor",strokeWidth:3}),(0,s.jsx)("path",{d:"M25 35.5C24.1716 35.5 23.5 36.1716 23.5 37C23.5 37.8284 24.1716 38.5 25 38.5V35.5ZM49.0607 38.0607C49.6464 37.4749 49.6464 36.5251 49.0607 35.9393L39.5147 26.3934C38.9289 25.8076 37.9792 25.8076 37.3934 26.3934C36.8076 26.9792 36.8076 27.9289 37.3934 28.5147L45.8787 37L37.3934 45.4853C36.8076 46.0711 36.8076 47.0208 37.3934 47.6066C37.9792 48.1924 38.9289 48.1924 39.5147 47.6066L49.0607 38.0607ZM25 38.5L48 38.5V35.5L25 35.5V38.5Z",fill:"currentColor"})]});function o(e){let{children:r,onClick:t,href:o,variant:i="primary",size:l="md",disabled:d=!1,className:c="",type:m="button",icon:u}=e,x="\n    cursor-pointer font-bold font-sans transition-all duration-200 \n    border-2 border-transparent flex items-center justify-center\n    rounded-full relative overflow-hidden group\n    shadow-lg hover:shadow-xl active:shadow-inner\n    transform hover:-translate-y-1 active:translate-y-0\n    ".concat(d?"opacity-50 cursor-not-allowed":"","\n  "),h="\n    ".concat(x,"\n    ").concat({primary:"\n      bg-gradient-to-r from-green-400 to-green-600 \n      hover:from-green-500 hover:to-green-700\n      text-white border-green-500\n      active:border-green-600 active:shadow-inner\n    ",secondary:"\n      bg-gradient-to-r from-blue-400 to-blue-600 \n      hover:from-blue-500 hover:to-blue-700\n      text-white border-blue-500\n      active:border-blue-600 active:shadow-inner\n    ",outline:"\n      bg-transparent border-green-400 text-green-400\n      hover:bg-green-400 hover:text-black\n      active:border-green-500 active:shadow-inner\n    "}[i],"\n    ").concat({sm:"px-3 py-1.5 text-xs min-w-[80px]",md:"px-4 py-2 text-sm min-w-[100px]",lg:"px-6 py-3 text-base min-w-[120px]"}[l],"\n    ").concat(c,"\n  "),p=()=>(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-400/20 to-green-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full blur-xl"}),(0,s.jsxs)("span",{className:"relative z-10 flex items-center justify-center",children:[(0,s.jsx)("span",{className:"text",children:r}),(u||"primary"===i)&&(0,s.jsx)(a.P.div,{className:"ml-2 text-current",whileHover:{x:3},transition:{type:"spring",stiffness:400,damping:10},children:u||(0,s.jsx)(n,{})})]})]});return o?(0,s.jsx)(a.P.a,{href:o,className:h,whileHover:{scale:1.02,y:-1},whileTap:{scale:.96,y:1},transition:{type:"spring",stiffness:400,damping:10},children:(0,s.jsx)(p,{})}):(0,s.jsx)(a.P.button,{type:m,onClick:t,disabled:d,className:h,whileHover:{scale:d?1:1.02,y:d?0:-1},whileTap:{scale:d?1:.96,y:+!d},transition:{type:"spring",stiffness:400,damping:10},children:(0,s.jsx)(p,{})})}function i(e){return(0,s.jsx)(o,{...e,variant:"primary"})}function l(e){return(0,s.jsx)(o,{...e,variant:"outline"})}let d=o},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}})},35971:(e,r,t)=>{Promise.resolve().then(t.bind(t,9598))},64198:(e,r,t)=>{"use strict";t.d(r,{CustomToaster:()=>l,P0:()=>i,oR:()=>n.Ay});var s=t(95155),a=t(68289),n=t(13568),o=t(10351);let i={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,r)=>n.Ay.promise(e,r,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function l(){return(0,s.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,s.jsx)(n.bv,{toast:e,children:r=>{let{icon:t,message:i}=r;return(0,s.jsxs)(a.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:t}),(0,s.jsx)("div",{className:"flex-1",children:i}),"loading"!==e.type&&(0,s.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,s.jsx)(o.yGN,{className:"w-4 h-4"})})]})}})})}}},e=>{e.O(0,[844,5236,6874,3568,8441,5964,7358],()=>e(e.s=35971)),_N_E=e.O()}]);