(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4449],{1147:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},2767:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},4993:(e,t,r)=>{"use strict";var n=r(12115);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},6115:(e,t,r)=>{"use strict";var n=r(12115),a=r(49033),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=a.useSyncExternalStore,l=n.useRef,c=n.useEffect,s=n.useMemo,u=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,a){var f=l(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=o(e,(f=s(function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==a&&d.hasValue){var t=d.value;if(a(t,e))return l=t}return l=e}if(t=l,i(o,e))return t;var r=n(e);return void 0!==a&&a(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,s=void 0===r?null:r;return[function(){return e(t())},null===s?void 0:function(){return e(s())}]},[t,r,n,a]))[0],f[1]);return c(function(){d.hasValue=!0,d.value=p},[p]),u(p),p}},6706:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(81571);t.sumBy=function(e,t){let r;if(!e||!e.length)return 0;null!=t&&(t=n.iteratee(t));for(let n=0;n<e.length;n++){let a=t?t(e[n]):e[n];void 0!==a&&(void 0===r?r=a:r+=a)}return r}},7547:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(23676),a=r(72465),i=r(10656),o=r(81571);t.uniqBy=function(e,t=a.identity){return i.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},8287:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},8870:function(e,t,r){var n;!function(a){"use strict";var i,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,c="[DecimalError] ",s=c+"Invalid argument: ",u=c+"Exponent out of range: ",f=Math.floor,d=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,h=f(1286742750677284.5),y={};function v(e,t){var r,n,a,i,o,c,s,u,f=e.constructor,d=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?S(t,d):t;if(s=e.d,u=t.d,o=e.e,a=t.e,s=s.slice(),i=o-a){for(i<0?(n=s,i=-i,c=u.length):(n=u,a=o,c=s.length),i>(c=(o=Math.ceil(d/7))>c?o+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=s.length)-(i=u.length)<0&&(i=c,n=u,u=s,s=n),r=0;i;)r=(s[--i]=s[i]+u[i]+r)/1e7|0,s[i]%=1e7;for(r&&(s.unshift(r),++a),c=s.length;0==s[--c];)s.pop();return t.d=s,t.e=a,l?S(t,d):t}function m(e,t,r){if(e!==~~e||e<t||e>r)throw Error(s+e)}function g(e){var t,r,n,a=e.length-1,i="",o=e[0];if(a>0){for(i+=o,t=1;t<a;t++)(r=7-(n=e[t]+"").length)&&(i+=P(r)),i+=n;(r=7-(n=(o=e[t])+"").length)&&(i+=P(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return i+o}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,a;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(a=e.d.length)?n:a;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===a?0:n>a^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return S(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return w(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,a=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(i))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(i)?new r(0):(l=!1,t=b(j(this,a),j(e,a),a),l=!0,S(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?k(this,e):v(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(c+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):S(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return j(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?v(this,e):k(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(s+e);if(t=w(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,a,i,o,s=this.constructor;if(this.s<1){if(!this.s)return new s(0);throw Error(c+"NaN")}for(e=w(this),l=!1,0==(a=Math.sqrt(+this))||a==1/0?(((t=g(this.d)).length+e)%2==0&&(t+="0"),a=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new s(t=a==1/0?"5e"+e:(t=a.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new s(a.toString()),a=o=(r=s.precision)+3;;)if(n=(i=n).plus(b(this,i,o+2)).times(.5),g(i.d).slice(0,o)===(t=g(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),a==o&&"4999"==t){if(S(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=t)break;o+=4}return l=!0,S(n,r)},y.times=y.mul=function(e){var t,r,n,a,i,o,c,s,u,f=this.constructor,d=this.d,p=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(s=d.length)<(u=p.length)&&(i=d,d=p,p=i,o=s,s=u,u=o),i=[],n=o=s+u;n--;)i.push(0);for(n=u;--n>=0;){for(t=0,a=s+n;a>n;)c=i[a]+p[n]*d[a-n-1]+t,i[a--]=c%1e7|0,t=c/1e7|0;i[a]=(i[a]+t)%1e7|0}for(;!i[--o];)i.pop();return t?++r:i.shift(),e.d=i,e.e=r,l?S(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(m(e,0,1e9),void 0===t?t=n.rounding:m(t,0,8),S(r,e+w(r)+1,t))},y.toExponential=function(e,t){var r,n=this,a=n.constructor;return void 0===e?r=A(n,!0):(m(e,0,1e9),void 0===t?t=a.rounding:m(t,0,8),r=A(n=S(new a(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,a=this.constructor;return void 0===e?A(this):(m(e,0,1e9),void 0===t?t=a.rounding:m(t,0,8),r=A((n=S(new a(this),e+w(this)+1,t)).abs(),!1,e+w(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return S(new e(this),w(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,a,o,s,u=this,d=u.constructor,p=+(e=new d(e));if(!e.s)return new d(i);if(!(u=new d(u)).s){if(e.s<1)throw Error(c+"Infinity");return u}if(u.eq(i))return u;if(n=d.precision,e.eq(i))return S(u,n);if(s=(t=e.e)>=(r=e.d.length-1),o=u.s,s){if((r=p<0?-p:p)<=0x1fffffffffffff){for(a=new d(i),t=Math.ceil(n/7+4),l=!1;r%2&&M((a=a.times(u)).d,t),0!==(r=f(r/2));)M((u=u.times(u)).d,t);return l=!0,e.s<0?new d(i).div(a):S(a,n)}}else if(o<0)throw Error(c+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,u.s=1,l=!1,a=e.times(j(u,n+12)),l=!0,(a=x(a)).s=o,a},y.toPrecision=function(e,t){var r,n,a=this,i=a.constructor;return void 0===e?(r=w(a),n=A(a,r<=i.toExpNeg||r>=i.toExpPos)):(m(e,1,1e9),void 0===t?t=i.rounding:m(t,0,8),r=w(a=S(new i(a),e,t)),n=A(a,e<=r||r<=i.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(m(e,1,1e9),void 0===t?t=r.rounding:m(t,0,8)),S(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=w(this),t=this.constructor;return A(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,a=e.length;for(e=e.slice();a--;)r=e[a]*t+n,e[a]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var a,i;if(r!=n)i=r>n?1:-1;else for(a=i=0;a<r;a++)if(e[a]!=t[a]){i=e[a]>t[a]?1:-1;break}return i}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,a,i,o){var l,s,u,f,d,p,h,y,v,m,g,b,x,O,P,j,E,k,A=n.constructor,M=n.s==a.s?1:-1,T=n.d,C=a.d;if(!n.s)return new A(n);if(!a.s)throw Error(c+"Division by zero");for(u=0,s=n.e-a.e,E=C.length,P=T.length,y=(h=new A(M)).d=[];C[u]==(T[u]||0);)++u;if(C[u]>(T[u]||0)&&--s,(b=null==i?i=A.precision:o?i+(w(n)-w(a))+1:i)<0)return new A(0);if(b=b/7+2|0,u=0,1==E)for(f=0,C=C[0],b++;(u<P||f)&&b--;u++)x=1e7*f+(T[u]||0),y[u]=x/C|0,f=x%C|0;else{for((f=1e7/(C[0]+1)|0)>1&&(C=e(C,f),T=e(T,f),E=C.length,P=T.length),O=E,m=(v=T.slice(0,E)).length;m<E;)v[m++]=0;(k=C.slice()).unshift(0),j=C[0],C[1]>=1e7/2&&++j;do f=0,(l=t(C,v,E,m))<0?(g=v[0],E!=m&&(g=1e7*g+(v[1]||0)),(f=g/j|0)>1?(f>=1e7&&(f=1e7-1),p=(d=e(C,f)).length,m=v.length,1==(l=t(d,v,p,m))&&(f--,r(d,E<p?k:C,p))):(0==f&&(l=f=1),d=C.slice()),(p=d.length)<m&&d.unshift(0),r(v,d,m),-1==l&&(m=v.length,(l=t(C,v,E,m))<1&&(f++,r(v,E<m?k:C,m))),m=v.length):0===l&&(f++,v=[0]),y[u++]=f,l&&v[0]?v[m++]=T[O]||0:(v=[T[O]],m=1);while((O++<P||void 0!==v[0])&&b--)}return y[0]||y.shift(),h.e=s,S(h,o?i+w(h)+1:i)}}();function x(e,t){var r,n,a,o,c,s=0,f=0,p=e.constructor,h=p.precision;if(w(e)>16)throw Error(u+w(e));if(!e.s)return new p(i);for(null==t?(l=!1,c=h):c=t,o=new p(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(c+=Math.log(d(2,f))/Math.LN10*2+5|0,r=n=a=new p(i),p.precision=c;;){if(n=S(n.times(e),c),r=r.times(++s),g((o=a.plus(b(n,r,c))).d).slice(0,c)===g(a.d).slice(0,c)){for(;f--;)a=S(a.times(a),c);return p.precision=h,null==t?(l=!0,S(a,h)):a}a=o}}function w(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(c+"LN10 precision limit exceeded");return S(new e(e.LN10),t)}function P(e){for(var t="";e--;)t+="0";return t}function j(e,t){var r,n,a,o,s,u,f,d,p,h=1,y=e,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==t?(l=!1,d=x):d=t,y.eq(10))return null==t&&(l=!0),O(m,d);if(m.precision=d+=10,n=(r=g(v)).charAt(0),!(15e14>Math.abs(o=w(y))))return f=O(m,d+2,x).times(o+""),y=j(new m(n+"."+r.slice(1)),d-10).plus(f),m.precision=x,null==t?(l=!0,S(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=g((y=y.times(e)).d)).charAt(0),h++;for(o=w(y),n>1?(y=new m("0."+r),o++):y=new m(n+"."+r.slice(1)),u=s=y=b(y.minus(i),y.plus(i),d),p=S(y.times(y),d),a=3;;){if(s=S(s.times(p),d),g((f=u.plus(b(s,new m(a),d))).d).slice(0,d)===g(u.d).slice(0,d))return u=u.times(2),0!==o&&(u=u.plus(O(m,d+2,x).times(o+""))),u=b(u,new m(h),d),m.precision=x,null==t?(l=!0,S(u,x)):u;u=f,a+=2}}function E(e,t){var r,n,a;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(a=t.length;48===t.charCodeAt(a-1);)--a;if(t=t.slice(n,a)){if(a-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<a){for(n&&e.d.push(+t.slice(0,n)),a-=7;n<a;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=a;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>h||e.e<-h))throw Error(u+r)}else e.s=0,e.e=0,e.d=[0];return e}function S(e,t,r){var n,a,i,o,c,s,p,y,v=e.d;for(o=1,i=v[0];i>=10;i/=10)o++;if((n=t-o)<0)n+=7,a=t,p=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(i=v.length))return e;for(o=1,p=i=v[y];i>=10;i/=10)o++;n%=7,a=n-7+o}if(void 0!==r&&(c=p/(i=d(10,o-a-1))%10|0,s=t<0||void 0!==v[y+1]||p%i,s=r<4?(c||s)&&(0==r||r==(e.s<0?3:2)):c>5||5==c&&(4==r||s||6==r&&(n>0?a>0?p/d(10,o-a):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return s?(i=w(e),v.length=1,t=t-i-1,v[0]=d(10,(7-t%7)%7),e.e=f(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,i=1,y--):(v.length=y+1,i=d(10,7-n),v[y]=a>0?(p/d(10,o-a)%d(10,a)|0)*i:0),s)for(;;)if(0==y){1e7==(v[0]+=i)&&(v[0]=1,++e.e);break}else{if(v[y]+=i,1e7!=v[y])break;v[y--]=0,i=1}for(n=v.length;0===v[--n];)v.pop();if(l&&(e.e>h||e.e<-h))throw Error(u+w(e));return e}function k(e,t){var r,n,a,i,o,c,s,u,f,d,p=e.constructor,h=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),l?S(t,h):t;if(s=e.d,d=t.d,n=t.e,u=e.e,s=s.slice(),o=u-n){for((f=o<0)?(r=s,o=-o,c=d.length):(r=d,n=u,c=s.length),o>(a=Math.max(Math.ceil(h/7),c)+2)&&(o=a,r.length=1),r.reverse(),a=o;a--;)r.push(0);r.reverse()}else{for((f=(a=s.length)<(c=d.length))&&(c=a),a=0;a<c;a++)if(s[a]!=d[a]){f=s[a]<d[a];break}o=0}for(f&&(r=s,s=d,d=r,t.s=-t.s),c=s.length,a=d.length-c;a>0;--a)s[c++]=0;for(a=d.length;a>o;){if(s[--a]<d[a]){for(i=a;i&&0===s[--i];)s[i]=1e7-1;--s[i],s[a]+=1e7}s[a]-=d[a]}for(;0===s[--c];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(t.d=s,t.e=n,l?S(t,h):t):new p(0)}function A(e,t,r){var n,a=w(e),i=g(e.d),o=i.length;return t?(r&&(n=r-o)>0?i=i.charAt(0)+"."+i.slice(1)+P(n):o>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(a<0?"e":"e+")+a):a<0?(i="0."+P(-a-1)+i,r&&(n=r-o)>0&&(i+=P(n))):a>=o?(i+=P(a+1-o),r&&(n=r-a-1)>0&&(i=i+"."+P(n))):((n=a+1)<o&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-o)>0&&(a+1===o&&(i+="."),i+=P(n))),e.s<0?"-"+i:i}function M(e,t){if(e.length>t)return e.length=t,!0}function T(e){if(!e||"object"!=typeof e)throw Error(c+"Object expected");var t,r,n,a=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<a.length;t+=3)if(void 0!==(n=e[r=a[t]]))if(f(n)===n&&n>=a[t+1]&&n<=a[t+2])this[r]=n;else throw Error(s+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(s+r+": "+n);return this}(o=function e(t){var r,n,a;function i(e){if(!(this instanceof i))return new i(e);if(this.constructor=i,e instanceof i){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(s+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return E(this,e.toString())}if("string"!=typeof e)throw Error(s+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,p.test(e))E(this,e);else throw Error(s+e)}if(i.prototype=y,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=e,i.config=i.set=T,void 0===t&&(t={}),t)for(r=0,a=["precision","rounding","toExpNeg","toExpPos","LN10"];r<a.length;)t.hasOwnProperty(n=a[r++])||(t[n]=this[n]);return i.config(t),i}(o)).default=o.Decimal=o,i=new o(1),void 0===(n=(function(){return o}).call(t,r,t,e))||(e.exports=n)}(0)},10656:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(68179),a=r(99279);t.isArrayLikeObject=function(e){return a.isObjectLike(e)&&n.isArrayLike(e)}},10921:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(27040),a=r(14545),i=r(46200),o=r(64072);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let i=t[r];if(void 0===i)if(a.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return i}case"number":case"symbol":{"number"==typeof r&&(r=i.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var c=t,s=r,u=l;if(0===s.length)return u;let e=c;for(let t=0;t<s.length;t++){if(null==e||n.isUnsafeProperty(s[t]))return u;e=e[s[t]]}return void 0===e?u:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},12429:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44117);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},14545:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},14804:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),a=r(12429);t.matches=function(e){return e=a.cloneDeep(e),t=>n.isMatch(t,e)}},14986:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},15160:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},19452:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},20241:(e,t,r)=>{e.exports=r(22434).sortBy},20400:(e,t,r)=>{e.exports=r(82962).throttle},20697:(e,t,r)=>{e.exports=r(73054).omit},22188:(e,t,r)=>{e.exports=r(85252).isEqual},22434:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(47064),a=r(55998),i=r(64373);t.sortBy=function(e,...t){let r=t.length;return r>1&&i.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&i.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,a.flatten(t),["asc"])}},22436:(e,t,r)=>{"use strict";var n=r(12115),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,o=n.useEffect,l=n.useLayoutEffect,c=n.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!a(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),a=n[0].inst,u=n[1];return l(function(){a.value=r,a.getSnapshot=t,s(a)&&u({inst:a})},[e,r,t]),o(function(){return s(a)&&u({inst:a}),e(function(){s(a)&&u({inst:a})})},[e]),c(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},22520:(e,t,r)=>{"use strict";var n=r(44134).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let a=r(1147),i=r(98221),o=r(15160),l=r(42721),c=r(83616);t.isEqualWith=function(e,t,r){return function e(t,r,s,u,f,d,p){let h=p(t,r,s,u,f,d);if(void 0!==h)return h;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,s,u,f){if(Object.is(r,s))return!0;let d=o.getTag(r),p=o.getTag(s);if(d===l.argumentsTag&&(d=l.objectTag),p===l.argumentsTag&&(p=l.objectTag),d!==p)return!1;switch(d){case l.stringTag:return r.toString()===s.toString();case l.numberTag:{let e=r.valueOf(),t=s.valueOf();return c.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),s.valueOf());case l.regexpTag:return r.source===s.source&&r.flags===s.flags;case l.functionTag:return r===s}let h=(u=u??new Map).get(r),y=u.get(s);if(null!=h&&null!=y)return h===s;u.set(r,s),u.set(s,r);try{switch(d){case l.mapTag:if(r.size!==s.size)return!1;for(let[t,n]of r.entries())if(!s.has(t)||!e(n,s.get(t),t,r,s,u,f))return!1;return!0;case l.setTag:{if(r.size!==s.size)return!1;let t=Array.from(r.values()),n=Array.from(s.values());for(let a=0;a<t.length;a++){let i=t[a],o=n.findIndex(t=>e(i,t,void 0,r,s,u,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(s)||r.length!==s.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],s[t],t,r,s,u,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==s.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(s),u,f);case l.dataViewTag:if(r.byteLength!==s.byteLength||r.byteOffset!==s.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(s),u,f);case l.errorTag:return r.name===s.name&&r.message===s.message;case l.objectTag:{if(!(t(r.constructor,s.constructor,u,f)||a.isPlainObject(r)&&a.isPlainObject(s)))return!1;let n=[...Object.keys(r),...i.getSymbols(r)],o=[...Object.keys(s),...i.getSymbols(s)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let a=n[t],i=r[a];if(!Object.hasOwn(s,a))return!1;let o=s[a];if(!e(i,o,a,r,s,u,f))return!1}return!0}default:return!1}}finally{u.delete(r),u.delete(s)}}(t,r,d,p)}(e,t,void 0,void 0,void 0,void 0,r)}},23676:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let a=e[n],i=t(a);r.has(i)||r.set(i,a)}return Array.from(r.values())}},24517:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),a=r(46200),i=r(37298),o=r(10921),l=r(93205);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=a.toKey(e)}return t=i.cloneDeep(t),function(r){let a=o.get(r,e);return void 0===a?l.has(r,e):void 0===t?void 0===a:n.isMatch(a,t)}}},27040:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},29738:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(44117),a=r(42721);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,i,o,l)=>{let c=t?.(r,i,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case a.numberTag:case a.stringTag:case a.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case a.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},31901:(e,t,r)=>{e.exports=r(72605).minBy},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},36633:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},37298:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(29738);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},39611:(e,t,r)=>{"use strict";r(4993)},39688:(e,t,r)=>{"use strict";r.d(t,{QP:()=>ee});let n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],a=t.nextPart.get(r),i=a?n(e.slice(1),a):void 0;if(i)return i;if(0===t.validators.length)return;let o=e.join("-");return t.validators.find(({validator:e})=>e(o))?.classGroupId},a=/^\[(.+)\]$/,i=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:o(t,e)).classGroupId=r;return}if("function"==typeof e)return l(e)?void i(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,a])=>{i(a,o(t,e),r,n)})})},o=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},l=e=>e.isThemeGetter,c=/\s+/;function s(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=u(e))&&(n&&(n+=" "),n+=t);return n}let u=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=u(e[n]))&&(r&&(r+=" "),r+=t);return r},f=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},d=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,h=/^\d+\/\d+$/,y=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,v=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,m=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,g=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,b=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,x=e=>h.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),O=e=>!!e&&Number.isInteger(Number(e)),P=e=>e.endsWith("%")&&w(e.slice(0,-1)),j=e=>y.test(e),E=()=>!0,S=e=>v.test(e)&&!m.test(e),k=()=>!1,A=e=>g.test(e),M=e=>b.test(e),T=e=>!D(e)&&!z(e),C=e=>V(e,Y,k),D=e=>d.test(e),N=e=>V(e,G,S),I=e=>V(e,Z,w),_=e=>V(e,H,k),L=e=>V(e,q,M),R=e=>V(e,Q,A),z=e=>p.test(e),K=e=>X(e,G),B=e=>X(e,J),$=e=>X(e,H),F=e=>X(e,Y),U=e=>X(e,q),W=e=>X(e,Q,!0),V=(e,t,r)=>{let n=d.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},X=(e,t,r=!1)=>{let n=p.exec(e);return!!n&&(n[1]?t(n[1]):r)},H=e=>"position"===e||"percentage"===e,q=e=>"image"===e||"url"===e,Y=e=>"length"===e||"size"===e||"bg-size"===e,G=e=>"length"===e,Z=e=>"number"===e,J=e=>"family-name"===e,Q=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let r,o,l,u=function(c){let s;return o=(r={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,a=(a,i)=>{r.set(a,i),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(a(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):a(e,t)}}})((s=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r,n=[],a=0,i=0,o=0;for(let r=0;r<e.length;r++){let l=e[r];if(0===a&&0===i){if(":"===l){n.push(e.slice(o,r)),o=r+1;continue}if("/"===l){t=r;continue}}"["===l?a++:"]"===l?a--:"("===l?i++:")"===l&&i--}let l=0===n.length?e:e.substring(o),c=(r=l).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:n,hasImportantModifier:c!==l,baseClassName:c,maybePostfixModifierPosition:t&&t>o?t-o:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n})(s),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}})(s),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)i(r[e],n,e,t);return n})(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||(e=>{if(a.test(e)){let t=a.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&o[e]?[...n,...o[e]]:n}}})(s)}).cache.get,l=r.cache.set,u=f,f(c)};function f(e){let t=o(e);if(t)return t;let n=((e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:a,sortModifiers:i}=t,o=[],l=e.trim().split(c),s="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:c,modifiers:u,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:p}=r(t);if(c){s=t+(s.length>0?" "+s:s);continue}let h=!!p,y=n(h?d.substring(0,p):d);if(!y){if(!h||!(y=n(d))){s=t+(s.length>0?" "+s:s);continue}h=!1}let v=i(u).join(":"),m=f?v+"!":v,g=m+y;if(o.includes(g))continue;o.push(g);let b=a(y,h);for(let e=0;e<b.length;++e){let t=b[e];o.push(m+t)}s=t+(s.length>0?" "+s:s)}return s})(e,r);return l(e,n),n}return function(){return u(s.apply(null,arguments))}}(()=>{let e=f("color"),t=f("font"),r=f("text"),n=f("font-weight"),a=f("tracking"),i=f("leading"),o=f("breakpoint"),l=f("container"),c=f("spacing"),s=f("radius"),u=f("shadow"),d=f("inset-shadow"),p=f("text-shadow"),h=f("drop-shadow"),y=f("blur"),v=f("perspective"),m=f("aspect"),g=f("ease"),b=f("animate"),S=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],A=()=>[...k(),z,D],M=()=>["auto","hidden","clip","visible","scroll"],V=()=>["auto","contain","none"],X=()=>[z,D,c],H=()=>[x,"full","auto",...X()],q=()=>[O,"none","subgrid",z,D],Y=()=>["auto",{span:["full",O,z,D]},O,z,D],G=()=>[O,"auto",z,D],Z=()=>["auto","min","max","fr",z,D],J=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...X()],et=()=>[x,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...X()],er=()=>[e,z,D],en=()=>[...k(),$,_,{position:[z,D]}],ea=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ei=()=>["auto","cover","contain",F,C,{size:[z,D]}],eo=()=>[P,K,N],el=()=>["","none","full",s,z,D],ec=()=>["",w,K,N],es=()=>["solid","dashed","dotted","double"],eu=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ef=()=>[w,P,$,_],ed=()=>["","none",y,z,D],ep=()=>["none",w,z,D],eh=()=>["none",w,z,D],ey=()=>[w,z,D],ev=()=>[x,"full",...X()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[E],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[T],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",w],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",x,D,z,m]}],container:["container"],columns:[{columns:[w,D,z,l]}],"break-after":[{"break-after":S()}],"break-before":[{"break-before":S()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:A()}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:V()}],"overscroll-x":[{"overscroll-x":V()}],"overscroll-y":[{"overscroll-y":V()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:H()}],"inset-x":[{"inset-x":H()}],"inset-y":[{"inset-y":H()}],start:[{start:H()}],end:[{end:H()}],top:[{top:H()}],right:[{right:H()}],bottom:[{bottom:H()}],left:[{left:H()}],visibility:["visible","invisible","collapse"],z:[{z:[O,"auto",z,D]}],basis:[{basis:[x,"full","auto",l,...X()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,x,"auto","initial","none",D]}],grow:[{grow:["",w,z,D]}],shrink:[{shrink:["",w,z,D]}],order:[{order:[O,"first","last","none",z,D]}],"grid-cols":[{"grid-cols":q()}],"col-start-end":[{col:Y()}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":q()}],"row-start-end":[{row:Y()}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:X()}],"gap-x":[{"gap-x":X()}],"gap-y":[{"gap-y":X()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:X()}],px:[{px:X()}],py:[{py:X()}],ps:[{ps:X()}],pe:[{pe:X()}],pt:[{pt:X()}],pr:[{pr:X()}],pb:[{pb:X()}],pl:[{pl:X()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":X()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":X()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[l,"screen",...et()]}],"min-w":[{"min-w":[l,"screen","none",...et()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[o]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,K,N]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,z,I]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",P,D]}],"font-family":[{font:[B,D,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,z,D]}],"line-clamp":[{"line-clamp":[w,"none",z,I]}],leading:[{leading:[i,...X()]}],"list-image":[{"list-image":["none",z,D]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",z,D]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...es(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",z,N]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[w,"auto",z,D]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:X()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",z,D]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",z,D]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:ea()}],"bg-size":[{bg:ei()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},O,z,D],radial:["",z,D],conic:[O,z,D]},U,L]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:eo()}],"gradient-via-pos":[{via:eo()}],"gradient-to-pos":[{to:eo()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:el()}],"rounded-s":[{"rounded-s":el()}],"rounded-e":[{"rounded-e":el()}],"rounded-t":[{"rounded-t":el()}],"rounded-r":[{"rounded-r":el()}],"rounded-b":[{"rounded-b":el()}],"rounded-l":[{"rounded-l":el()}],"rounded-ss":[{"rounded-ss":el()}],"rounded-se":[{"rounded-se":el()}],"rounded-ee":[{"rounded-ee":el()}],"rounded-es":[{"rounded-es":el()}],"rounded-tl":[{"rounded-tl":el()}],"rounded-tr":[{"rounded-tr":el()}],"rounded-br":[{"rounded-br":el()}],"rounded-bl":[{"rounded-bl":el()}],"border-w":[{border:ec()}],"border-w-x":[{"border-x":ec()}],"border-w-y":[{"border-y":ec()}],"border-w-s":[{"border-s":ec()}],"border-w-e":[{"border-e":ec()}],"border-w-t":[{"border-t":ec()}],"border-w-r":[{"border-r":ec()}],"border-w-b":[{"border-b":ec()}],"border-w-l":[{"border-l":ec()}],"divide-x":[{"divide-x":ec()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ec()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...es(),"hidden","none"]}],"divide-style":[{divide:[...es(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...es(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,z,D]}],"outline-w":[{outline:["",w,K,N]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",u,W,R]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",d,W,R]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:ec()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[w,N]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":ec()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",p,W,R]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[w,z,D]}],"mix-blend":[{"mix-blend":[...eu(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":eu()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":ef()}],"mask-image-linear-to-pos":[{"mask-linear-to":ef()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ef()}],"mask-image-t-to-pos":[{"mask-t-to":ef()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ef()}],"mask-image-r-to-pos":[{"mask-r-to":ef()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ef()}],"mask-image-b-to-pos":[{"mask-b-to":ef()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ef()}],"mask-image-l-to-pos":[{"mask-l-to":ef()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ef()}],"mask-image-x-to-pos":[{"mask-x-to":ef()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ef()}],"mask-image-y-to-pos":[{"mask-y-to":ef()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[z,D]}],"mask-image-radial-from-pos":[{"mask-radial-from":ef()}],"mask-image-radial-to-pos":[{"mask-radial-to":ef()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":k()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":ef()}],"mask-image-conic-to-pos":[{"mask-conic-to":ef()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:ea()}],"mask-size":[{mask:ei()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",z,D]}],filter:[{filter:["","none",z,D]}],blur:[{blur:ed()}],brightness:[{brightness:[w,z,D]}],contrast:[{contrast:[w,z,D]}],"drop-shadow":[{"drop-shadow":["","none",h,W,R]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",w,z,D]}],"hue-rotate":[{"hue-rotate":[w,z,D]}],invert:[{invert:["",w,z,D]}],saturate:[{saturate:[w,z,D]}],sepia:[{sepia:["",w,z,D]}],"backdrop-filter":[{"backdrop-filter":["","none",z,D]}],"backdrop-blur":[{"backdrop-blur":ed()}],"backdrop-brightness":[{"backdrop-brightness":[w,z,D]}],"backdrop-contrast":[{"backdrop-contrast":[w,z,D]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,z,D]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,z,D]}],"backdrop-invert":[{"backdrop-invert":["",w,z,D]}],"backdrop-opacity":[{"backdrop-opacity":[w,z,D]}],"backdrop-saturate":[{"backdrop-saturate":[w,z,D]}],"backdrop-sepia":[{"backdrop-sepia":["",w,z,D]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":X()}],"border-spacing-x":[{"border-spacing-x":X()}],"border-spacing-y":[{"border-spacing-y":X()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",z,D]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",z,D]}],ease:[{ease:["linear","initial",g,z,D]}],delay:[{delay:[w,z,D]}],animate:[{animate:["none",b,z,D]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[v,z,D]}],"perspective-origin":[{"perspective-origin":A()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:eh()}],"scale-x":[{"scale-x":eh()}],"scale-y":[{"scale-y":eh()}],"scale-z":[{"scale-z":eh()}],"scale-3d":["scale-3d"],skew:[{skew:ey()}],"skew-x":[{"skew-x":ey()}],"skew-y":[{"skew-y":ey()}],transform:[{transform:[z,D,"","none","gpu","cpu"]}],"transform-origin":[{origin:A()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ev()}],"translate-x":[{"translate-x":ev()}],"translate-y":[{"translate-y":ev()}],"translate-z":[{"translate-z":ev()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",z,D]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":X()}],"scroll-mx":[{"scroll-mx":X()}],"scroll-my":[{"scroll-my":X()}],"scroll-ms":[{"scroll-ms":X()}],"scroll-me":[{"scroll-me":X()}],"scroll-mt":[{"scroll-mt":X()}],"scroll-mr":[{"scroll-mr":X()}],"scroll-mb":[{"scroll-mb":X()}],"scroll-ml":[{"scroll-ml":X()}],"scroll-p":[{"scroll-p":X()}],"scroll-px":[{"scroll-px":X()}],"scroll-py":[{"scroll-py":X()}],"scroll-ps":[{"scroll-ps":X()}],"scroll-pe":[{"scroll-pe":X()}],"scroll-pt":[{"scroll-pt":X()}],"scroll-pr":[{"scroll-pr":X()}],"scroll-pb":[{"scroll-pb":X()}],"scroll-pl":[{"scroll-pl":X()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",z,D]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[w,K,N,I]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},40220:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},42694:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8287);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},42721:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},44117:(e,t,r)=>{"use strict";var n=r(44134).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let a=r(98221),i=r(15160),o=r(42721),l=r(36633),c=r(80885);function s(e,t,r,a=new Map,f){let d=f?.(e,t,r,a);if(null!=d)return d;if(l.isPrimitive(e))return e;if(a.has(e))return a.get(e);if(Array.isArray(e)){let t=Array(e.length);a.set(e,t);for(let n=0;n<e.length;n++)t[n]=s(e[n],n,r,a,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,i]of(a.set(e,t),e))t.set(n,s(i,n,r,a,f));return t}if(e instanceof Set){let t=new Set;for(let n of(a.set(e,t),e))t.add(s(n,void 0,r,a,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(c.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);a.set(e,t);for(let n=0;n<e.length;n++)t[n]=s(e[n],n,r,a,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return a.set(e,t),u(t,e,r,a,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return a.set(e,t),u(t,e,r,a,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return a.set(e,t),u(t,e,r,a,f),t}if(e instanceof Error){let t=new e.constructor;return a.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,u(t,e,r,a,f),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return a.set(e,t),u(t,e,r,a,f),t}return e}function u(e,t,r=e,n,i){let o=[...Object.keys(t),...a.getSymbols(t)];for(let a=0;a<o.length;a++){let l=o[a],c=Object.getOwnPropertyDescriptor(e,l);(null==c||c.writable)&&(e[l]=s(t[l],l,r,n,i))}}t.cloneDeepWith=function(e,t){return s(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=s,t.copyProperties=u},45643:(e,t,r)=>{"use strict";e.exports=r(6115)},46200:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},47064:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(55181),a=r(51551),i=r(64072);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||a.isKey(e))?e:{key:e,path:i.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>{var r,n;return r=t,null==(n=e)||null==r?n:"object"==typeof r&&"key"in r?Object.hasOwn(n,r.key)?n[r.key]:l(n,r.path):"function"==typeof r?r(n):Array.isArray(r)?l(n,r):"object"==typeof n?n[r]:n})})).slice().sort((e,t)=>{for(let a=0;a<c.length;a++){let i=n.compareValues(e.criteria[a],t.criteria[a],r[a]);if(0!==i)return i}return 0}).map(e=>e.original)}},49033:(e,t,r)=>{"use strict";e.exports=r(22436)},49901:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(64373),a=r(64664);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=a.toFinite(e),void 0===t?(t=e,e=0):t=a.toFinite(t),r=void 0===r?e<t?1:-1:a.toFinite(r);let i=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(i);for(let t=0;t<i;t++)o[t]=e,e+=r;return o}},50177:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15160);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},51551:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(8287),a=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,i=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(i.test(e)||!a.test(e))||null!=t&&Object.hasOwn(t,e))}},52596:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",a=arguments.length;r<a;r++)(e=arguments[r])&&(t=function e(t){var r,n,a="";if("string"==typeof t||"number"==typeof t)a+=t;else if("object"==typeof t)if(Array.isArray(t)){var i=t.length;for(r=0;r<i;r++)t[r]&&(n=e(t[r]))&&(a&&(a+=" "),a+=n)}else for(n in t)t[n]&&(a&&(a+=" "),a+=n);return a}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n})},53588:(e,t)=>{"use strict";var r=Symbol.for("react.transitional.element"),n=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler");Symbol.for("react.provider");var l=Symbol.for("react.consumer"),c=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),f=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),h=Symbol.for("react.view_transition");Symbol.for("react.client.reference");t.zv=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case a:case o:case i:case u:case f:case h:return e;default:switch(e=e&&e.$$typeof){case c:case s:case p:case d:case l:return e;default:return t}}case n:return t}}}(e)===a}},55181:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let a=r(e),i=r(t);if(a===i&&0===a){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?i-a:a-i}return 0}},55998:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),a=(e,t)=>{for(let i=0;i<e.length;i++){let o=e[i];Array.isArray(o)&&t<n?a(o,t+1):r.push(o)}};return a(e,0),r}},58080:(e,t,r)=>{e.exports=r(78359).last},60512:(e,t,r)=>{e.exports=r(7547).uniqBy},60760:(e,t,r)=>{"use strict";r.d(t,{N:()=>g});var n=r(95155),a=r(12115),i=r(90869),o=r(82885),l=r(97494),c=r(80845),s=r(27351),u=r(51508);class f extends a.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,s.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:r,anchorX:i,root:o}=e,l=(0,a.useId)(),c=(0,a.useRef)(null),s=(0,a.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,a.useContext)(u.Q);return(0,a.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:a,right:u}=s.current;if(r||!c.current||!e||!t)return;c.current.dataset.motionPopId=l;let f=document.createElement("style");d&&(f.nonce=d);let p=null!=o?o:document.head;return p.appendChild(f),f.sheet&&f.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(a):"right: ".concat(u),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{p.removeChild(f),p.contains(f)&&p.removeChild(f)}},[r]),(0,n.jsx)(f,{isPresent:r,childRef:c,sizeRef:s,children:a.cloneElement(t,{ref:c})})}let p=e=>{let{children:t,initial:r,isPresent:i,onExitComplete:l,custom:s,presenceAffectsLayout:u,mode:f,anchorX:p,root:y}=e,v=(0,o.M)(h),m=(0,a.useId)(),g=!0,b=(0,a.useMemo)(()=>(g=!1,{id:m,initial:r,isPresent:i,custom:s,onExitComplete:e=>{for(let t of(v.set(e,!0),v.values()))if(!t)return;l&&l()},register:e=>(v.set(e,!1),()=>v.delete(e))}),[i,v,l]);return u&&g&&(b={...b}),(0,a.useMemo)(()=>{v.forEach((e,t)=>v.set(t,!1))},[i]),a.useEffect(()=>{i||v.size||!l||l()},[i]),"popLayout"===f&&(t=(0,n.jsx)(d,{isPresent:i,anchorX:p,root:y,children:t})),(0,n.jsx)(c.t.Provider,{value:b,children:t})};function h(){return new Map}var y=r(32082);let v=e=>e.key||"";function m(e){let t=[];return a.Children.forEach(e,e=>{(0,a.isValidElement)(e)&&t.push(e)}),t}let g=e=>{let{children:t,custom:r,initial:c=!0,onExitComplete:s,presenceAffectsLayout:u=!0,mode:f="sync",propagate:d=!1,anchorX:h="left",root:g}=e,[b,x]=(0,y.xQ)(d),w=(0,a.useMemo)(()=>m(t),[t]),O=d&&!b?[]:w.map(v),P=(0,a.useRef)(!0),j=(0,a.useRef)(w),E=(0,o.M)(()=>new Map),[S,k]=(0,a.useState)(w),[A,M]=(0,a.useState)(w);(0,l.E)(()=>{P.current=!1,j.current=w;for(let e=0;e<A.length;e++){let t=v(A[e]);O.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[A,O.length,O.join("-")]);let T=[];if(w!==S){let e=[...w];for(let t=0;t<A.length;t++){let r=A[t],n=v(r);O.includes(n)||(e.splice(t,0,r),T.push(r))}return"wait"===f&&T.length&&(e=T),M(m(e)),k(w),null}let{forceRender:C}=(0,a.useContext)(i.L);return(0,n.jsx)(n.Fragment,{children:A.map(e=>{let t=v(e),a=(!d||!!b)&&(w===A||O.includes(t));return(0,n.jsx)(p,{isPresent:a,initial:(!P.current||!!c)&&void 0,custom:r,presenceAffectsLayout:u,mode:f,root:g,onExitComplete:a?void 0:()=>{if(!E.has(t))return;E.set(t,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(null==C||C(),M(j.current),d&&(null==x||x()),s&&s())},anchorX:h,children:e},t)})})}},62194:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(10921);t.property=function(e){return function(t){return n.get(t,e)}}},62513:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.minBy=function(e,t){if(0===e.length)return;let r=e[0],n=t(r);for(let a=1;a<e.length;a++){let i=e[a],o=t(i);o<n&&(n=o,r=i)}return r}},64072:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,a="",i="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];i?"\\"===l&&n+1<r?a+=e[++n]:l===i?i="":a+=l:o?'"'===l||"'"===l?i=l:"]"===l?(o=!1,t.push(a),a=""):a+=l:"["===l?(o=!0,a&&(t.push(a),a="")):"."===l?a&&(t.push(a),a=""):a+=l,n++}return a&&t.push(a),t}},64373:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98412),a=r(68179),i=r(82384),o=r(83616);t.isIterateeCall=function(e,t,r){return!!i.isObject(r)&&(!!("number"==typeof t&&a.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},64664:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42694);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},68179:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(19452);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},70027:(e,t,r)=>{e.exports=r(84043).maxBy},72465:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},72605:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(62513),a=r(72465),i=r(81571);t.minBy=function(e,t){if(null!=e)return n.minBy(Array.from(e),i.iteratee(t??a.identity))}},72744:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98132),a=r(82384),i=r(36633),o=r(83616);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map){var a=e,o=t,l=r,u=n;if(0===o.size)return!0;if(!(a instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(a.get(e),t,e,a,o,u))return!1;return!0}if(t instanceof Set)return s(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let a=0;a<f.length;a++){let o=f[a];if(!i.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!a.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let a=new Set;for(let i=0;i<t.length;i++){let o=t[i],l=!1;for(let c=0;c<e.length;c++){if(a.has(c))continue;let s=e[c],u=!1;if(r(s,o,i,e,t,n)&&(u=!0),u){a.add(c),l=!0;break}}if(!l)return!1}return!0}function s(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,a,i,o,c){let s=r(t,n,a,i,o,c);return void 0!==s?!!s:l(t,n,e,c)},new Map)},t.isSetMatch=s},73054:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(94456),a=r(12429);t.omit=function(e,...t){if(null==e)return{};let r=a.cloneDeep(e);for(let e=0;e<t.length;e++){let a=t[e];switch(typeof a){case"object":Array.isArray(a)||(a=Array.from(a));for(let e=0;e<a.length;e++){let t=a[e];n.unset(r,t)}break;case"string":case"symbol":case"number":n.unset(r,a)}}return r}},78359:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(40220),a=r(14986),i=r(68179);t.last=function(e){if(i.isArrayLike(e))return n.last(a.toArray(e))}},78673:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t=0,r={}){let n;"object"!=typeof r&&(r={});let a=null,i=null,o=null,l=0,c=null,{leading:s=!1,trailing:u=!0,maxWait:f}=r,d="maxWait"in r,p=d?Math.max(Number(f)||0,t):0,h=t=>(null!==a&&(n=e.apply(i,a)),a=i=null,l=t,n),y=e=>(c=null,u&&null!==a)?h(e):n,v=e=>{if(null===o)return!0;let r=e-o,n=d&&e-l>=p;return r>=t||r<0||n},m=()=>{let e=Date.now();if(v(e))return y(e);c=setTimeout(m,(e=>{let r=t-(null===o?0:e-o),n=p-(e-l);return d?Math.min(r,n):r})(e))},g=function(...e){let r=Date.now(),u=v(r);if(a=e,i=this,o=r,u){if(null===c)return(l=r,c=setTimeout(m,t),s&&null!==a)?h(r):n;if(d)return clearTimeout(c),c=setTimeout(m,t),h(r)}return null===c&&(c=setTimeout(m,t)),n};return g.cancel=()=>{null!==c&&clearTimeout(c),l=0,o=a=i=c=null},g.flush=()=>null===c?n:y(Date.now()),g}},80885:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},80931:(e,t,r)=>{e.exports=r(86006).isPlainObject},81571:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(72465),a=r(62194),i=r(14804),o=r(24517);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return i.matches(e);case"string":case"symbol":case"number":return a.property(e)}}},81682:(e,t,r)=>{e.exports=r(6706).sumBy},82384:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},82661:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function a(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function i(e,t,n,i,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new a(n,i||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,a=[];if(0===this._eventsCount)return a;for(n in e=this._events)t.call(e,n)&&a.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?a.concat(Object.getOwnPropertySymbols(e)):a},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var a=0,i=n.length,o=Array(i);a<i;a++)o[a]=n[a].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,a,i,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,s,u=this._events[l],f=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),f){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,a),!0;case 5:return u.fn.call(u.context,t,n,a,i),!0;case 6:return u.fn.call(u.context,t,n,a,i,o),!0}for(s=1,c=Array(f-1);s<f;s++)c[s-1]=arguments[s];u.fn.apply(u.context,c)}else{var d,p=u.length;for(s=0;s<p;s++)switch(u[s].once&&this.removeListener(e,u[s].fn,void 0,!0),f){case 1:u[s].fn.call(u[s].context);break;case 2:u[s].fn.call(u[s].context,t);break;case 3:u[s].fn.call(u[s].context,t,n);break;case 4:u[s].fn.call(u[s].context,t,n,a);break;default:if(!c)for(d=1,c=Array(f-1);d<f;d++)c[d-1]=arguments[d];u[s].fn.apply(u[s].context,c)}}return!0},l.prototype.on=function(e,t,r){return i(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return i(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,a){var i=r?r+e:e;if(!this._events[i])return this;if(!t)return o(this,i),this;var l=this._events[i];if(l.fn)l.fn!==t||a&&!l.once||n&&l.context!==n||o(this,i);else{for(var c=0,s=[],u=l.length;c<u;c++)(l[c].fn!==t||a&&!l[c].once||n&&l[c].context!==n)&&s.push(l[c]);s.length?this._events[i]=1===s.length?s[0]:s:o(this,i)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},82962:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(78673);t.throttle=function(e,t=0,r={}){let{leading:a=!0,trailing:i=!0}=r;return n.debounce(e,t,{leading:a,maxWait:t,trailing:i})}},83616:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},83949:(e,t,r)=>{e.exports=r(49901).range},84043:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(93639),a=r(72465),i=r(81571);t.maxBy=function(e,t){if(null!=e)return n.maxBy(Array.from(e),i.iteratee(t??a.identity))}},85252:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(22520),a=r(2767);t.isEqual=function(e,t){return n.isEqualWith(e,t,a.noop)}},86006:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},93205:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(14545),a=r(98412),i=r(50177),o=r(64072);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||i.isArguments(l))&&a.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},93639:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.maxBy=function(e,t){if(0===e.length)return;let r=e[0],n=t(r);for(let a=1;a<e.length;a++){let i=e[a],o=t(i);o>n&&(n=o,r=i)}return r}},94456:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(10921),a=r(27040),i=r(14545),o=r(46200),l=r(64072);function c(e,t){let r=n.get(e,t.slice(0,-1),e),i=t[t.length-1];if(r?.[i]===void 0)return!0;if(a.isUnsafeProperty(i))return!1;try{return delete r[i],!0}catch{return!1}}t.unset=function(e,t){if(null==e)return!0;switch(typeof t){case"symbol":case"number":case"object":if(Array.isArray(t))return c(e,t);if("number"==typeof t?t=o.toKey(t):"object"==typeof t&&(t=Object.is(t?.valueOf(),-0)?"-0":String(t)),a.isUnsafeProperty(t))return!1;if(e?.[t]===void 0)return!0;try{return delete e[t],!0}catch{return!1}case"string":if(e?.[t]===void 0&&i.isDeepKey(t))return c(e,l.toPath(t));if(a.isUnsafeProperty(t))return!1;try{return delete e[t],!0}catch{return!1}}}},95672:(e,t,r)=>{e.exports=r(10921).get},97327:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Area:()=>xj,AreaChart:()=>Pi,Bar:()=>mo,BarChart:()=>On,Brush:()=>gS,CartesianAxis:()=>bg,CartesianGrid:()=>bL,Cell:()=>pt,ComposedChart:()=>Pu,Cross:()=>aD,Curve:()=>aA,Customized:()=>pH,DefaultLegendContent:()=>eM,DefaultTooltipContent:()=>nZ,Dot:()=>p1,ErrorBar:()=>vU,Funnel:()=>PW,FunnelChart:()=>PX,Global:()=>n4,Label:()=>pI,LabelList:()=>pV,Layer:()=>q,Legend:()=>nX,Line:()=>b8,LineChart:()=>Ot,Pie:()=>yq,PieChart:()=>Of,PolarAngleAxis:()=>h0,PolarGrid:()=>hx,PolarRadiusAxis:()=>h$,Polygon:()=>pQ,Radar:()=>vx,RadarChart:()=>Pt,RadialBar:()=>m4,RadialBarChart:()=>Pc,Rectangle:()=>a7,ReferenceArea:()=>ba,ReferenceDot:()=>g6,ReferenceLine:()=>gJ,ResponsiveContainer:()=>pe,Sankey:()=>O7,Scatter:()=>x6,ScatterChart:()=>Pn,Sector:()=>ii,SunburstChart:()=>Px,Surface:()=>V,Symbols:()=>eE,Text:()=>pS,Tooltip:()=>d3,Trapezoid:()=>yg,Treemap:()=>OI,XAxis:()=>wi,YAxis:()=>wp,ZAxis:()=>xz,getNiceTickValues:()=>sl,useActiveTooltipDataPoints:()=>y0,useActiveTooltipLabel:()=>yZ,useChartHeight:()=>nj,useChartWidth:()=>nP,useOffset:()=>yJ,usePlotArea:()=>yQ});var n,a,i,o,l,c,s,u,f={};r.r(f),r.d(f,{scaleBand:()=>iv,scaleDiverging:()=>function e(){var t=oT(cX()(od));return t.copy=function(){return cU(t,e())},is.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=oK(cX()).domain([.1,1,10]);return t.copy=function(){return cU(t,e()).base(t.base())},is.apply(t,arguments)},scaleDivergingPow:()=>cH,scaleDivergingSqrt:()=>cq,scaleDivergingSymlog:()=>function e(){var t=oF(cX());return t.copy=function(){return cU(t,e()).constant(t.constant())},is.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,ou),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,ou):[0,1],oT(n)},scaleImplicit:()=>ih,scaleLinear:()=>oC,scaleLog:()=>function e(){let t=oK(om()).domain([1,10]);return t.copy=()=>ov(t,e()).base(t.base()),ic.apply(t,arguments),t},scaleOrdinal:()=>iy,scalePoint:()=>im,scalePow:()=>oH,scaleQuantile:()=>function e(){var t,r=[],n=[],a=[];function i(){var e=0,t=Math.max(1,n.length);for(a=Array(t-1);++e<t;)a[e-1]=function(e,t,r=iM){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,a=(n-1)*t,i=Math.floor(a),o=+r(e[i],i,e);return o+(r(e[i+1],i+1,e)-o)*(a-i)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[iC(a,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?a[t-1]:r[0],t<a.length?a[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(iE),i()},o.range=function(e){return arguments.length?(n=Array.from(e),i()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return a.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},ic.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,a=1,i=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[iC(i,e,0,a)]:t}function c(){var e=-1;for(i=Array(a);++e<a;)i[e]=((e+1)*n-(e-a)*r)/(a+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,c()):[r,n]},l.range=function(e){return arguments.length?(a=(o=Array.from(e)).length-1,c()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,i[0]]:t>=a?[i[a-1],n]:[i[t-1],i[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return i.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},ic.apply(oT(l),arguments)},scaleRadial:()=>function e(){var t,r=og(),n=[0,1],a=!1;function i(e){var n,i=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(i)?t:a?Math.round(i):i}return i.invert=function(e){return r.invert(oY(e))},i.domain=function(e){return arguments.length?(r.domain(e),i):r.domain()},i.range=function(e){return arguments.length?(r.range((n=Array.from(e,ou)).map(oY)),i):n.slice()},i.rangeRound=function(e){return i.range(e).round(!0)},i.round=function(e){return arguments.length?(a=!!e,i):a},i.clamp=function(e){return arguments.length?(r.clamp(e),i):r.clamp()},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e(r.domain(),n).round(a).clamp(r.clamp()).unknown(t)},ic.apply(i,arguments),oT(i)},scaleSequential:()=>function e(){var t=oT(cF()(od));return t.copy=function(){return cU(t,e())},is.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=oK(cF()).domain([1,10]);return t.copy=function(){return cU(t,e()).base(t.base())},is.apply(t,arguments)},scaleSequentialPow:()=>cW,scaleSequentialQuantile:()=>function e(){var t=[],r=od;function n(e){if(null!=e&&!isNaN(e*=1))return r((iC(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(iE),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return oZ(e);if(t>=1)return oG(e);var n,a=(n-1)*t,i=Math.floor(a),o=oG((function e(t,r,n=0,a=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),a=Math.floor(Math.min(t.length-1,a)),!(n<=r&&r<=a))return t;for(i=void 0===i?oJ:function(e=iE){if(e===iE)return oJ;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(i);a>n;){if(a-n>600){let o=a-n+1,l=r-n+1,c=Math.log(o),s=.5*Math.exp(2*c/3),u=.5*Math.sqrt(c*s*(o-s)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*s/o+u)),d=Math.min(a,Math.floor(r+(o-l)*s/o+u));e(t,r,f,d,i)}let o=t[r],l=n,c=a;for(oQ(t,n,r),i(t[a],o)>0&&oQ(t,n,a);l<c;){for(oQ(t,l,c),++l,--c;0>i(t[l],o);)++l;for(;i(t[c],o)>0;)--c}0===i(t[n],o)?oQ(t,n,c):oQ(t,++c,a),c<=r&&(n=c+1),r<=c&&(a=c-1)}return t})(e,i).subarray(0,i+1));return o+(oZ(e.subarray(i+1))-o)*(a-i)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},is.apply(n,arguments)},scaleSequentialSqrt:()=>cV,scaleSequentialSymlog:()=>function e(){var t=oF(cF());return t.copy=function(){return cU(t,e()).constant(t.constant())},is.apply(t,arguments)},scaleSqrt:()=>oq,scaleSymlog:()=>function e(){var t=oF(om());return t.copy=function(){return ov(t,e()).constant(t.constant())},ic.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],a=1;function i(e){return null!=e&&e<=e?n[iC(r,e,0,a)]:t}return i.domain=function(e){return arguments.length?(a=Math.min((r=Array.from(e)).length,n.length-1),i):r.slice()},i.range=function(e){return arguments.length?(n=Array.from(e),a=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},i.unknown=function(e){return arguments.length?(t=e,i):t},i.copy=function(){return e().domain(r).range(n).unknown(t)},ic.apply(i,arguments)},scaleTime:()=>cB,scaleUtc:()=>c$,tickFormat:()=>oM});var d=r(12115),p=r(52596),h=r(95672),y=r.n(h),v=r(53588),m=e=>0===e?0:e>0?1:-1,g=e=>"number"==typeof e&&e!=+e,b=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,x=e=>("number"==typeof e||e instanceof Number)&&!g(e),w=e=>x(e)||"string"==typeof e,O=0,P=e=>{var t=++O;return"".concat(e||"").concat(t)},j=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!x(e)&&"string"!=typeof e)return n;if(b(e)){if(null==t)return n;var i=e.indexOf("%");r=t*parseFloat(e.slice(0,i))/100}else r=+e;return g(r)&&(r=n),a&&null!=t&&r>t&&(r=t),r},E=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},S=(e,t)=>x(e)&&x(t)?r=>e+r*(t-e):()=>t;function k(e,t,r){return x(e)&&x(t)?e+r*(t-e):t}function A(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):y()(e,t))===r)}var M=e=>null==e?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1)),T=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],C=["points","pathLength"],D={svg:["viewBox","children"],polygon:C,polyline:C},N=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],I=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,d.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var n={};return Object.keys(r).forEach(e=>{N.includes(e)&&(n[e]=t||(t=>r[e](r,t)))}),n},_=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(a=>{var i=e[a];N.includes(a)&&"function"==typeof i&&(n||(n={}),n[a]=e=>(i(t,r,e),null))}),n},L=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",R=null,z=null,K=e=>{if(e===R&&Array.isArray(z))return z;var t=[];return d.Children.forEach(e,e=>{null==e||((0,v.zv)(e)?t=t.concat(K(e.props.children)):t.push(e))}),z=t,R=e,t};function B(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>L(e)):[L(t)],K(e).forEach(e=>{var t=y()(e,"type.displayName")||y()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var $=e=>!e||"object"!=typeof e||!("clipDot"in e)||!!e.clipDot,F=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,d.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var a={};return Object.keys(n).forEach(e=>{var i;((e,t,r,n)=>{var a,i=null!=(a=n&&(null==D?void 0:D[n]))?a:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&i.includes(t)||T.includes(t))||r&&N.includes(t)})(null==(i=n)?void 0:i[e],e,t,r)&&(a[e]=n[e])}),a},U=["children","width","height","viewBox","className","style","title","desc"];function W(){return(W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var V=(0,d.forwardRef)((e,t)=>{var{children:r,width:n,height:a,viewBox:i,className:o,style:l,title:c,desc:s}=e,u=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,U),f=i||{width:n,height:a,x:0,y:0},h=(0,p.$)("recharts-surface",o);return d.createElement("svg",W({},F(u,!0,"svg"),{className:h,width:n,height:a,style:l,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height),ref:t}),d.createElement("title",null,c),d.createElement("desc",null,s),r)}),X=["children","className"];function H(){return(H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var q=d.forwardRef((e,t)=>{var{children:r,className:n}=e,a=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,X),i=(0,p.$)("recharts-layer",n);return d.createElement("g",H({className:i},F(a,!0),{ref:t}),r)}),Y=r(47650),G=(0,d.createContext)(null);let Z=Math.cos,J=Math.sin,Q=Math.sqrt,ee=Math.PI,et=2*ee,er={draw(e,t){let r=Q(t/ee);e.moveTo(r,0),e.arc(0,0,r,0,et)}},en=Q(1/3),ea=2*en,ei=J(ee/10)/J(7*ee/10),eo=J(et/10)*ei,el=-Z(et/10)*ei,ec=Q(3),es=Q(3)/2,eu=1/Q(12),ef=(eu/2+1)*3;function ed(e){return function(){return e}}let ep=Math.PI,eh=2*ep,ey=eh-1e-6;function ev(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class em{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?ev:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return ev;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,a,i){this._append`C${+e},${+t},${+r},${+n},${this._x1=+a},${this._y1=+i}`}arcTo(e,t,r,n,a){if(e*=1,t*=1,r*=1,n*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let i=this._x1,o=this._y1,l=r-e,c=n-t,s=i-e,u=o-t,f=s*s+u*u;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(f>1e-6)if(Math.abs(u*l-c*s)>1e-6&&a){let d=r-i,p=n-o,h=l*l+c*c,y=Math.sqrt(h),v=Math.sqrt(f),m=a*Math.tan((ep-Math.acos((h+f-(d*d+p*p))/(2*y*v)))/2),g=m/v,b=m/y;Math.abs(g-1)>1e-6&&this._append`L${e+g*s},${t+g*u}`,this._append`A${a},${a},0,0,${+(u*d>s*p)},${this._x1=e+b*l},${this._y1=t+b*c}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,n,a,i){if(e*=1,t*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let o=r*Math.cos(n),l=r*Math.sin(n),c=e+o,s=t+l,u=1^i,f=i?n-a:a-n;null===this._x1?this._append`M${c},${s}`:(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-s)>1e-6)&&this._append`L${c},${s}`,r&&(f<0&&(f=f%eh+eh),f>ey?this._append`A${r},${r},0,1,${u},${e-o},${t-l}A${r},${r},0,1,${u},${this._x1=c},${this._y1=s}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=ep)},${u},${this._x1=e+r*Math.cos(a)},${this._y1=t+r*Math.sin(a)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eg(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new em(t)}em.prototype,Q(3),Q(3);var eb=["type","size","sizeType"];function ex(){return(ex=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ew(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eO(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ew(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ew(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var eP={symbolCircle:er,symbolCross:{draw(e,t){let r=Q(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=Q(t/ea),n=r*en;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=Q(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=Q(.8908130915292852*t),n=eo*r,a=el*r;e.moveTo(0,-r),e.lineTo(n,a);for(let t=1;t<5;++t){let i=et*t/5,o=Z(i),l=J(i);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*a,l*n+o*a)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-Q(t/(3*ec));e.moveTo(0,2*r),e.lineTo(-ec*r,-r),e.lineTo(ec*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=Q(t/ef),n=r/2,a=r*eu,i=r*eu+r,o=-n;e.moveTo(n,a),e.lineTo(n,i),e.lineTo(o,i),e.lineTo(-.5*n-es*a,es*n+-.5*a),e.lineTo(-.5*n-es*i,es*n+-.5*i),e.lineTo(-.5*o-es*i,es*o+-.5*i),e.lineTo(-.5*n+es*a,-.5*a-es*n),e.lineTo(-.5*n+es*i,-.5*i-es*n),e.lineTo(-.5*o+es*i,-.5*i-es*o),e.closePath()}}},ej=Math.PI/180,eE=e=>{var{type:t="circle",size:r=64,sizeType:n="area"}=e,a=eO(eO({},function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,eb)),{},{type:t,size:r,sizeType:n}),{className:i,cx:o,cy:l}=a,c=F(a,!0);return o===+o&&l===+l&&r===+r?d.createElement("path",ex({},c,{className:(0,p.$)("recharts-symbols",i),transform:"translate(".concat(o,", ").concat(l,")"),d:(()=>{var e=eP["symbol".concat(M(t))]||er;return(function(e,t){let r=null,n=eg(a);function a(){let a;if(r||(r=a=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),a)return r=null,a+""||null}return e="function"==typeof e?e:ed(e||er),t="function"==typeof t?t:ed(void 0===t?64:+t),a.type=function(t){return arguments.length?(e="function"==typeof t?t:ed(t),a):e},a.size=function(e){return arguments.length?(t="function"==typeof e?e:ed(+e),a):t},a.context=function(e){return arguments.length?(r=null==e?null:e,a):r},a})().type(e).size(((e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*ej;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}})(r,n,t))()})()})):null};function eS(){return(eS=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ek(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eA(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}eE.registerSymbol=(e,t)=>{eP["symbol".concat(M(e))]=t};class eM extends d.PureComponent{renderIcon(e,t){var{inactiveColor:r}=this.props,n=32/6,a=32/3,i=e.inactive?r:e.color,o=null!=t?t:e.type;if("none"===o)return null;if("plainline"===o)return d.createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:e.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===o)return d.createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(a,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(2*a,",").concat(16,"\n            H").concat(32,"M").concat(2*a,",").concat(16,"\n            A").concat(n,",").concat(n,",0,1,1,").concat(a,",").concat(16),className:"recharts-legend-icon"});if("rect"===o)return d.createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(d.isValidElement(e.legendIcon)){var l=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ek(Object(r),!0).forEach(function(t){eA(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ek(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return delete l.legendIcon,d.cloneElement(e.legendIcon,l)}return d.createElement(eE,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:o})}renderItems(){var{payload:e,iconSize:t,layout:r,formatter:n,inactiveColor:a,iconType:i}=this.props,o={x:0,y:0,width:32,height:32},l={display:"horizontal"===r?"inline-block":"block",marginRight:10},c={display:"inline-block",verticalAlign:"middle",marginRight:4};return e.map((e,r)=>{var s=e.formatter||n,u=(0,p.$)({"recharts-legend-item":!0,["legend-item-".concat(r)]:!0,inactive:e.inactive});if("none"===e.type)return null;var f=e.inactive?a:e.color,h=s?s(e.value,e,r):e.value;return d.createElement("li",eS({className:u,style:l,key:"legend-item-".concat(r)},_(this.props,e,r)),d.createElement(V,{width:t,height:t,viewBox:o,style:c,"aria-label":"".concat(h," legend icon")},this.renderIcon(e,i)),d.createElement("span",{className:"recharts-legend-item-text",style:{color:f}},h))})}render(){var{payload:e,layout:t,align:r}=this.props;return e&&e.length?d.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===t?r:"left"}},this.renderItems()):null}}eA(eM,"displayName","Legend"),eA(eM,"defaultProps",{align:"center",iconSize:14,inactiveColor:"#ccc",layout:"horizontal",verticalAlign:"middle"});var eT=r(60512),eC=r.n(eT);function eD(e,t,r){return!0===t?eC()(e,r):"function"==typeof t?eC()(e,t):e}var eN=r(45643),eI=(0,d.createContext)(null),e_=e=>e,eL=()=>{var e=(0,d.useContext)(eI);return e?e.store.dispatch:e_},eR=()=>{},ez=()=>eR,eK=(e,t)=>e===t;function eB(e){var t=(0,d.useContext)(eI);return(0,eN.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:ez,t?t.store.getState:eR,t?t.store.getState:eR,t?e:eR,eK)}var e$=e=>Array.isArray(e)?e:[e],eF=0,eU=class{revision=eF;_value;_lastValue;_isEqual=eW;constructor(e,t=eW){this._value=this._lastValue=e,this._isEqual=t}get value(){return this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++eF)}};function eW(e,t){return e===t}function eV(e){return e instanceof eU||console.warn("Not a valid cell! ",e),e.value}var eX=(e,t)=>!1;function eH(){return function(e,t=eW){return new eU(null,t)}(0,eX)}var eq=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=eH()),eV(t)};Symbol();var eY=0,eG=Object.getPrototypeOf({}),eZ=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,eJ);tag=eH();tags={};children={};collectionTag=null;id=eY++},eJ={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in eG)return n;if("object"==typeof n&&null!==n){var a;let r=e.children[t];return void 0===r&&(r=e.children[t]=Array.isArray(a=n)?new eQ(a):new eZ(a)),r.tag&&eV(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=eH()).value=n),eV(r),n}})(),ownKeys:e=>(eq(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},eQ=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],e0);tag=eH();tags={};children={};collectionTag=null;id=eY++},e0={get:([e],t)=>("length"===t&&eq(e),eJ.get(e,t)),ownKeys:([e])=>eJ.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>eJ.getOwnPropertyDescriptor(e,t),has:([e],t)=>eJ.has(e,t)},e1="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function e2(){return{s:0,v:void 0,o:null,p:null}}function e5(e,t={}){let r,n=e2(),{resultEqualityCheck:a}=t,i=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=e2(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=e2(),e.set(t,o)):o=r}}let c=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),i++,a){let e=r?.deref?.()??r;null!=e&&a(e,t)&&(t=e,0!==i&&i--),r="object"==typeof t&&null!==t||"function"==typeof t?new e1(t):t}return c.s=1,c.v=t,t}return o.clearCache=()=>{n=e2(),o.resetResultsCount()},o.resultsCount=()=>i,o.resetResultsCount=()=>{i=0},o}var e3=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,a=0,i={},o=e.pop();"object"==typeof o&&(i=o,o=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(o,`createSelector expects an output function after the inputs, but received: [${typeof o}]`);let{memoize:l,memoizeOptions:c=[],argsMemoize:s=e5,argsMemoizeOptions:u=[],devModeChecks:f={}}={...r,...i},d=e$(c),p=e$(u),h=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),y=l(function(){return n++,o.apply(null,arguments)},...d);return Object.assign(s(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let a=0;a<n;a++)r.push(e[a].apply(null,t));return r}(h,arguments);return t=y.apply(null,e)},...p),{resultFunc:o,memoizedResultFunc:y,dependencies:h,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:l,argsMemoize:s})};return Object.assign(n,{withTypes:()=>n}),n}(e5),e4=Object.assign((e,t=e3)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>e4}),e6=r(20241),e8=r.n(e6),e7=e=>e.legend.settings,e9=e3([e=>e.legend.payload,e7],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?e8()(n,r):n});function te(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,d.useState)({height:0,left:0,top:0,width:0}),n=(0,d.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),a={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(a.height-t.height)>1||Math.abs(a.left-t.left)>1||Math.abs(a.top-t.top)>1||Math.abs(a.width-t.width)>1)&&r({height:a.height,left:a.left,top:a.top,width:a.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,n]}function tt(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var tr="function"==typeof Symbol&&Symbol.observable||"@@observable",tn=()=>Math.random().toString(36).substring(7).split("").join("."),ta={INIT:`@@redux/INIT${tn()}`,REPLACE:`@@redux/REPLACE${tn()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${tn()}`};function ti(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function to(e){let t,r=Object.keys(e),n={};for(let t=0;t<r.length;t++){let a=r[t];"function"==typeof e[a]&&(n[a]=e[a])}let a=Object.keys(n);try{Object.keys(n).forEach(e=>{let t=n[e];if(void 0===t(void 0,{type:ta.INIT}))throw Error(tt(12));if(void 0===t(void 0,{type:ta.PROBE_UNKNOWN_ACTION()}))throw Error(tt(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let i=!1,o={};for(let t=0;t<a.length;t++){let l=a[t],c=n[l],s=e[l],u=c(s,r);if(void 0===u)throw r&&r.type,Error(tt(14));o[l]=u,i=i||u!==s}return(i=i||a.length!==Object.keys(e).length)?o:e}}function tl(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function tc(e){return ti(e)&&"type"in e&&"string"==typeof e.type}function ts(e){return({dispatch:t,getState:r})=>n=>a=>"function"==typeof a?a(t,r,e):n(a)}var tu=ts(),tf=Symbol.for("immer-nothing"),td=Symbol.for("immer-draftable"),tp=Symbol.for("immer-state");function th(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var ty=Object.getPrototypeOf;function tv(e){return!!e&&!!e[tp]}function tm(e){return!!e&&(tb(e)||Array.isArray(e)||!!e[td]||!!e.constructor?.[td]||tj(e)||tE(e))}var tg=Object.prototype.constructor.toString();function tb(e){if(!e||"object"!=typeof e)return!1;let t=ty(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===tg}function tx(e,t){0===tw(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function tw(e){let t=e[tp];return t?t.type_:Array.isArray(e)?1:tj(e)?2:3*!!tE(e)}function tO(e,t){return 2===tw(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function tP(e,t,r){let n=tw(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function tj(e){return e instanceof Map}function tE(e){return e instanceof Set}function tS(e){return e.copy_||e.base_}function tk(e,t){if(tj(e))return new Map(e);if(tE(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=tb(e);if(!0!==t&&("class_only"!==t||r)){let t=ty(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[tp];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let a=r[n],i=t[a];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[a]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[a]})}return Object.create(ty(e),t)}}function tA(e,t=!1){return tT(e)||tv(e)||!tm(e)||(tw(e)>1&&(e.set=e.add=e.clear=e.delete=tM),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>tA(t,!0))),e}function tM(){th(2)}function tT(e){return Object.isFrozen(e)}var tC={};function tD(e){let t=tC[e];return t||th(0,e),t}function tN(e,t){t&&(tD("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function tI(e){t_(e),e.drafts_.forEach(tR),e.drafts_=null}function t_(e){e===n&&(n=e.parent_)}function tL(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function tR(e){let t=e[tp];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function tz(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[tp].modified_&&(tI(t),th(4)),tm(e)&&(e=tK(t,e),t.parent_||t$(t,e)),t.patches_&&tD("Patches").generateReplacementPatches_(r[tp].base_,e,t.patches_,t.inversePatches_)):e=tK(t,r,[]),tI(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==tf?e:void 0}function tK(e,t,r){if(tT(t))return t;let n=t[tp];if(!n)return tx(t,(a,i)=>tB(e,n,t,a,i,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return t$(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,a=t,i=!1;3===n.type_&&(a=new Set(t),t.clear(),i=!0),tx(a,(a,o)=>tB(e,n,t,a,o,r,i)),t$(e,t,!1),r&&e.patches_&&tD("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function tB(e,t,r,n,a,i,o){if(tv(a)){let o=tK(e,a,i&&t&&3!==t.type_&&!tO(t.assigned_,n)?i.concat(n):void 0);if(tP(r,n,o),!tv(o))return;e.canAutoFreeze_=!1}else o&&r.add(a);if(tm(a)&&!tT(a)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;tK(e,a),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&t$(e,a)}}function t$(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&tA(t,r)}var tF={get(e,t){if(t===tp)return e;let r=tS(e);if(!tO(r,t)){var n=e,a=r,i=t;let o=tV(a,i);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let o=r[t];return e.finalized_||!tm(o)?o:o===tW(e.base_,t)?(tH(e),e.copy_[t]=tq(o,e)):o},has:(e,t)=>t in tS(e),ownKeys:e=>Reflect.ownKeys(tS(e)),set(e,t,r){let n=tV(tS(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=tW(tS(e),t),a=n?.[tp];if(a&&a.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||tO(e.base_,t)))return!0;tH(e),tX(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==tW(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,tH(e),tX(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=tS(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){th(11)},getPrototypeOf:e=>ty(e.base_),setPrototypeOf(){th(12)}},tU={};function tW(e,t){let r=e[tp];return(r?tS(r):e)[t]}function tV(e,t){if(!(t in e))return;let r=ty(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=ty(r)}}function tX(e){!e.modified_&&(e.modified_=!0,e.parent_&&tX(e.parent_))}function tH(e){e.copy_||(e.copy_=tk(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function tq(e,t){let r=tj(e)?tD("MapSet").proxyMap_(e,t):tE(e)?tD("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),a={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=a,o=tF;r&&(i=[a],o=tU);let{revoke:l,proxy:c}=Proxy.revocable(i,o);return a.draft_=c,a.revoke_=l,c}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function tY(e){return tv(e)||th(10,e),function e(t){let r;if(!tm(t)||tT(t))return t;let n=t[tp];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=tk(t,n.scope_.immer_.useStrictShallowCopy_)}else r=tk(t,!0);return tx(r,(t,n)=>{tP(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}tx(tF,(e,t)=>{tU[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),tU.deleteProperty=function(e,t){return tU.set.call(this,e,t,void 0)},tU.set=function(e,t,r){return tF.set.call(this,e[0],t,r,e[0])};var tG=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...a){return n.produce(e,e=>t.call(this,e,...a))}}if("function"!=typeof t&&th(6),void 0!==r&&"function"!=typeof r&&th(7),tm(e)){let a=tL(this),i=tq(e,void 0),o=!0;try{n=t(i),o=!1}finally{o?tI(a):t_(a)}return tN(a,r),tz(n,a)}if(e&&"object"==typeof e)th(1,e);else{if(void 0===(n=t(e))&&(n=e),n===tf&&(n=void 0),this.autoFreeze_&&tA(n,!0),r){let t=[],a=[];tD("Patches").generateReplacementPatches_(e,n,t,a),r(t,a)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){tm(e)||th(8),tv(e)&&(e=tY(e));let t=tL(this),r=tq(e,void 0);return r[tp].isManual_=!0,t_(t),r}finishDraft(e,t){let r=e&&e[tp];r&&r.isManual_||th(9);let{scope_:n}=r;return tN(n,t),tz(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=tD("Patches").applyPatches_;return tv(e)?n(e,t):this.produce(e,e=>n(e,t))}},tZ=tG.produce;tG.produceWithPatches.bind(tG),tG.setAutoFreeze.bind(tG),tG.setUseStrictShallowCopy.bind(tG),tG.applyPatches.bind(tG),tG.createDraft.bind(tG),tG.finishDraft.bind(tG),r(49509);var tJ="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?tl:tl.apply(null,arguments)};function tQ(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(rT(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>tc(t)&&t.type===e,r}"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var t0=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function t1(e){return tm(e)?tZ(e,()=>{}):e}function t2(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var t5=e=>t=>{setTimeout(t,e)};function t3(e){let t,r={},n=[],a={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(rT(28));if(n in r)throw Error(rT(29));return r[n]=t,a},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),a),addDefaultCase:e=>(t=e,a)};return e(a),[r,n,t]}var t4=Symbol.for("rtk-slice-createasyncthunk"),t6=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(t6||{}),t8=function({creators:e}={}){let t=e?.asyncThunk?.[t4];return function(e){let r,{name:n,reducerPath:a=n}=e;if(!n)throw Error(rT(11));let i=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},o=Object.keys(i),l={},c={},s={},u=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(rT(12));if(r in c)throw Error(rT(13));return c[r]=t,f},addMatcher:(e,t)=>(u.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(s[e]=t,f),exposeCaseReducer:(e,t)=>(l[e]=t,f)};function d(){let[t={},r=[],n]="function"==typeof e.extraReducers?t3(e.extraReducers):[e.extraReducers],a={...t,...c};return function(e,t){let r,[n,a,i]=t3(t);if("function"==typeof e)r=()=>t1(e());else{let t=t1(e);r=()=>t}function o(e=r(),t){let l=[n[t.type],...a.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===l.filter(e=>!!e).length&&(l=[i]),l.reduce((e,r)=>{if(r)if(tv(e)){let n=r(e,t);return void 0===n?e:n}else{if(tm(e))return tZ(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return o.getInitialState=r,o}(e.initialState,e=>{for(let t in a)e.addCase(t,a[t]);for(let t of u)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}o.forEach(r=>{let a=i[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===a._reducerDefinitionType?function({type:e,reducerName:t},r,n,a){if(!a)throw Error(rT(18));let{payloadCreator:i,fulfilled:o,pending:l,rejected:c,settled:s,options:u}=r,f=a(e,i,u);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),c&&n.addCase(f.rejected,c),s&&n.addMatcher(f.settled,s),n.exposeCaseReducer(t,{fulfilled:o||t7,pending:l||t7,rejected:c||t7,settled:s||t7})}(o,a,f,t):function({type:e,reducerName:t,createNotation:r},n,a){let i,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(rT(17));i=n.reducer,o=n.prepare}else i=n;a.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,o?tQ(e,o):tQ(e))}(o,a,f)});let p=e=>e,h=new Map,y=new WeakMap;function v(e,t){return r||(r=d()),r(e,t)}function m(){return r||(r=d()),r.getInitialState()}function g(t,r=!1){function n(e){let a=e[t];return void 0===a&&r&&(a=t2(y,n,m)),a}function a(t=p){let n=t2(h,r,()=>new WeakMap);return t2(n,t,()=>{let n={};for(let[a,i]of Object.entries(e.selectors??{}))n[a]=function(e,t,r,n){function a(i,...o){let l=t(i);return void 0===l&&n&&(l=r()),e(l,...o)}return a.unwrapped=e,a}(i,t,()=>t2(y,t,m),r);return n})}return{reducerPath:t,getSelectors:a,get selectors(){return a(n)},selectSlice:n}}let b={name:n,reducer:v,actions:s,caseReducers:l,getInitialState:m,...g(a),injectInto(e,{reducerPath:t,...r}={}){let n=t??a;return e.inject({reducerPath:n,reducer:v},r),{...b,...g(n,!0)}}};return b}}();function t7(){}var t9="listener",re="completed",rt="cancelled",rr=`task-${rt}`,rn=`task-${re}`,ra=`${t9}-${rt}`,ri=`${t9}-${re}`,ro=class{constructor(e){this.code=e,this.message=`task ${rt} (reason: ${e})`}name="TaskAbortError";message},rl=(e,t)=>{if("function"!=typeof e)throw TypeError(rT(32))},rc=()=>{},rs=(e,t=rc)=>(e.catch(t),e),ru=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),rf=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},rd=e=>{if(e.aborted){let{reason:t}=e;throw new ro(t)}};function rp(e,t){let r=rc;return new Promise((n,a)=>{let i=()=>a(new ro(e.reason));if(e.aborted)return void i();r=ru(e,i),t.finally(()=>r()).then(n,a)}).finally(()=>{r=rc})}var rh=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof ro?"cancelled":"rejected",error:e}}finally{t?.()}},ry=e=>t=>rs(rp(e,t).then(t=>(rd(e),t))),rv=e=>{let t=ry(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:rm}=Object,rg={},rb="listenerMiddleware",rx=e=>{let{type:t,actionCreator:r,matcher:n,predicate:a,effect:i}=e;if(t)a=tQ(t).match;else if(r)t=r.type,a=r.match;else if(n)a=n;else if(a);else throw Error(rT(21));return rl(i,"options.listener"),{predicate:a,type:t,effect:i}},rw=rm(e=>{let{type:t,predicate:r,effect:n}=rx(e);return{id:((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(rT(22))}}},{withTypes:()=>rw}),rO=(e,t)=>{let{type:r,effect:n,predicate:a}=rx(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===a)&&e.effect===n)},rP=e=>{e.pending.forEach(e=>{rf(e,ra)})},rj=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},rE=rm(tQ(`${rb}/add`),{withTypes:()=>rE}),rS=tQ(`${rb}/removeAll`),rk=rm(tQ(`${rb}/remove`),{withTypes:()=>rk}),rA=(...e)=>{console.error(`${rb}/error`,...e)},rM=(e={})=>{let t=new Map,{extra:r,onError:n=rA}=e;rl(n,"onError");let a=e=>(e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&rP(e)}))(rO(t,e)??rw(e));rm(a,{withTypes:()=>a});let i=e=>{let r=rO(t,e);return r&&(r.unsubscribe(),e.cancelActive&&rP(r)),!!r};rm(i,{withTypes:()=>i});let o=async(e,i,o,l)=>{let c=new AbortController,s=((e,t)=>{let r=async(r,n)=>{rd(t);let a=()=>{},i=[new Promise((t,n)=>{let i=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});a=()=>{i(),n()}})];null!=n&&i.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await rp(t,Promise.race(i));return rd(t),e}finally{a()}};return(e,t)=>rs(r(e,t))})(a,c.signal),u=[];try{e.pending.add(c),await Promise.resolve(e.effect(i,rm({},o,{getOriginalState:l,condition:(e,t)=>s(e,t).then(Boolean),take:s,delay:rv(c.signal),pause:ry(c.signal),extra:r,signal:c.signal,fork:((e,t)=>(r,n)=>{rl(r,"taskExecutor");let a=new AbortController;ru(e,()=>rf(a,e.reason));let i=rh(async()=>{rd(e),rd(a.signal);let t=await r({pause:ry(a.signal),delay:rv(a.signal),signal:a.signal});return rd(a.signal),t},()=>rf(a,rn));return n?.autoJoin&&t.push(i.catch(rc)),{result:ry(e)(i),cancel(){rf(a,rr)}}})(c.signal,u),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==c&&(rf(e,ra),r.delete(e))})},cancel:()=>{rf(c,ra),e.pending.delete(c)},throwIfCancelled:()=>{rd(c.signal)}})))}catch(e){e instanceof ro||rj(n,e,{raisedBy:"effect"})}finally{await Promise.all(u),rf(c,ri),e.pending.delete(c)}},l=(e=>()=>{e.forEach(rP),e.clear()})(t);return{middleware:e=>r=>c=>{let s;if(!tc(c))return r(c);if(rE.match(c))return a(c.payload);if(rS.match(c))return void l();if(rk.match(c))return i(c.payload);let u=e.getState(),f=()=>{if(u===rg)throw Error(rT(23));return u};try{if(s=r(c),t.size>0){let r=e.getState();for(let a of Array.from(t.values())){let t=!1;try{t=a.predicate(c,r,u)}catch(e){t=!1,rj(n,e,{raisedBy:"predicate"})}t&&o(a,c,e,f)}}}finally{u=rg}return s},startListening:a,stopListening:i,clearListeners:l}};function rT(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}Symbol.for("rtk-state-proxy-original");var rC=t8({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:rD,setLayout:rN,setChartSize:rI,setScale:r_}=rC.actions,rL=rC.reducer;function rR(e,t){if((a=e.length)>1)for(var r,n,a,i=1,o=e[t[0]],l=o.length;i<a;++i)for(n=o,o=e[t[i]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function rz(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function rK(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function rB(e,t){return e[t]}function r$(e){let t=[];return t.key=e,t}function rF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rF(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}Array.prototype.slice;var rW=Math.PI/180,rV=(e,t,r,n)=>({x:e+Math.cos(-rW*n)*r,y:t+Math.sin(-rW*n)*r}),rX=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},rH=e=>(0,d.isValidElement)(e)||"function"==typeof e||"boolean"==typeof e||null==e?"":e.className;function rq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function rY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?rq(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):rq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function rG(e,t,r){return null==e||null==t?r:w(t)?y()(e,t,r):"function"==typeof t?t(e):r}var rZ=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,rJ=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var a,i,o=e.map(e=>(e.coordinate===t&&(a=!0),e.coordinate===r&&(i=!0),e.coordinate));return a||o.push(t),i||o.push(r),o},rQ=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:a,range:i,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:s,tickCount:u,ticks:f,niceTicks:d,axisType:p}=e;if(!o)return null;var h="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,y=(t||r)&&"category"===a&&o.bandwidth?o.bandwidth()/h:0;return(y="angleAxis"===p&&i&&i.length>=2?2*m(i[0]-i[1])*y:y,t&&(f||d))?(f||d||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+y,value:e,offset:y,index:t})).filter(e=>!g(e.coordinate)):c&&s?s.map((e,t)=>({coordinate:o(e)+y,value:e,index:t,offset:y})):o.ticks&&!r&&null!=u?o.ticks(u).map((e,t)=>({coordinate:o(e)+y,value:e,offset:y,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+y,value:n?n[e]:e,index:t,offset:y}))},r0=(e,t)=>{if(!t||2!==t.length||!x(t[0])||!x(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),a=[e[0],e[1]];return(!x(e[0])||e[0]<r)&&(a[0]=r),(!x(e[1])||e[1]>n)&&(a[1]=n),a[0]>n&&(a[0]=n),a[1]<r&&(a[1]=r),a},r1={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var a=0,i=0,o=0;o<t;++o){var l=g(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1]):(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,a,i=0,o=e[0].length;i<o;++i){for(a=r=0;r<n;++r)a+=e[r][i][1]||0;if(a)for(r=0;r<n;++r)e[r][i][1]/=a}rR(e,t)}},none:rR,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,a=e[t[0]],i=a.length;n<i;++n){for(var o=0,l=0;o<r;++o)l+=e[o][n][1]||0;a[n][1]+=a[n][0]=-l/2}rR(e,t)}},wiggle:function(e,t){if((a=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,a,i=0,o=1;o<n;++o){for(var l=0,c=0,s=0;l<a;++l){for(var u=e[t[l]],f=u[o][1]||0,d=(f-(u[o-1][1]||0))/2,p=0;p<l;++p){var h=e[t[p]];d+=(h[o][1]||0)-(h[o-1][1]||0)}c+=f,s+=d*f}r[o-1][1]+=r[o-1][0]=i,c&&(i-=s/c)}r[o-1][1]+=r[o-1][0]=i,rR(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var a=0,i=0;i<t;++i){var o=g(e[i][r][1])?e[i][r][0]:e[i][r][1];o>=0?(e[i][r][0]=a,e[i][r][1]=a+o,a=e[i][r][1]):(e[i][r][0]=0,e[i][r][1]=0)}}};function r2(e){return null==e?void 0:String(e)}function r5(e){var{axis:t,ticks:r,bandSize:n,entry:a,index:i,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&null!=a[t.dataKey]){var l=A(r,"value",a[t.dataKey]);if(l)return l.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var c=rG(a,null==o?t.dataKey:o);return null==c?null:t.scale(c)}var r3=e=>{var{axis:t,ticks:r,offset:n,bandSize:a,entry:i,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=rG(i,t.dataKey,t.scale.domain()[o]);return null==l?null:t.scale(l)-a/2+n},r4=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),a=Math.max(r[0],r[1]);return n<=0&&a>=0?0:a<0?a:n}return r[0]},r6=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,r8=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,r7=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=e8()(t,e=>e.coordinate),i=1/0,o=1,l=a.length;o<l;o++){var c=a[o],s=a[o-1];i=Math.min((c.coordinate||0)-(s.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0};function r9(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:a,name:i}=e;return rY(rY({},t),{},{dataKey:r,payload:n,value:a,name:i})}function ne(e,t){return e?String(e):"string"==typeof t?t:void 0}var nt=e=>e.layout.width,nr=e=>e.layout.height,nn=e=>e.layout.scale,na=e=>e.layout.margin,ni=e3(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),no=e3(e=>e.cartesianAxis.yAxis,e=>Object.values(e)),nl=["#1890FF","#66B5FF","#41D9C7","#2FC25B","#6EDB8F","#9AE65C","#FACC14","#E6965C","#57AD71","#223273","#738AE6","#7564CC","#8543E0","#A877ED","#5C8EE6","#13C2C2","#70E0E0","#5CA3E6","#3436C7","#8082FF","#DD81E6","#F04864","#FA7D92","#D598D9"],nc="data-recharts-item-index",ns="data-recharts-item-data-key";function nu(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nf(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nu(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nu(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var nd=e3([nt,nr,na,e=>e.brush.height,ni,no,e7,e=>e.legend.size],(e,t,r,n,a,i,o,l)=>{var c=i.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:60;return nf(nf({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),s=a.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:nf(nf({},e),{},{[r]:y()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),u=nf(nf({},s),c),f=u.bottom;u.bottom+=n;var d=e-(u=((e,t,r)=>{if(t&&r){var{width:n,height:a}=r,{align:i,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==i&&x(e[i]))return rY(rY({},e),{},{[i]:e[i]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===i)&&"middle"!==o&&x(e[o]))return rY(rY({},e),{},{[o]:e[o]+(a||0)})}return e})(u,o,l)).left-u.right,p=t-u.top-u.bottom;return nf(nf({brushBottom:f},u),{},{width:Math.max(d,0),height:Math.max(p,0)})}),np=e3(nd,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),nh=e3(nt,nr,(e,t)=>({x:0,y:0,width:e,height:t})),ny=(0,d.createContext)(null),nv=()=>null!=(0,d.useContext)(ny),nm=e=>{var{children:t}=e;return d.createElement(ny.Provider,{value:!0},t)},ng=e=>e.brush,nb=e3([ng,nd,na],(e,t,r)=>({height:e.height,x:x(e.x)?e.x:t.left,y:x(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:x(e.width)?e.width:t.width})),nx=()=>{var e,t=nv(),r=eB(np),n=eB(nb),a=null==(e=eB(ng))?void 0:e.padding;return t&&n&&a?{width:n.width-a.left-a.right,height:n.height-a.top-a.bottom,x:a.left,y:a.top}:r},nw={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},nO=()=>{var e;return null!=(e=eB(nd))?e:nw},nP=()=>eB(nt),nj=()=>eB(nr),nE={top:0,right:0,bottom:0,left:0},nS=e=>e.layout.layoutType,nk=()=>eB(nS),nA=e=>{var t=eL();return(0,d.useEffect)(()=>{t(rI(e))},[t,e]),null},nM=e=>{var{margin:t}=e,r=eL();return(0,d.useEffect)(()=>{r(rD(t))},[r,t]),null},nT=t8({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push(t.payload)},removeLegendPayload(e,t){var r=tY(e).payload.indexOf(t.payload);r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:nC,setLegendSettings:nD,addLegendPayload:nN,removeLegendPayload:nI}=nT.actions,n_=nT.reducer,nL=["contextPayload"];function nR(){return(nR=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function nz(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nz(Object(r),!0).forEach(function(t){nB(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nz(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nB(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function n$(e){return e.value}function nF(e){var{contextPayload:t}=e,r=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,nL),n=eD(t,e.payloadUniqBy,n$),a=nK(nK({},r),{},{payload:n});return d.isValidElement(e.content)?d.cloneElement(e.content,a):"function"==typeof e.content?d.createElement(e.content,a):d.createElement(eM,a)}function nU(e){var t=eL();return(0,d.useEffect)(()=>{t(nD(e))},[t,e]),null}function nW(e){var t=eL();return(0,d.useEffect)(()=>(t(nC(e)),()=>{t(nC({width:0,height:0}))}),[t,e]),null}function nV(e){var t,r=eB(e9),n=(0,d.useContext)(G),a=null!=(t=eB(e=>e.layout.margin))?t:nE,{width:i,height:o,wrapperStyle:l,portal:c}=e,[s,u]=te([r]),f=nP(),p=nj(),h=f-(a.left||0)-(a.right||0),y=nX.getWidthOrHeight(e.layout,o,i,h),v=c?l:nK(nK({position:"absolute",width:(null==y?void 0:y.width)||i||"auto",height:(null==y?void 0:y.height)||o||"auto"},function(e,t,r,n,a,i){var o,l,{layout:c,align:s,verticalAlign:u}=t;return e&&(void 0!==e.left&&null!==e.left||void 0!==e.right&&null!==e.right)||(o="center"===s&&"vertical"===c?{left:((n||0)-i.width)/2}:"right"===s?{right:r&&r.right||0}:{left:r&&r.left||0}),e&&(void 0!==e.top&&null!==e.top||void 0!==e.bottom&&null!==e.bottom)||(l="middle"===u?{top:((a||0)-i.height)/2}:"bottom"===u?{bottom:r&&r.bottom||0}:{top:r&&r.top||0}),nK(nK({},o),l)}(l,e,a,f,p,s)),l),m=null!=c?c:n;if(null==m)return null;var g=d.createElement("div",{className:"recharts-legend-wrapper",style:v,ref:u},d.createElement(nU,{layout:e.layout,align:e.align,verticalAlign:e.verticalAlign,itemSorter:e.itemSorter}),d.createElement(nW,{width:s.width,height:s.height}),d.createElement(nF,nR({},e,y,{margin:a,chartWidth:f,chartHeight:p,contextPayload:r})));return(0,Y.createPortal)(g,m)}class nX extends d.PureComponent{static getWidthOrHeight(e,t,r,n){return"vertical"===e&&x(t)?{height:t}:"horizontal"===e?{width:r||n}:null}render(){return d.createElement(nV,this.props)}}function nH(){return(nH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function nq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nq(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function nG(e){return Array.isArray(e)&&w(e[0])&&w(e[1])?e.join(" ~ "):e}nB(nX,"displayName","Legend"),nB(nX,"defaultProps",{align:"center",iconSize:14,itemSorter:"value",layout:"horizontal",verticalAlign:"bottom"});var nZ=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:n={},labelStyle:a={},payload:i,formatter:o,itemSorter:l,wrapperClassName:c,labelClassName:s,label:u,labelFormatter:f,accessibilityLayer:h=!1}=e,y=nY({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),v=nY({margin:0},a),m=null!=u,g=m?u:"",b=(0,p.$)("recharts-default-tooltip",c),x=(0,p.$)("recharts-tooltip-label",s);return m&&f&&null!=i&&(g=f(u,i)),d.createElement("div",nH({className:b,style:y},h?{role:"status","aria-live":"assertive"}:{}),d.createElement("p",{className:x,style:v},d.isValidElement(g)?g:"".concat(g)),(()=>{if(i&&i.length){var e=(l?e8()(i,l):i).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||o||nG,{value:l,name:c}=e,s=l,u=c;if(a){var f=a(l,c,e,r,i);if(Array.isArray(f))[s,u]=f;else{if(null==f)return null;s=f}}var p=nY({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},n);return d.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:p},w(u)?d.createElement("span",{className:"recharts-tooltip-item-name"},u):null,w(u)?d.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,d.createElement("span",{className:"recharts-tooltip-item-value"},s),d.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return d.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},nJ="recharts-tooltip-wrapper",nQ={visibility:"hidden"};function n0(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:a,position:i,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=e;if(i&&x(i[n]))return i[n];var u=r[n]-l-(a>0?a:0),f=r[n]+a;if(t[n])return o[n]?u:f;var d=c[n];return null==d?0:o[n]?u<d?Math.max(f,d):Math.max(u,d):null==s?0:f+l>d+s?Math.max(u,d):Math.max(f,d)}function n1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function n2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n1(Object(r),!0).forEach(function(t){n5(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function n5(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class n3 extends d.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:n,children:a,coordinate:i,hasPayload:o,isAnimationActive:l,offset:c,position:s,reverseDirection:u,useTranslate3d:f,viewBox:h,wrapperStyle:y,lastBoundingBox:v,innerRef:m,hasPortalFromProps:g}=this.props,{cssClasses:b,cssProperties:w}=function(e){var t,r,n,{allowEscapeViewBox:a,coordinate:i,offsetTopLeft:o,position:l,reverseDirection:c,tooltipBox:s,useTranslate3d:u,viewBox:f}=e;return{cssProperties:t=s.height>0&&s.width>0&&i?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=n0({allowEscapeViewBox:a,coordinate:i,key:"x",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:s.width,viewBox:f,viewBoxDimension:f.width}),translateY:n=n0({allowEscapeViewBox:a,coordinate:i,key:"y",offsetTopLeft:o,position:l,reverseDirection:c,tooltipDimension:s.height,viewBox:f,viewBoxDimension:f.height}),useTranslate3d:u}):nQ,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,p.$)(nJ,{["".concat(nJ,"-right")]:x(r)&&t&&x(t.x)&&r>=t.x,["".concat(nJ,"-left")]:x(r)&&t&&x(t.x)&&r<t.x,["".concat(nJ,"-bottom")]:x(n)&&t&&x(t.y)&&n>=t.y,["".concat(nJ,"-top")]:x(n)&&t&&x(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:i})}}({allowEscapeViewBox:t,coordinate:i,offsetTopLeft:c,position:s,reverseDirection:u,tooltipBox:{height:v.height,width:v.width},useTranslate3d:f,viewBox:h}),O=g?{}:n2(n2({transition:l&&e?"transform ".concat(r,"ms ").concat(n):void 0},w),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&o?"visible":"hidden",position:"absolute",top:0,left:0}),P=n2(n2({},O),{},{visibility:!this.state.dismissed&&e&&o?"visible":"hidden"},y);return d.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:b,style:P,ref:m},a)}constructor(){super(...arguments),n5(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),n5(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,a;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(a=this.props.coordinate)?void 0:a.y)?n:0}})}})}}var n4={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout)},n6=()=>eB(e=>e.rootProps.accessibilityLayer);function n8(){}function n7(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function n9(e){this._context=e}function ae(e){this._context=e}function at(e){this._context=e}n9.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:n7(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:n7(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},ae.prototype={areaStart:n8,areaEnd:n8,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:n7(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},at.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:n7(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class ar{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function an(e){this._context=e}function aa(e){this._context=e}function ai(e){return new aa(e)}an.prototype={areaStart:n8,areaEnd:n8,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function ao(e,t,r){var n=e._x1-e._x0,a=t-e._x1,i=(e._y1-e._y0)/(n||a<0&&-0),o=(r-e._y1)/(a||n<0&&-0);return((i<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(i),Math.abs(o),.5*Math.abs((i*a+o*n)/(n+a)))||0}function al(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function ac(e,t,r){var n=e._x0,a=e._y0,i=e._x1,o=e._y1,l=(i-n)/3;e._context.bezierCurveTo(n+l,a+l*t,i-l,o-l*r,i,o)}function as(e){this._context=e}function au(e){this._context=new af(e)}function af(e){this._context=e}function ad(e){this._context=e}function ap(e){var t,r,n=e.length-1,a=Array(n),i=Array(n),o=Array(n);for(a[0]=0,i[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)a[t]=1,i[t]=4,o[t]=4*e[t]+2*e[t+1];for(a[n-1]=2,i[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=a[t]/i[t-1],i[t]-=r,o[t]-=r*o[t-1];for(a[n-1]=o[n-1]/i[n-1],t=n-2;t>=0;--t)a[t]=(o[t]-a[t+1])/i[t];for(t=0,i[n-1]=(e[n]+a[n-1])/2;t<n-1;++t)i[t]=2*e[t+1]-a[t+1];return[a,i]}function ah(e,t){this._context=e,this._t=t}function ay(e){return e[0]}function av(e){return e[1]}function am(e,t){var r=ed(!0),n=null,a=ai,i=null,o=eg(l);function l(l){var c,s,u,f=(l=rz(l)).length,d=!1;for(null==n&&(i=a(u=o())),c=0;c<=f;++c)!(c<f&&r(s=l[c],c,l))===d&&((d=!d)?i.lineStart():i.lineEnd()),d&&i.point(+e(s,c,l),+t(s,c,l));if(u)return i=null,u+""||null}return e="function"==typeof e?e:void 0===e?ay:ed(e),t="function"==typeof t?t:void 0===t?av:ed(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:ed(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:ed(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:ed(!!e),l):r},l.curve=function(e){return arguments.length?(a=e,null!=n&&(i=a(n)),l):a},l.context=function(e){return arguments.length?(null==e?n=i=null:i=a(n=e),l):n},l}function ag(e,t,r){var n=null,a=ed(!0),i=null,o=ai,l=null,c=eg(s);function s(s){var u,f,d,p,h,y=(s=rz(s)).length,v=!1,m=Array(y),g=Array(y);for(null==i&&(l=o(h=c())),u=0;u<=y;++u){if(!(u<y&&a(p=s[u],u,s))===v)if(v=!v)f=u,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),d=u-1;d>=f;--d)l.point(m[d],g[d]);l.lineEnd(),l.areaEnd()}v&&(m[u]=+e(p,u,s),g[u]=+t(p,u,s),l.point(n?+n(p,u,s):m[u],r?+r(p,u,s):g[u]))}if(h)return l=null,h+""||null}function u(){return am().defined(a).curve(o).context(i)}return e="function"==typeof e?e:void 0===e?ay:ed(+e),t="function"==typeof t?t:void 0===t?ed(0):ed(+t),r="function"==typeof r?r:void 0===r?av:ed(+r),s.x=function(t){return arguments.length?(e="function"==typeof t?t:ed(+t),n=null,s):e},s.x0=function(t){return arguments.length?(e="function"==typeof t?t:ed(+t),s):e},s.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:ed(+e),s):n},s.y=function(e){return arguments.length?(t="function"==typeof e?e:ed(+e),r=null,s):t},s.y0=function(e){return arguments.length?(t="function"==typeof e?e:ed(+e),s):t},s.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:ed(+e),s):r},s.lineX0=s.lineY0=function(){return u().x(e).y(t)},s.lineY1=function(){return u().x(e).y(r)},s.lineX1=function(){return u().x(n).y(t)},s.defined=function(e){return arguments.length?(a="function"==typeof e?e:ed(!!e),s):a},s.curve=function(e){return arguments.length?(o=e,null!=i&&(l=o(i)),s):o},s.context=function(e){return arguments.length?(null==e?i=l=null:l=o(i=e),s):i},s}function ab(e){return Number.isFinite(e)}function ax(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}function aw(){return(aw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function aO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aO(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}aa.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},as.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:ac(this,this._t0,al(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,ac(this,al(this,r=ao(this,e,t)),r);break;default:ac(this,this._t0,r=ao(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(au.prototype=Object.create(as.prototype)).point=function(e,t){as.prototype.point.call(this,t,e)},af.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,a,i){this._context.bezierCurveTo(t,e,n,r,i,a)}},ad.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=ap(e),a=ap(t),i=0,o=1;o<r;++i,++o)this._context.bezierCurveTo(n[0][i],a[0][i],n[1][i],a[1][i],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},ah.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var aj={curveBasisClosed:function(e){return new ae(e)},curveBasisOpen:function(e){return new at(e)},curveBasis:function(e){return new n9(e)},curveBumpX:function(e){return new ar(e,!0)},curveBumpY:function(e){return new ar(e,!1)},curveLinearClosed:function(e){return new an(e)},curveLinear:ai,curveMonotoneX:function(e){return new as(e)},curveMonotoneY:function(e){return new au(e)},curveNatural:function(e){return new ad(e)},curveStep:function(e){return new ah(e,.5)},curveStepAfter:function(e){return new ah(e,1)},curveStepBefore:function(e){return new ah(e,0)}},aE=e=>ab(e.x)&&ab(e.y),aS=e=>e.x,ak=e=>e.y,aA=e=>{var{className:t,points:r,path:n,pathRef:a}=e;if((!r||!r.length)&&!n)return null;var i=r&&r.length?(e=>{var t,{type:r="linear",points:n=[],baseLine:a,layout:i,connectNulls:o=!1}=e,l=((e,t)=>{if("function"==typeof e)return e;var r="curve".concat(M(e));return("curveMonotone"===r||"curveBump"===r)&&t?aj["".concat(r).concat("vertical"===t?"Y":"X")]:aj[r]||ai})(r,i),c=o?n.filter(aE):n;if(Array.isArray(a)){var s=o?a.filter(e=>aE(e)):a,u=c.map((e,t)=>aP(aP({},e),{},{base:s[t]}));return(t="vertical"===i?ag().y(ak).x1(aS).x0(e=>e.base.x):ag().x(aS).y1(ak).y0(e=>e.base.y)).defined(aE).curve(l),t(u)}return(t="vertical"===i&&x(a)?ag().y(ak).x1(aS).x0(a):x(a)?ag().x(aS).y1(ak).y0(a):am().x(aS).y(ak)).defined(aE).curve(l),t(c)})(e):n;return d.createElement("path",aw({},F(e,!1),I(e),{className:(0,p.$)("recharts-curve",t),d:null===i?void 0:i,ref:a}))},aM=["x","y","top","left","width","height","className"];function aT(){return(aT=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function aC(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var aD=e=>{var{x:t=0,y:r=0,top:n=0,left:a=0,width:i=0,height:o=0,className:l}=e,c=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aC(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aC(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:n,left:a,width:i,height:o},function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,aM));return x(t)&&x(r)&&x(i)&&x(o)&&x(n)&&x(a)?d.createElement("path",aT({},F(c,!0),{className:(0,p.$)("recharts-cross",l),d:"M".concat(t,",").concat(n,"v").concat(o,"M").concat(a,",").concat(r,"h").concat(i)})):null};function aN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aI(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aN(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}var a_=r(22188),aL=r.n(a_),aR=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],az=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),aK=(e,t)=>r=>az(aR(e,t),r),aB=function(){let e,t;for(var r,n,a,i,o=arguments.length,l=Array(o),c=0;c<o;c++)l[c]=arguments[c];if(1===l.length)switch(l[0]){case"linear":[r,a,n,i]=[0,0,1,1];break;case"ease":[r,a,n,i]=[.25,.1,.25,1];break;case"ease-in":[r,a,n,i]=[.42,0,1,1];break;case"ease-out":[r,a,n,i]=[.42,0,.58,1];break;case"ease-in-out":[r,a,n,i]=[0,0,.58,1];break;default:var s=l[0].split("(");"cubic-bezier"===s[0]&&4===s[1].split(")")[0].split(",").length&&([r,a,n,i]=s[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===l.length&&([r,a,n,i]=l);var u=aK(r,n),f=aK(a,i),d=(e=r,t=n,r=>az([...aR(e,t).map((e,t)=>e*t).slice(1),0],r)),p=e=>e>1?1:e<0?0:e,h=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var a=u(r)-t,i=d(r);if(1e-4>Math.abs(a-t)||i<1e-4)break;r=p(r-a/i)}return f(r)};return h.isStepper=!1,h},a$=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,a=(e,a,i)=>{var o=i+(-(e-a)*t-i*r)*n/1e3,l=i*n/1e3+e;return 1e-4>Math.abs(l-a)&&1e-4>Math.abs(o)?[a,0]:[l,o]};return a.isStepper=!0,a.dt=n,a};function aF(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aF(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aF(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var aW=(e,t)=>Object.keys(t).reduce((r,n)=>aU(aU({},r),{},{[n]:e(n,t[n])}),{});function aV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aV(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var aH=(e,t,r)=>e+(t-e)*r,aq=e=>{var{from:t,to:r}=e;return t!==r},aY=(e,t,r)=>{var n=aW((t,r)=>{if(aq(r)){var[n,a]=e(r.from,r.to,r.velocity);return aX(aX({},r),{},{from:n,velocity:a})}return r},t);return r<1?aW((e,t)=>aq(t)?aX(aX({},t),{},{velocity:aH(t.velocity,n[e].velocity,r),from:aH(t.from,n[e].from,r)}):t,t):aY(e,n,r-1)};class aG{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,a=i=>{i-r>=t?e(i):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(a))};return n=requestAnimationFrame(a),()=>{cancelAnimationFrame(n)}}}var aZ=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function aJ(){return(aJ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function aQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aQ(Object(r),!0).forEach(function(t){a1(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function a1(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class a2 extends d.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:a,to:i,from:o}=this.props,{style:l}=this.state;if(r){if(!t){this.state&&l&&(n&&l[n]!==i||!n&&l!==i)&&this.setState({style:n?{[n]:i}:i});return}if(!aL()(e.to,i)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||a?o:e.to;this.state&&l&&(n&&l[n]!==s||!n&&l!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(a0(a0({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var t,r,n,a,i,o,l,c,s,u,f,d,p,h,y,v,m,g,b,x,w,O,P,j,E,{from:S,to:k,duration:A,easing:M,begin:T,onAnimationEnd:C,onAnimationStart:D}=e,N=(O=(e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return aB(e);case"spring":return a$();default:if("cubic-bezier"===e.split("(")[0])return aB(e)}return"function"==typeof e?e:null})(M),P=this.changeStyle,j=this.manager.getTimeoutController(),E=[Object.keys(S),Object.keys(k)].reduce((e,t)=>e.filter(e=>t.includes(e))),!0===O.isStepper?(t=S,r=k,n=O,a=E,i=P,o=j,c=a.reduce((e,n)=>aX(aX({},e),{},{[n]:{from:t[n],velocity:0,to:r[n]}}),{}),s=null,u=e=>{l||(l=e);var a=(e-l)/n.dt;c=aY(n,c,a),i(aX(aX(aX({},t),r),aW((e,t)=>t.from,c))),l=e,Object.values(c).filter(aq).length&&(s=o.setTimeout(u))},()=>(s=o.setTimeout(u),()=>{s()})):(f=S,d=k,p=O,h=A,y=E,v=P,m=j,b=null,x=y.reduce((e,t)=>aX(aX({},e),{},{[t]:[f[t],d[t]]}),{}),w=e=>{g||(g=e);var t=(e-g)/h,r=aW((e,r)=>aH(...r,p(t)),x);if(v(aX(aX(aX({},f),d),r)),t<1)b=m.setTimeout(w);else{var n=aW((e,t)=>aH(...t,p(1)),x);v(aX(aX(aX({},f),d),n))}},()=>(b=m.setTimeout(w),()=>{b()})));this.manager.start([D,T,()=>{this.stopJSAnimation=N()},A,C])}runAnimation(e){let t;var{begin:r,duration:n,attributeName:a,to:i,easing:o,onAnimationStart:l,onAnimationEnd:c,children:s}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof o||"function"==typeof s||"spring"===o)return void this.runJSAnimation(e);var u=a?{[a]:i}:i,f=(t=Object.keys(u),t.map(e=>"".concat(e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase()))," ").concat(n,"ms ").concat(o)).join(","));this.manager.start([l,r,a0(a0({},u),{},{transition:f}),n,c])}render(){var e=this.props,{children:t,begin:r,duration:n,attributeName:a,easing:i,isActive:o,from:l,to:c,canBegin:s,onAnimationEnd:u,shouldReAnimate:f,onAnimationReStart:p,animationManager:h}=e,y=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,aZ),v=d.Children.count(t),m=this.state.style;if("function"==typeof t)return t(m);if(!o||0===v||n<=0)return t;var g=e=>{var{style:t={},className:r}=e.props;return(0,d.cloneElement)(e,a0(a0({},y),{},{style:a0(a0({},t),m),className:r}))};return 1===v?g(d.Children.only(t)):d.createElement("div",null,d.Children.map(t,e=>g(e)))}constructor(e,t){super(e,t),a1(this,"mounted",!1),a1(this,"manager",null),a1(this,"stopJSAnimation",null),a1(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:a,to:i,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:i});return}if(a){if("function"==typeof o){this.state={style:a};return}this.state={style:n?{[n]:a}:a}}else this.state={style:{}}}}a1(a2,"displayName","Animate"),a1(a2,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var a5=(0,d.createContext)(null);function a3(e){var t,r,n,a,i,o,l,c=(0,d.useContext)(a5);return d.createElement(a2,aJ({},e,{animationManager:null!=(o=null!=(l=e.animationManager)?l:c)?o:(t=new aG,r=()=>null,n=!1,a=null,i=e=>{if(!n){if(Array.isArray(e)){if(!e.length)return;var[o,...l]=e;if("number"==typeof o){a=t.setTimeout(i.bind(null,l),o);return}i(o),a=t.setTimeout(i.bind(null,l));return}"object"==typeof e&&r(e),"function"==typeof e&&e()}},{stop:()=>{n=!0},start:e=>{n=!1,a&&(a(),a=null),i(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>t})}))}function a4(){return(a4=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var a6=(e,t,r,n,a)=>{var i,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,s=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&a instanceof Array){for(var u=[0,0,0,0],f=0;f<4;f++)u[f]=a[f]>o?o:a[f];i="M".concat(e,",").concat(t+l*u[0]),u[0]>0&&(i+="A ".concat(u[0],",").concat(u[0],",0,0,").concat(s,",").concat(e+c*u[0],",").concat(t)),i+="L ".concat(e+r-c*u[1],",").concat(t),u[1]>0&&(i+="A ".concat(u[1],",").concat(u[1],",0,0,").concat(s,",\n        ").concat(e+r,",").concat(t+l*u[1])),i+="L ".concat(e+r,",").concat(t+n-l*u[2]),u[2]>0&&(i+="A ".concat(u[2],",").concat(u[2],",0,0,").concat(s,",\n        ").concat(e+r-c*u[2],",").concat(t+n)),i+="L ".concat(e+c*u[3],",").concat(t+n),u[3]>0&&(i+="A ".concat(u[3],",").concat(u[3],",0,0,").concat(s,",\n        ").concat(e,",").concat(t+n-l*u[3])),i+="Z"}else if(o>0&&a===+a&&a>0){var d=Math.min(o,a);i="M ".concat(e,",").concat(t+l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+c*d,",").concat(t,"\n            L ").concat(e+r-c*d,",").concat(t,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r,",").concat(t+l*d,"\n            L ").concat(e+r,",").concat(t+n-l*d,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e+r-c*d,",").concat(t+n,"\n            L ").concat(e+c*d,",").concat(t+n,"\n            A ").concat(d,",").concat(d,",0,0,").concat(s,",").concat(e,",").concat(t+n-l*d," Z")}else i="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},a8={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},a7=e=>{var t=aI(e,a8),r=(0,d.useRef)(null),[n,a]=(0,d.useState)(-1);(0,d.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:i,y:o,width:l,height:c,radius:s,className:u}=t,{animationEasing:f,animationDuration:h,animationBegin:y,isAnimationActive:v,isUpdateAnimationActive:m}=t;if(i!==+i||o!==+o||l!==+l||c!==+c||0===l||0===c)return null;var g=(0,p.$)("recharts-rectangle",u);return m?d.createElement(a3,{canBegin:n>0,from:{width:l,height:c,x:i,y:o},to:{width:l,height:c,x:i,y:o},duration:h,animationEasing:f,isActive:m},e=>{var{width:a,height:i,x:o,y:l}=e;return d.createElement(a3,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,isActive:v,easing:f},d.createElement("path",a4({},F(t,!0),{className:g,d:a6(o,l,a,i,s),ref:r})))}):d.createElement("path",a4({},F(t,!0),{className:g,d:a6(i,o,l,c,s)}))};function a9(e){var{cx:t,cy:r,radius:n,startAngle:a,endAngle:i}=e;return{points:[rV(t,r,n,a),rV(t,r,n,i)],cx:t,cy:r,radius:n,startAngle:a,endAngle:i}}function ie(){return(ie=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var it=e=>{var{cx:t,cy:r,radius:n,angle:a,sign:i,isExternal:o,cornerRadius:l,cornerIsExternal:c}=e,s=l*(o?1:-1)+n,u=Math.asin(l/s)/rW,f=c?a:a+i*u,d=rV(t,r,s,f);return{center:d,circleTangency:rV(t,r,n,f),lineTangency:rV(t,r,s*Math.cos(u*rW),c?a-i*u:a),theta:u}},ir=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:a,startAngle:i,endAngle:o}=e,l=((e,t)=>m(t-e)*Math.min(Math.abs(t-e),359.999))(i,o),c=i+l,s=rV(t,r,a,i),u=rV(t,r,a,c),f="M ".concat(s.x,",").concat(s.y,"\n    A ").concat(a,",").concat(a,",0,\n    ").concat(+(Math.abs(l)>180),",").concat(+(i>c),",\n    ").concat(u.x,",").concat(u.y,"\n  ");if(n>0){var d=rV(t,r,n,i),p=rV(t,r,n,c);f+="L ".concat(p.x,",").concat(p.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(l)>180),",").concat(+(i<=c),",\n            ").concat(d.x,",").concat(d.y," Z")}else f+="L ".concat(t,",").concat(r," Z");return f},ia={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},ii=e=>{var t,r=aI(e,ia),{cx:n,cy:a,innerRadius:i,outerRadius:o,cornerRadius:l,forceCornerRadius:c,cornerIsExternal:s,startAngle:u,endAngle:f,className:h}=r;if(o<i||u===f)return null;var y=(0,p.$)("recharts-sector",h),v=o-i,g=j(l,v,0,!0);return t=g>0&&360>Math.abs(u-f)?(e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:a,cornerRadius:i,forceCornerRadius:o,cornerIsExternal:l,startAngle:c,endAngle:s}=e,u=m(s-c),{circleTangency:f,lineTangency:d,theta:p}=it({cx:t,cy:r,radius:a,angle:c,sign:u,cornerRadius:i,cornerIsExternal:l}),{circleTangency:h,lineTangency:y,theta:v}=it({cx:t,cy:r,radius:a,angle:s,sign:-u,cornerRadius:i,cornerIsExternal:l}),g=l?Math.abs(c-s):Math.abs(c-s)-p-v;if(g<0)return o?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):ir({cx:t,cy:r,innerRadius:n,outerRadius:a,startAngle:c,endAngle:s});var b="M ".concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(f.x,",").concat(f.y,"\n    A").concat(a,",").concat(a,",0,").concat(+(g>180),",").concat(+(u<0),",").concat(h.x,",").concat(h.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(y.x,",").concat(y.y,"\n  ");if(n>0){var{circleTangency:x,lineTangency:w,theta:O}=it({cx:t,cy:r,radius:n,angle:c,sign:u,isExternal:!0,cornerRadius:i,cornerIsExternal:l}),{circleTangency:P,lineTangency:j,theta:E}=it({cx:t,cy:r,radius:n,angle:s,sign:-u,isExternal:!0,cornerRadius:i,cornerIsExternal:l}),S=l?Math.abs(c-s):Math.abs(c-s)-O-E;if(S<0&&0===i)return"".concat(b,"L").concat(t,",").concat(r,"Z");b+="L".concat(j.x,",").concat(j.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(S>180),",").concat(+(u>0),",").concat(x.x,",").concat(x.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(u<0),",").concat(w.x,",").concat(w.y,"Z")}else b+="L".concat(t,",").concat(r,"Z");return b})({cx:n,cy:a,innerRadius:i,outerRadius:o,cornerRadius:Math.min(g,v/2),forceCornerRadius:c,cornerIsExternal:s,startAngle:u,endAngle:f}):ir({cx:n,cy:a,innerRadius:i,outerRadius:o,startAngle:u,endAngle:f}),d.createElement("path",ie({},F(r,!0),{className:y,d:t}))},io=r(83949),il=r.n(io);function ic(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function is(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class iu extends Map{constructor(e,t=ip){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(id(this,e))}has(e){return super.has(id(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function id({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function ip(e){return null!==e&&"object"==typeof e?e.valueOf():e}let ih=Symbol("implicit");function iy(){var e=new iu,t=[],r=[],n=ih;function a(a){let i=e.get(a);if(void 0===i){if(n!==ih)return n;e.set(a,i=t.push(a)-1)}return r[i%r.length]}return a.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new iu,r))e.has(n)||e.set(n,t.push(n)-1);return a},a.range=function(e){return arguments.length?(r=Array.from(e),a):r.slice()},a.unknown=function(e){return arguments.length?(n=e,a):n},a.copy=function(){return iy(t,r).unknown(n)},ic.apply(a,arguments),a}function iv(){var e,t,r=iy().unknown(void 0),n=r.domain,a=r.range,i=0,o=1,l=!1,c=0,s=0,u=.5;function f(){var r=n().length,f=o<i,d=f?o:i,p=f?i:o;e=(p-d)/Math.max(1,r-c+2*s),l&&(e=Math.floor(e)),d+=(p-d-e*(r-c))*u,t=e*(1-c),l&&(d=Math.round(d),t=Math.round(t));var h=(function(e,t,r){e*=1,t*=1,r=(a=arguments.length)<2?(t=e,e=0,1):a<3?1:+r;for(var n=-1,a=0|Math.max(0,Math.ceil((t-e)/r)),i=Array(a);++n<a;)i[n]=e+n*r;return i})(r).map(function(t){return d+e*t});return a(f?h.reverse():h)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([i,o]=e,i*=1,o*=1,f()):[i,o]},r.rangeRound=function(e){return[i,o]=e,i*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(c=Math.min(1,s=+e),f()):c},r.paddingInner=function(e){return arguments.length?(c=Math.min(1,e),f()):c},r.paddingOuter=function(e){return arguments.length?(s=+e,f()):s},r.align=function(e){return arguments.length?(u=Math.max(0,Math.min(1,e)),f()):u},r.copy=function(){return iv(n(),[i,o]).round(l).paddingInner(c).paddingOuter(s).align(u)},ic.apply(f(),arguments)}function im(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(iv.apply(null,arguments).paddingInner(1))}let ig=Math.sqrt(50),ib=Math.sqrt(10),ix=Math.sqrt(2);function iw(e,t,r){let n,a,i,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),c=o/Math.pow(10,l),s=c>=ig?10:c>=ib?5:c>=ix?2:1;return(l<0?(n=Math.round(e*(i=Math.pow(10,-l)/s)),a=Math.round(t*i),n/i<e&&++n,a/i>t&&--a,i=-i):(n=Math.round(e/(i=Math.pow(10,l)*s)),a=Math.round(t/i),n*i<e&&++n,a*i>t&&--a),a<n&&.5<=r&&r<2)?iw(e,t,2*r):[n,a,i]}function iO(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[a,i,o]=n?iw(t,e,r):iw(e,t,r);if(!(i>=a))return[];let l=i-a+1,c=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)c[e]=-((i-e)/o);else for(let e=0;e<l;++e)c[e]=(i-e)*o;else if(o<0)for(let e=0;e<l;++e)c[e]=-((a+e)/o);else for(let e=0;e<l;++e)c[e]=(a+e)*o;return c}function iP(e,t,r){return iw(e*=1,t*=1,r*=1)[2]}function ij(e,t,r){t*=1,e*=1,r*=1;let n=t<e,a=n?iP(t,e,r):iP(e,t,r);return(n?-1:1)*(a<0?-(1/a):a)}function iE(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function iS(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function ik(e){let t,r,n;function a(e,n,i=0,o=e.length){if(i<o){if(0!==t(n,n))return o;do{let t=i+o>>>1;0>r(e[t],n)?i=t+1:o=t}while(i<o)}return i}return 2!==e.length?(t=iE,r=(t,r)=>iE(e(t),r),n=(t,r)=>e(t)-r):(t=e===iE||e===iS?e:iA,r=e,n=e),{left:a,center:function(e,t,r=0,i=e.length){let o=a(e,t,r,i-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,a=0,i=e.length){if(a<i){if(0!==t(n,n))return i;do{let t=a+i>>>1;0>=r(e[t],n)?a=t+1:i=t}while(a<i)}return a}}}function iA(){return 0}function iM(e){return null===e?NaN:+e}let iT=ik(iE),iC=iT.right;function iD(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function iN(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function iI(){}iT.left,ik(iM).center;var i_="\\s*([+-]?\\d+)\\s*",iL="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",iR="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iz=/^#([0-9a-f]{3,8})$/,iK=RegExp(`^rgb\\(${i_},${i_},${i_}\\)$`),iB=RegExp(`^rgb\\(${iR},${iR},${iR}\\)$`),i$=RegExp(`^rgba\\(${i_},${i_},${i_},${iL}\\)$`),iF=RegExp(`^rgba\\(${iR},${iR},${iR},${iL}\\)$`),iU=RegExp(`^hsl\\(${iL},${iR},${iR}\\)$`),iW=RegExp(`^hsla\\(${iL},${iR},${iR},${iL}\\)$`),iV={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function iX(){return this.rgb().formatHex()}function iH(){return this.rgb().formatRgb()}function iq(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=iz.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?iY(t):3===r?new iJ(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?iG(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?iG(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=iK.exec(e))?new iJ(t[1],t[2],t[3],1):(t=iB.exec(e))?new iJ(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=i$.exec(e))?iG(t[1],t[2],t[3],t[4]):(t=iF.exec(e))?iG(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=iU.exec(e))?i3(t[1],t[2]/100,t[3]/100,1):(t=iW.exec(e))?i3(t[1],t[2]/100,t[3]/100,t[4]):iV.hasOwnProperty(e)?iY(iV[e]):"transparent"===e?new iJ(NaN,NaN,NaN,0):null}function iY(e){return new iJ(e>>16&255,e>>8&255,255&e,1)}function iG(e,t,r,n){return n<=0&&(e=t=r=NaN),new iJ(e,t,r,n)}function iZ(e,t,r,n){var a;return 1==arguments.length?((a=e)instanceof iI||(a=iq(a)),a)?new iJ((a=a.rgb()).r,a.g,a.b,a.opacity):new iJ:new iJ(e,t,r,null==n?1:n)}function iJ(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function iQ(){return`#${i5(this.r)}${i5(this.g)}${i5(this.b)}`}function i0(){let e=i1(this.opacity);return`${1===e?"rgb(":"rgba("}${i2(this.r)}, ${i2(this.g)}, ${i2(this.b)}${1===e?")":`, ${e})`}`}function i1(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function i2(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function i5(e){return((e=i2(e))<16?"0":"")+e.toString(16)}function i3(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new i6(e,t,r,n)}function i4(e){if(e instanceof i6)return new i6(e.h,e.s,e.l,e.opacity);if(e instanceof iI||(e=iq(e)),!e)return new i6;if(e instanceof i6)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,a=Math.min(t,r,n),i=Math.max(t,r,n),o=NaN,l=i-a,c=(i+a)/2;return l?(o=t===i?(r-n)/l+(r<n)*6:r===i?(n-t)/l+2:(t-r)/l+4,l/=c<.5?i+a:2-i-a,o*=60):l=c>0&&c<1?0:o,new i6(o,l,c,e.opacity)}function i6(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function i8(e){return(e=(e||0)%360)<0?e+360:e}function i7(e){return Math.max(0,Math.min(1,e||0))}function i9(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function oe(e,t,r,n,a){var i=e*e,o=i*e;return((1-3*e+3*i-o)*t+(4-6*i+3*o)*r+(1+3*e+3*i-3*o)*n+o*a)/6}iD(iI,iq,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:iX,formatHex:iX,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return i4(this).formatHsl()},formatRgb:iH,toString:iH}),iD(iJ,iZ,iN(iI,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new iJ(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new iJ(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new iJ(i2(this.r),i2(this.g),i2(this.b),i1(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iQ,formatHex:iQ,formatHex8:function(){return`#${i5(this.r)}${i5(this.g)}${i5(this.b)}${i5((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:i0,toString:i0})),iD(i6,function(e,t,r,n){return 1==arguments.length?i4(e):new i6(e,t,r,null==n?1:n)},iN(iI,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new i6(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new i6(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,a=2*r-n;return new iJ(i9(e>=240?e-240:e+120,a,n),i9(e,a,n),i9(e<120?e+240:e-120,a,n),this.opacity)},clamp(){return new i6(i8(this.h),i7(this.s),i7(this.l),i1(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=i1(this.opacity);return`${1===e?"hsl(":"hsla("}${i8(this.h)}, ${100*i7(this.s)}%, ${100*i7(this.l)}%${1===e?")":`, ${e})`}`}}));let ot=e=>()=>e;function or(e,t){var r=t-e;return r?function(t){return e+t*r}:ot(isNaN(e)?t:e)}let on=function e(t){var r,n=1==(r=+t)?or:function(e,t){var n,a,i;return t-e?(n=e,a=t,n=Math.pow(n,i=r),a=Math.pow(a,i)-n,i=1/i,function(e){return Math.pow(n+e*a,i)}):ot(isNaN(e)?t:e)};function a(e,t){var r=n((e=iZ(e)).r,(t=iZ(t)).r),a=n(e.g,t.g),i=n(e.b,t.b),o=or(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=a(t),e.b=i(t),e.opacity=o(t),e+""}}return a.gamma=e,a}(1);function oa(e){return function(t){var r,n,a=t.length,i=Array(a),o=Array(a),l=Array(a);for(r=0;r<a;++r)n=iZ(t[r]),i[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return i=e(i),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=i(e),n.g=o(e),n.b=l(e),n+""}}}function oi(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}oa(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),a=e[n],i=e[n+1],o=n>0?e[n-1]:2*a-i,l=n<t-1?e[n+2]:2*i-a;return oe((r-n/t)*t,o,a,i,l)}}),oa(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),a=e[(n+t-1)%t],i=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return oe((r-n/t)*t,a,i,o,l)}});var oo=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ol=RegExp(oo.source,"g");function oc(e,t){var r,n,a=typeof t;return null==t||"boolean"===a?ot(t):("number"===a?oi:"string"===a?(n=iq(t))?(t=n,on):function(e,t){var r,n,a,i,o,l=oo.lastIndex=ol.lastIndex=0,c=-1,s=[],u=[];for(e+="",t+="";(a=oo.exec(e))&&(i=ol.exec(t));)(o=i.index)>l&&(o=t.slice(l,o),s[c]?s[c]+=o:s[++c]=o),(a=a[0])===(i=i[0])?s[c]?s[c]+=i:s[++c]=i:(s[++c]=null,u.push({i:c,x:oi(a,i)})),l=ol.lastIndex;return l<t.length&&(o=t.slice(l),s[c]?s[c]+=o:s[++c]=o),s.length<2?u[0]?(r=u[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=u.length,function(e){for(var r,n=0;n<t;++n)s[(r=u[n]).i]=r.x(e);return s.join("")})}:t instanceof iq?on:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,a=e?Math.min(n,e.length):0,i=Array(a),o=Array(n);for(r=0;r<a;++r)i[r]=oc(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<a;++r)o[r]=i[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},a={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=oc(e[r],t[r]):a[r]=t[r];return function(e){for(r in n)a[r]=n[r](e);return a}}:oi:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,a=t.slice();return function(i){for(r=0;r<n;++r)a[r]=e[r]*(1-i)+t[r]*i;return a}})(e,t)}function os(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function ou(e){return+e}var of=[0,1];function od(e){return e}function op(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function oh(e,t,r){var n=e[0],a=e[1],i=t[0],o=t[1];return a<n?(n=op(a,n),i=r(o,i)):(n=op(n,a),i=r(i,o)),function(e){return i(n(e))}}function oy(e,t,r){var n=Math.min(e.length,t.length)-1,a=Array(n),i=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)a[o]=op(e[o],e[o+1]),i[o]=r(t[o],t[o+1]);return function(t){var r=iC(e,t,1,n)-1;return i[r](a[r](t))}}function ov(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function om(){var e,t,r,n,a,i,o=of,l=of,c=oc,s=od;function u(){var e,t,r,c=Math.min(o.length,l.length);return s!==od&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),s=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?oy:oh,a=i=null,f}function f(t){return null==t||isNaN(t*=1)?r:(a||(a=n(o.map(e),l,c)))(e(s(t)))}return f.invert=function(r){return s(t((i||(i=n(l,o.map(e),oi)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,ou),u()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),u()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=os,u()},f.clamp=function(e){return arguments.length?(s=!!e||od,u()):s!==od},f.interpolate=function(e){return arguments.length?(c=e,u()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,u()}}function og(){return om()(od,od)}var ob=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ox(e){var t;if(!(t=ob.exec(e)))throw Error("invalid format: "+e);return new ow({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ow(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function oO(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function oP(e){return(e=oO(Math.abs(e)))?e[1]:NaN}function oj(e,t){var r=oO(e,t);if(!r)return e+"";var n=r[0],a=r[1];return a<0?"0."+Array(-a).join("0")+n:n.length>a+1?n.slice(0,a+1)+"."+n.slice(a+1):n+Array(a-n.length+2).join("0")}ox.prototype=ow.prototype,ow.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let oE={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>oj(100*e,t),r:oj,s:function(e,t){var r=oO(e,t);if(!r)return e+"";var n=r[0],i=r[1],o=i-(a=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,l=n.length;return o===l?n:o>l?n+Array(o-l+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+Array(1-o).join("0")+oO(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function oS(e){return e}var ok=Array.prototype.map,oA=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function oM(e,t,r,n){var a,i,c=ij(e,t,r);switch((n=ox(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(i=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(oP(s)/3)))-oP(Math.abs(c))))||(n.precision=i),l(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(i=Math.max(0,oP(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(a=Math.abs(a=c)))-oP(a))+1)||(n.precision=i-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(i=Math.max(0,-oP(Math.abs(c))))||(n.precision=i-("%"===n.type)*2)}return o(n)}function oT(e){var t=e.domain;return e.ticks=function(e){var r=t();return iO(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return oM(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,a,i=t(),o=0,l=i.length-1,c=i[o],s=i[l],u=10;for(s<c&&(a=c,c=s,s=a,a=o,o=l,l=a);u-- >0;){if((a=iP(c,s,r))===n)return i[o]=c,i[l]=s,t(i);if(a>0)c=Math.floor(c/a)*a,s=Math.ceil(s/a)*a;else if(a<0)c=Math.ceil(c*a)/a,s=Math.floor(s*a)/a;else break;n=a}return e},e}function oC(){var e=og();return e.copy=function(){return ov(e,oC())},ic.apply(e,arguments),oT(e)}function oD(e,t){e=e.slice();var r,n=0,a=e.length-1,i=e[n],o=e[a];return o<i&&(r=n,n=a,a=r,r=i,i=o,o=r),e[n]=t.floor(i),e[a]=t.ceil(o),e}function oN(e){return Math.log(e)}function oI(e){return Math.exp(e)}function o_(e){return-Math.log(-e)}function oL(e){return-Math.exp(-e)}function oR(e){return isFinite(e)?+("1e"+e):e<0?0:e}function oz(e){return(t,r)=>-e(-t,r)}function oK(e){let t,r,n=e(oN,oI),a=n.domain,i=10;function l(){var o,l;return t=(o=i)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=i)?oR:l===Math.E?Math.exp:e=>Math.pow(l,e),a()[0]<0?(t=oz(t),r=oz(r),e(o_,oL)):e(oN,oI),n}return n.base=function(e){return arguments.length?(i=+e,l()):i},n.domain=function(e){return arguments.length?(a(e),l()):a()},n.ticks=e=>{let n,o,l=a(),c=l[0],s=l[l.length-1],u=s<c;u&&([c,s]=[s,c]);let f=t(c),d=t(s),p=null==e?10:+e,h=[];if(!(i%1)&&d-f<p){if(f=Math.floor(f),d=Math.ceil(d),c>0){for(;f<=d;++f)for(n=1;n<i;++n)if(!((o=f<0?n/r(-f):n*r(f))<c)){if(o>s)break;h.push(o)}}else for(;f<=d;++f)for(n=i-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<c)){if(o>s)break;h.push(o)}2*h.length<p&&(h=iO(c,s,p))}else h=iO(f,d,Math.min(d-f,p)).map(r);return u?h.reverse():h},n.tickFormat=(e,a)=>{if(null==e&&(e=10),null==a&&(a=10===i?"s":","),"function"!=typeof a&&(i%1||null!=(a=ox(a)).precision||(a.trim=!0),a=o(a)),e===1/0)return a;let l=Math.max(1,i*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*i<i-.5&&(n*=i),n<=l?a(e):""}},n.nice=()=>a(oD(a(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function oB(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function o$(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function oF(e){var t=1,r=e(oB(1),o$(t));return r.constant=function(r){return arguments.length?e(oB(t=+r),o$(t)):t},oT(r)}function oU(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function oW(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function oV(e){return e<0?-e*e:e*e}function oX(e){var t=e(od,od),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(od,od):.5===r?e(oW,oV):e(oU(r),oU(1/r)):r},oT(t)}function oH(){var e=oX(om());return e.copy=function(){return ov(e,oH()).exponent(e.exponent())},ic.apply(e,arguments),e}function oq(){return oH.apply(null,arguments).exponent(.5)}function oY(e){return Math.sign(e)*e*e}function oG(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let a of e)null!=(a=t(a,++n,e))&&(r<a||void 0===r&&a>=a)&&(r=a)}return r}function oZ(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let a of e)null!=(a=t(a,++n,e))&&(r>a||void 0===r&&a>=a)&&(r=a)}return r}function oJ(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function oQ(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}o=(i=function(e){var t,r,n,i=void 0===e.grouping||void 0===e.thousands?oS:(t=ok.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var a=e.length,i=[],o=0,l=t[0],c=0;a>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),i.push(e.substring(a-=l,a+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return i.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",c=void 0===e.decimal?".":e.decimal+"",s=void 0===e.numerals?oS:(n=ok.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),u=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",d=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=ox(e)).fill,r=e.align,n=e.sign,p=e.symbol,h=e.zero,y=e.width,v=e.comma,m=e.precision,g=e.trim,b=e.type;"n"===b?(v=!0,b="g"):oE[b]||(void 0===m&&(m=12),g=!0,b="g"),(h||"0"===t&&"="===r)&&(h=!0,t="0",r="=");var x="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",w="$"===p?l:/[%p]/.test(b)?u:"",O=oE[b],P=/[defgprs%]/.test(b);function j(e){var o,l,u,p=x,j=w;if("c"===b)j=O(e)+j,e="";else{var E=(e*=1)<0||1/e<0;if(e=isNaN(e)?d:O(Math.abs(e),m),g&&(e=function(e){e:for(var t,r=e.length,n=1,a=-1;n<r;++n)switch(e[n]){case".":a=t=n;break;case"0":0===a&&(a=n),t=n;break;default:if(!+e[n])break e;a>0&&(a=0)}return a>0?e.slice(0,a)+e.slice(t+1):e}(e)),E&&0==+e&&"+"!==n&&(E=!1),p=(E?"("===n?n:f:"-"===n||"("===n?"":n)+p,j=("s"===b?oA[8+a/3]:"")+j+(E&&"("===n?")":""),P){for(o=-1,l=e.length;++o<l;)if(48>(u=e.charCodeAt(o))||u>57){j=(46===u?c+e.slice(o+1):e.slice(o))+j,e=e.slice(0,o);break}}}v&&!h&&(e=i(e,1/0));var S=p.length+e.length+j.length,k=S<y?Array(y-S+1).join(t):"";switch(v&&h&&(e=i(k+e,k.length?y-j.length:1/0),k=""),r){case"<":e=p+e+j+k;break;case"=":e=p+k+e+j;break;case"^":e=k.slice(0,S=k.length>>1)+p+e+j+k.slice(S);break;default:e=k+p+e+j}return s(e)}return m=void 0===m?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),j.toString=function(){return e+""},j}return{format:p,formatPrefix:function(e,t){var r=p(((e=ox(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(oP(t)/3))),a=Math.pow(10,-n),i=oA[8+n/3];return function(e){return r(a*e)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,l=i.formatPrefix;let o0=new Date,o1=new Date;function o2(e,t,r,n){function a(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return a.floor=t=>(e(t=new Date(+t)),t),a.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),a.round=e=>{let t=a(e),r=a.ceil(e);return e-t<r-e?t:r},a.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),a.range=(r,n,i)=>{let o,l=[];if(r=a.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return l;do l.push(o=new Date(+r)),t(r,i),e(r);while(o<r&&r<n);return l},a.filter=r=>o2(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(a.count=(t,n)=>(o0.setTime(+t),o1.setTime(+n),e(o0),e(o1),Math.floor(r(o0,o1))),a.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?a.filter(n?t=>n(t)%e==0:t=>a.count(0,t)%e==0):a:null),a}let o5=o2(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);o5.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?o2(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):o5:null,o5.range;let o3=o2(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());o3.range;let o4=o2(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());o4.range;let o6=o2(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());o6.range;let o8=o2(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());o8.range;let o7=o2(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());o7.range;let o9=o2(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);o9.range;let le=o2(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);le.range;let lt=o2(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function lr(e){return o2(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}lt.range;let ln=lr(0),la=lr(1),li=lr(2),lo=lr(3),ll=lr(4),lc=lr(5),ls=lr(6);function lu(e){return o2(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}ln.range,la.range,li.range,lo.range,ll.range,lc.range,ls.range;let lf=lu(0),ld=lu(1),lp=lu(2),lh=lu(3),ly=lu(4),lv=lu(5),lm=lu(6);lf.range,ld.range,lp.range,lh.range,ly.range,lv.range,lm.range;let lg=o2(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());lg.range;let lb=o2(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());lb.range;let lx=o2(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());lx.every=e=>isFinite(e=Math.floor(e))&&e>0?o2(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,lx.range;let lw=o2(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function lO(e,t,r,n,a,i){let o=[[o3,1,1e3],[o3,5,5e3],[o3,15,15e3],[o3,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[a,1,36e5],[a,3,108e5],[a,6,216e5],[a,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let a=Math.abs(r-t)/n,i=ik(([,,e])=>e).right(o,a);if(i===o.length)return e.every(ij(t/31536e6,r/31536e6,n));if(0===i)return o5.every(Math.max(ij(t,r,n),1));let[l,c]=o[a/o[i-1][2]<o[i][2]/a?i-1:i];return l.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let a=r&&"function"==typeof r.range?r:l(e,t,r),i=a?a.range(e,+t+1):[];return n?i.reverse():i},l]}lw.every=e=>isFinite(e=Math.floor(e))&&e>0?o2(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,lw.range;let[lP,lj]=lO(lw,lb,lf,lt,o7,o6),[lE,lS]=lO(lx,lg,ln,o9,o8,o4);function lk(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function lA(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function lM(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var lT={"-":"",_:" ",0:"0"},lC=/^\s*\d+/,lD=/^%/,lN=/[\\^$*+?|[\]().{}]/g;function lI(e,t,r){var n=e<0?"-":"",a=(n?-e:e)+"",i=a.length;return n+(i<r?Array(r-i+1).join(t)+a:a)}function l_(e){return e.replace(lN,"\\$&")}function lL(e){return RegExp("^(?:"+e.map(l_).join("|")+")","i")}function lR(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function lz(e,t,r){var n=lC.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function lK(e,t,r){var n=lC.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function lB(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function l$(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function lF(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function lU(e,t,r){var n=lC.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function lW(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function lV(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function lX(e,t,r){var n=lC.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function lH(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function lq(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function lY(e,t,r){var n=lC.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function lG(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function lZ(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function lJ(e,t,r){var n=lC.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function lQ(e,t,r){var n=lC.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function l0(e,t,r){var n=lC.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function l1(e,t,r){var n=lD.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function l2(e,t,r){var n=lC.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function l5(e,t,r){var n=lC.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function l3(e,t){return lI(e.getDate(),t,2)}function l4(e,t){return lI(e.getHours(),t,2)}function l6(e,t){return lI(e.getHours()%12||12,t,2)}function l8(e,t){return lI(1+o9.count(lx(e),e),t,3)}function l7(e,t){return lI(e.getMilliseconds(),t,3)}function l9(e,t){return l7(e,t)+"000"}function ce(e,t){return lI(e.getMonth()+1,t,2)}function ct(e,t){return lI(e.getMinutes(),t,2)}function cr(e,t){return lI(e.getSeconds(),t,2)}function cn(e){var t=e.getDay();return 0===t?7:t}function ca(e,t){return lI(ln.count(lx(e)-1,e),t,2)}function ci(e){var t=e.getDay();return t>=4||0===t?ll(e):ll.ceil(e)}function co(e,t){return e=ci(e),lI(ll.count(lx(e),e)+(4===lx(e).getDay()),t,2)}function cl(e){return e.getDay()}function cc(e,t){return lI(la.count(lx(e)-1,e),t,2)}function cs(e,t){return lI(e.getFullYear()%100,t,2)}function cu(e,t){return lI((e=ci(e)).getFullYear()%100,t,2)}function cf(e,t){return lI(e.getFullYear()%1e4,t,4)}function cd(e,t){var r=e.getDay();return lI((e=r>=4||0===r?ll(e):ll.ceil(e)).getFullYear()%1e4,t,4)}function cp(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+lI(t/60|0,"0",2)+lI(t%60,"0",2)}function ch(e,t){return lI(e.getUTCDate(),t,2)}function cy(e,t){return lI(e.getUTCHours(),t,2)}function cv(e,t){return lI(e.getUTCHours()%12||12,t,2)}function cm(e,t){return lI(1+le.count(lw(e),e),t,3)}function cg(e,t){return lI(e.getUTCMilliseconds(),t,3)}function cb(e,t){return cg(e,t)+"000"}function cx(e,t){return lI(e.getUTCMonth()+1,t,2)}function cw(e,t){return lI(e.getUTCMinutes(),t,2)}function cO(e,t){return lI(e.getUTCSeconds(),t,2)}function cP(e){var t=e.getUTCDay();return 0===t?7:t}function cj(e,t){return lI(lf.count(lw(e)-1,e),t,2)}function cE(e){var t=e.getUTCDay();return t>=4||0===t?ly(e):ly.ceil(e)}function cS(e,t){return e=cE(e),lI(ly.count(lw(e),e)+(4===lw(e).getUTCDay()),t,2)}function ck(e){return e.getUTCDay()}function cA(e,t){return lI(ld.count(lw(e)-1,e),t,2)}function cM(e,t){return lI(e.getUTCFullYear()%100,t,2)}function cT(e,t){return lI((e=cE(e)).getUTCFullYear()%100,t,2)}function cC(e,t){return lI(e.getUTCFullYear()%1e4,t,4)}function cD(e,t){var r=e.getUTCDay();return lI((e=r>=4||0===r?ly(e):ly.ceil(e)).getUTCFullYear()%1e4,t,4)}function cN(){return"+0000"}function cI(){return"%"}function c_(e){return+e}function cL(e){return Math.floor(e/1e3)}function cR(e){return new Date(e)}function cz(e){return e instanceof Date?+e:+new Date(+e)}function cK(e,t,r,n,a,i,o,l,c,s){var u=og(),f=u.invert,d=u.domain,p=s(".%L"),h=s(":%S"),y=s("%I:%M"),v=s("%I %p"),m=s("%a %d"),g=s("%b %d"),b=s("%B"),x=s("%Y");function w(e){return(c(e)<e?p:l(e)<e?h:o(e)<e?y:i(e)<e?v:n(e)<e?a(e)<e?m:g:r(e)<e?b:x)(e)}return u.invert=function(e){return new Date(f(e))},u.domain=function(e){return arguments.length?d(Array.from(e,cz)):d().map(cR)},u.ticks=function(t){var r=d();return e(r[0],r[r.length-1],null==t?10:t)},u.tickFormat=function(e,t){return null==t?w:s(t)},u.nice=function(e){var r=d();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?d(oD(r,e)):u},u.copy=function(){return ov(u,cK(e,t,r,n,a,i,o,l,c,s))},u}function cB(){return ic.apply(cK(lE,lS,lx,lg,ln,o9,o8,o4,o3,s).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function c$(){return ic.apply(cK(lP,lj,lw,lb,lf,le,o7,o6,o3,u).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cF(){var e,t,r,n,a,i=0,o=1,l=od,c=!1;function s(t){return null==t||isNaN(t*=1)?a:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function u(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),s):[l(0),l(1)]}}return s.domain=function(a){return arguments.length?([i,o]=a,e=n(i*=1),t=n(o*=1),r=e===t?0:1/(t-e),s):[i,o]},s.clamp=function(e){return arguments.length?(c=!!e,s):c},s.interpolator=function(e){return arguments.length?(l=e,s):l},s.range=u(oc),s.rangeRound=u(os),s.unknown=function(e){return arguments.length?(a=e,s):a},function(a){return n=a,e=a(i),t=a(o),r=e===t?0:1/(t-e),s}}function cU(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function cW(){var e=oX(cF());return e.copy=function(){return cU(e,cW()).exponent(e.exponent())},is.apply(e,arguments)}function cV(){return cW.apply(null,arguments).exponent(.5)}function cX(){var e,t,r,n,a,i,o,l=0,c=.5,s=1,u=1,f=od,d=!1;function p(e){return isNaN(e*=1)?o:(e=.5+((e=+i(e))-t)*(u*e<u*t?n:a),f(d?Math.max(0,Math.min(1,e)):e))}function h(e){return function(t){var r,n,a;return arguments.length?([r,n,a]=t,f=function(e,t){void 0===t&&(t=e,e=oc);for(var r=0,n=t.length-1,a=t[0],i=Array(n<0?0:n);r<n;)i[r]=e(a,a=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return i[t](e-t)}}(e,[r,n,a]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([l,c,s]=o,e=i(l*=1),t=i(c*=1),r=i(s*=1),n=e===t?0:.5/(t-e),a=t===r?0:.5/(r-t),u=t<e?-1:1,p):[l,c,s]},p.clamp=function(e){return arguments.length?(d=!!e,p):d},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=h(oc),p.rangeRound=h(os),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return i=o,e=o(l),t=o(c),r=o(s),n=e===t?0:.5/(t-e),a=t===r?0:.5/(r-t),u=t<e?-1:1,p}}function cH(){var e=oX(cX());return e.copy=function(){return cU(e,cH()).exponent(e.exponent())},is.apply(e,arguments)}function cq(){return cH.apply(null,arguments).exponent(.5)}s=(c=function(e){var t=e.dateTime,r=e.date,n=e.time,a=e.periods,i=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,s=lL(a),u=lR(a),f=lL(i),d=lR(i),p=lL(o),h=lR(o),y=lL(l),v=lR(l),m=lL(c),g=lR(c),b={a:function(e){return o[e.getDay()]},A:function(e){return i[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:l3,e:l3,f:l9,g:cu,G:cd,H:l4,I:l6,j:l8,L:l7,m:ce,M:ct,p:function(e){return a[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:c_,s:cL,S:cr,u:cn,U:ca,V:co,w:cl,W:cc,x:null,X:null,y:cs,Y:cf,Z:cp,"%":cI},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return i[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:ch,e:ch,f:cb,g:cT,G:cD,H:cy,I:cv,j:cm,L:cg,m:cx,M:cw,p:function(e){return a[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:c_,s:cL,S:cO,u:cP,U:cj,V:cS,w:ck,W:cA,x:null,X:null,y:cM,Y:cC,Z:cN,"%":cI},w={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=m.exec(t.slice(r));return n?(e.m=g.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return j(e,t,r,n)},d:lq,e:lq,f:l0,g:lW,G:lU,H:lG,I:lG,j:lY,L:lQ,m:lH,M:lZ,p:function(e,t,r){var n=s.exec(t.slice(r));return n?(e.p=u.get(n[0].toLowerCase()),r+n[0].length):-1},q:lX,Q:l2,s:l5,S:lJ,u:lK,U:lB,V:l$,w:lz,W:lF,x:function(e,t,n){return j(e,r,t,n)},X:function(e,t,r){return j(e,n,t,r)},y:lW,Y:lU,Z:lV,"%":l1};function O(e,t){return function(r){var n,a,i,o=[],l=-1,c=0,s=e.length;for(r instanceof Date||(r=new Date(+r));++l<s;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(a=lT[n=e.charAt(++l)])?n=e.charAt(++l):a="e"===n?" ":"0",(i=t[n])&&(n=i(r,a)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function P(e,t){return function(r){var n,a,i=lM(1900,void 0,1);if(j(i,e,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!t||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(a=(n=lA(lM(i.y,0,1))).getUTCDay())>4||0===a?ld.ceil(n):ld(n),n=le.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(a=(n=lk(lM(i.y,0,1))).getDay())>4||0===a?la.ceil(n):la(n),n=o9.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),a="Z"in i?lA(lM(i.y,0,1)).getUTCDay():lk(lM(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(a+5)%7:i.w+7*i.U-(a+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,lA(i)):lk(i)}}function j(e,t,r,n){for(var a,i,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return -1;if(37===(a=t.charCodeAt(o++))){if(!(i=w[(a=t.charAt(o++))in lT?t.charAt(o++):a])||(n=i(e,r,n))<0)return -1}else if(a!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=P(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=P(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,c.parse,u=c.utcFormat,c.utcParse;var cY=e=>e.chartData,cG=e3([cY],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),cZ=(e,t,r,n)=>n?cG(e):cY(e);function cJ(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if(ab(t)&&ab(r))return!0}return!1}function cQ(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var c0=r(8870),c1=r.n(c0),c2=e=>e,c5={},c3=e=>function t(){let r;return 0==arguments.length||1==arguments.length&&(r=arguments.length<=0?void 0:arguments[0],r===c5)?t:e(...arguments)},c4=(e,t)=>1===e?t:c3(function(){for(var r=arguments.length,n=Array(r),a=0;a<r;a++)n[a]=arguments[a];var i=n.filter(e=>e!==c5).length;return i>=e?t(...n):c4(e-i,c3(function(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return t(...n.map(e=>e===c5?r.shift():e),...r)}))}),c6=e=>c4(e.length,e),c8=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},c7=c6((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),c9=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return c2;var n=t.reverse(),a=n[0],i=n.slice(1);return function(){return i.reduce((e,t)=>t(e),a(...arguments))}},se=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),st=e=>{var t=null,r=null;return function(){for(var n=arguments.length,a=Array(n),i=0;i<n;i++)a[i]=arguments[i];return t&&a.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=a,r=e(...a))}};function sr(e){return 0===e?1:Math.floor(new(c1())(e).abs().log(10).toNumber())+1}function sn(e,t,r){for(var n=new(c1())(e),a=0,i=[];n.lt(t)&&a<1e5;)i.push(n.toNumber()),n=n.add(r),a++;return i}c6((e,t,r)=>{var n=+e;return n+r*(t-n)}),c6((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),c6((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var sa=e=>{var[t,r]=e,[n,a]=[t,r];return t>r&&([n,a]=[r,t]),[n,a]},si=(e,t,r)=>{if(e.lte(0))return new(c1())(0);var n=sr(e.toNumber()),a=new(c1())(10).pow(n),i=e.div(a),o=1!==n?.05:.1,l=new(c1())(Math.ceil(i.div(o).toNumber())).add(r).mul(o).mul(a);return new(c1())(t?l.toNumber():Math.ceil(l.toNumber()))},so=function(e,t,r,n){var a,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(c1())(0),tickMin:new(c1())(0),tickMax:new(c1())(0)};var o=si(new(c1())(t).sub(e).div(r-1),n,i),l=Math.ceil((a=e<=0&&t>=0?new(c1())(0):(a=new(c1())(e).add(t).div(2)).sub(new(c1())(a).mod(o))).sub(e).div(o).toNumber()),c=Math.ceil(new(c1())(t).sub(a).div(o).toNumber()),s=l+c+1;return s>r?so(e,t,r,n,i+1):(s<r&&(c=t>0?c+(r-s):c,l=t>0?l:l+(r-s)),{step:o,tickMin:a.sub(new(c1())(l).mul(o)),tickMax:a.add(new(c1())(c).mul(o))})},sl=st(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],i=Math.max(n,2),[o,l]=sa([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...c8(0,n-1).map(()=>1/0)]:[...c8(0,n-1).map(()=>-1/0),l];return t>r?se(c):c}if(o===l){var s=new(c1())(1),u=new(c1())(o);if(!u.isint()&&a){var f=Math.abs(o);f<1?(s=new(c1())(10).pow(sr(o)-1),u=new(c1())(Math.floor(u.div(s).toNumber())).mul(s)):f>1&&(u=new(c1())(Math.floor(o)))}else 0===o?u=new(c1())(Math.floor((n-1)/2)):a||(u=new(c1())(Math.floor(o)));var d=Math.floor((n-1)/2);return c9(c7(e=>u.add(new(c1())(e-d).mul(s)).toNumber()),c8)(0,n)}var{step:p,tickMin:h,tickMax:y}=so(o,l,i,a,0),v=sn(h,y.add(new(c1())(.1).mul(p)),p);return t>r?se(v):v}),sc=st(function(e,t){var[r,n]=e,a=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[i,o]=sa([r,n]);if(i===-1/0||o===1/0)return[r,n];if(i===o)return[i];var l=Math.max(t,2),c=si(new(c1())(o).sub(i).div(l-1),a,0),s=[...sn(new(c1())(i),new(c1())(o),c),o];return!1===a&&(s=s.map(e=>Math.round(e))),r>n?se(s):s}),ss=e=>e.rootProps.maxBarSize,su=e=>e.rootProps.barGap,sf=e=>e.rootProps.barCategoryGap,sd=e=>e.rootProps.barSize,sp=e=>e.rootProps.stackOffset,sh=e=>e.options.chartName,sy=e=>e.rootProps.syncId,sv=e=>e.rootProps.syncMethod,sm=e=>e.options.eventEmitter,sg={allowDuplicatedCategory:!0,angleAxisId:0,axisLine:!0,cx:0,cy:0,orientation:"outer",reversed:!1,scale:"auto",tick:!0,tickLine:!0,tickSize:8,type:"category"},sb={allowDataOverflow:!1,allowDuplicatedCategory:!0,angle:0,axisLine:!0,cx:0,cy:0,orientation:"right",radiusAxisId:0,scale:"auto",stroke:"#ccc",tick:!0,tickCount:5,type:"number"},sx=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t},sw={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:sg.angleAxisId,includeHidden:!1,name:void 0,reversed:sg.reversed,scale:sg.scale,tick:sg.tick,tickCount:void 0,ticks:void 0,type:sg.type,unit:void 0},sO={allowDataOverflow:sb.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:sb.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:sb.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:sb.scale,tick:sb.tick,tickCount:sb.tickCount,ticks:void 0,type:sb.type,unit:void 0},sP={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:sg.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:sg.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:sg.scale,tick:sg.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},sj={allowDataOverflow:sb.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:sb.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:sb.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:sb.scale,tick:sb.tick,tickCount:sb.tickCount,ticks:void 0,type:"category",unit:void 0},sE=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?sP:sw,sS=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?sj:sO,sk=e=>e.polarOptions,sA=e3([nt,nr,nd],rX),sM=e3([sk,sA],(e,t)=>{if(null!=e)return j(e.innerRadius,t,0)}),sT=e3([sk,sA],(e,t)=>{if(null!=e)return j(e.outerRadius,t,.8*t)}),sC=e3([sk],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]}),sD=e3([sE,sC],sx),sN=e3([sA,sM,sT],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]}),sI=e3([sS,sN],sx),s_=e3([nS,sk,sM,sT,nt,nr],(e,t,r,n,a,i)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:l,startAngle:c,endAngle:s}=t;return{cx:j(o,a,a/2),cy:j(l,i,i/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}}),sL=(e,t)=>t,sR=(e,t,r)=>r;function sz(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function sK(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?sz(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):sz(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var sB=[0,"auto"],s$={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},sF=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?s$:r},sU={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:sB,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:60},sW=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?sU:r},sV={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},sX=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?sV:r},sH=(e,t,r)=>{switch(t){case"xAxis":return sF(e,r);case"yAxis":return sW(e,r);case"zAxis":return sX(e,r);case"angleAxis":return sE(e,r);case"radiusAxis":return sS(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},sq=(e,t,r)=>{switch(t){case"xAxis":return sF(e,r);case"yAxis":return sW(e,r);case"angleAxis":return sE(e,r);case"radiusAxis":return sS(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},sY=e=>e.graphicalItems.countOfBars>0;function sG(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var sZ=e=>e.graphicalItems.cartesianItems,sJ=e3([sL,sR],sG),sQ=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),s0=e3([sZ,sH,sJ],sQ),s1=e=>e.filter(e=>void 0===e.stackId),s2=e3([s0],s1),s5=e=>e.map(e=>e.data).filter(Boolean).flat(1),s3=e3([s0],s5),s4=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:a}=t;return e.length>0?e:r.slice(n,a+1)},s6=e3([s3,cZ],s4),s8=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rG(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:rG(e,t)}))):e.map(e=>({value:e})),s7=e3([s6,sH,s0],s8);function s9(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function ue(e){return e.filter(e=>w(e)||e instanceof Date).map(Number).filter(e=>!1===g(e))}var ut=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,a]=t;return[n,{stackedData:((e,t,r)=>{var n=r1[r];return(function(){var e=ed([]),t=rK,r=rR,n=rB;function a(a){var i,o,l=Array.from(e.apply(this,arguments),r$),c=l.length,s=-1;for(let e of a)for(i=0,++s;i<c;++i)(l[i][s]=[0,+n(e,l[i].key,s,a)]).data=e;for(i=0,o=rz(t(l));i<c;++i)l[o[i]].index=i;return r(l,o),l}return a.keys=function(t){return arguments.length?(e="function"==typeof t?t:ed(Array.from(t)),a):e},a.value=function(e){return arguments.length?(n="function"==typeof e?e:ed(+e),a):n},a.order=function(e){return arguments.length?(t=null==e?rK:"function"==typeof e?e:ed(Array.from(e)),a):t},a.offset=function(e){return arguments.length?(r=null==e?rR:e,a):r},a})().keys(t).value((e,t)=>+rG(e,t,0)).order(rK).offset(n)(e)})(e,a.map(e=>e.dataKey),r),graphicalItems:a}]})),ur=e3([s6,s0,sp],ut),un=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:a}=t;if("zAxis"!==r){var i=((e,t,r)=>{if(null!=e)return(e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]])(Object.keys(e).reduce((n,a)=>{var{stackedData:i}=e[a],o=i.reduce((e,n)=>{var a=(e=>{var t=e.flat(2).filter(x);return[Math.min(...t),Math.max(...t)]})(n.slice(t,r+1));return[Math.min(e[0],a[0]),Math.max(e[1],a[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))})(e,n,a);if(null==i||0!==i[0]||0!==i[1])return i}},ua=e3([ur,cY,sL],un),ui=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var a,i,o=null==(a=r.errorBars)?void 0:a.filter(e=>s9(n,e)),l=rG(e,null!=(i=t.dataKey)?i:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||g(t)||!r.length?[]:ue(r.flatMap(r=>{var n,a,i=rG(e,r.dataKey);if(Array.isArray(i)?[n,a]=i:n=a=i,ab(n)&&ab(a))return[t-n,t+a]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rG(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),uo=e3(s6,sH,s2,sL,ui);function ul(e){var{value:t}=e;if(w(t)||t instanceof Date)return t}var uc=e=>{var t;if(null==e||!("domain"in e))return sB;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=ue(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:sB},us=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var a=n.flat();return[Math.min(...a),Math.max(...a)]}},uu=e=>e.referenceElements.dots,uf=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),ud=e3([uu,sL,sR],uf),up=e=>e.referenceElements.areas,uh=e3([up,sL,sR],uf),uy=e=>e.referenceElements.lines,uv=e3([uy,sL,sR],uf),um=(e,t)=>{var r=ue(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ug=e3(ud,sL,um),ub=(e,t)=>{var r=ue(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},ux=e3([uh,sL],ub),uw=(e,t)=>{var r=ue(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},uO=e3(uv,sL,uw),uP=e3(ug,uO,ux,(e,t,r)=>us(e,r,t)),uj=e3([sH],uc),uE=(e,t,r,n,a)=>{var i=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[a,i]=e;if(ab(a))r=a;else if("function"==typeof a)return;if(ab(i))n=i;else if("function"==typeof i)return;var o=[r,n];if(cJ(o))return o}}(t,e.allowDataOverflow);return null!=i?i:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(cJ(n))return cQ(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var a,i,[o,l]=e;if("auto"===o)null!=t&&(a=Math.min(...t));else if(x(o))a=o;else if("function"==typeof o)try{null!=t&&(a=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&r6.test(o)){var c=r6.exec(o);if(null==c||null==t)a=void 0;else{var s=+c[1];a=t[0]-s}}else a=null==t?void 0:t[0];if("auto"===l)null!=t&&(i=Math.max(...t));else if(x(l))i=l;else if("function"==typeof l)try{null!=t&&(i=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&r8.test(l)){var u=r8.exec(l);if(null==u||null==t)i=void 0;else{var f=+u[1];i=t[1]+f}}else i=null==t?void 0:t[1];var d=[a,i];if(cJ(d))return null==t?d:cQ(d,t,r)}}}(t,us(r,a,(e=>{var t=ue(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]})(n)),e.allowDataOverflow)},uS=e3([sH,uj,ua,uo,uP],uE),uk=[0,1],uA=(e,t,r,n,a,i,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:c}=e,s=rZ(t,i);return s&&null==l?il()(0,r.length):"category"===c?((e,t,r)=>{var n=e.map(ul).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&E(n))?il()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))})(n,e,s):"expand"===a?uk:o}},uM=e3([sH,nS,s6,s7,sp,sL,uS],uA),uT=(e,t,r,n,a)=>{if(null!=e){var{scale:i,type:o}=e;if("auto"===i)return"radial"===t&&"radiusAxis"===a?"band":"radial"===t&&"angleAxis"===a?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof i){var l="scale".concat(M(i));return l in f?l:"point"}}},uC=e3([sH,nS,sY,sh,sL],uT);function uD(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var a=function(e){if(null!=e){if(e in f)return f[e]();var t="scale".concat(M(e));if(t in f)return f[t]()}}(t);if(null!=a){var i=a.domain(r).range(n);return(e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),a=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<a||o>i||l<a||l>i)&&e.domain([t[0],t[r-1]])}})(i),i}}}var uN=(e,t,r)=>{var n=uc(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&cJ(e))return sl(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&cJ(e))return sc(e,t.tickCount,t.allowDecimals)}},uI=e3([uM,sq,uC],uN),u_=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&cJ(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,uL=e3([sH,uM,uI,sL],u_),uR=e3(s7,sH,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(ue(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var a=n[n.length-1]-n[0];if(0===a)return 1/0;for(var i=0;i<n.length-1;i++)r=Math.min(r,n[i+1]-n[i]);return r/a}}),uz=e3(uR,nS,sf,nd,(e,t,r,n)=>n,(e,t,r,n,a)=>{if(!ab(e))return 0;var i="vertical"===t?n.height:n.width;if("gap"===a)return e*i/2;if("no-gap"===a){var o=j(r,e*i),l=e*i/2;return l-o-(l-o)/i*o}return 0}),uK=e3(sF,(e,t)=>{var r=sF(e,t);return null==r||"string"!=typeof r.padding?0:uz(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:a}=e;return"string"==typeof a?{left:t,right:t}:{left:(null!=(r=a.left)?r:0)+t,right:(null!=(n=a.right)?n:0)+t}}),uB=e3(sW,(e,t)=>{var r=sW(e,t);return null==r||"string"!=typeof r.padding?0:uz(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:a}=e;return"string"==typeof a?{top:t,bottom:t}:{top:(null!=(r=a.top)?r:0)+t,bottom:(null!=(n=a.bottom)?n:0)+t}}),u$=e3([nd,uK,nb,ng,(e,t,r)=>r],(e,t,r,n,a)=>{var{padding:i}=n;return a?[i.left,r.width-i.right]:[e.left+t.left,e.left+e.width-t.right]}),uF=e3([nd,nS,uB,nb,ng,(e,t,r)=>r],(e,t,r,n,a,i)=>{var{padding:o}=a;return i?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),uU=(e,t,r,n)=>{var a;switch(t){case"xAxis":return u$(e,r,n);case"yAxis":return uF(e,r,n);case"zAxis":return null==(a=sX(e,r))?void 0:a.range;case"angleAxis":return sC(e);case"radiusAxis":return sN(e,r);default:return}},uW=e3([sH,uU],sx),uV=e3([sH,uC,uL,uW],uD);function uX(e,t){return e.id<t.id?-1:+(e.id>t.id)}e3(s0,sL,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>s9(t,e)));var uH=(e,t)=>t,uq=(e,t,r)=>r,uY=e3(ni,uH,uq,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(uX)),uG=e3(no,uH,uq,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(uX)),uZ=(e,t)=>({width:e.width,height:t.height}),uJ=e3(nd,sF,uZ),uQ=e3(nr,nd,uY,uH,uq,(e,t,r,n,a)=>{var i,o={};return r.forEach(r=>{var l=uZ(t,r);null==i&&(i=((e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}})(t,n,e));var c="top"===n&&!a||"bottom"===n&&a;o[r.id]=i-Number(c)*l.height,i+=(c?-1:1)*l.height}),o}),u0=e3(nt,nd,uG,uH,uq,(e,t,r,n,a)=>{var i,o={};return r.forEach(r=>{var l=((e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height}))(t,r);null==i&&(i=((e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}})(t,n,e));var c="left"===n&&!a||"right"===n&&a;o[r.id]=i-Number(c)*l.width,i+=(c?-1:1)*l.width}),o}),u1=e3(nd,sW,(e,t)=>({width:"number"==typeof t.width?t.width:60,height:e.height})),u2=(e,t,r)=>{switch(t){case"xAxis":return uJ(e,r).width;case"yAxis":return u1(e,r).height;default:return}},u5=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:a,type:i,dataKey:o}=r,l=rZ(e,n),c=t.map(e=>e.value);if(o&&l&&"category"===i&&a&&E(c))return c}},u3=e3([nS,s7,sH,sL],u5),u4=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:a,scale:i}=r;if(rZ(e,n)&&("number"===a||"auto"!==i))return t.map(e=>e.value)}},u6=e3([nS,s7,sq,sL],u4),u8=e3([nS,(e,t,r)=>{switch(t){case"xAxis":return sF(e,r);case"yAxis":return sW(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},uC,uV,u3,u6,uU,uI,sL],(e,t,r,n,a,i,o,l,c)=>{if(null==t)return null;var s=rZ(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:i,duplicateDomain:a,isCategorical:s,niceTicks:l,range:o,realScaleType:r,scale:n}}),u7=(e,t,r,n,a,i,o,l,c)=>{if(null!=t&&null!=n){var s=rZ(e,c),{type:u,ticks:f,tickCount:d}=t,p="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,h="category"===u&&n.bandwidth?n.bandwidth()/p:0;h="angleAxis"===c&&null!=i&&i.length>=2?2*m(i[0]-i[1])*h:h;var y=f||a;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+h,value:e,offset:h})).filter(e=>!g(e.coordinate)):s&&l?l.map((e,t)=>({coordinate:n(e)+h,value:e,index:t,offset:h})):n.ticks?n.ticks(d).map(e=>({coordinate:n(e)+h,value:e,offset:h})):n.domain().map((e,t)=>({coordinate:n(e)+h,value:o?o[e]:e,index:t,offset:h}))}},u9=e3([nS,sq,uC,uV,uI,uU,u3,u6,sL],u7),fe=(e,t,r,n,a,i,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=rZ(e,o),{tickCount:c}=t,s=0;return(s="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*m(n[0]-n[1])*s:s,l&&i)?i.map((e,t)=>({coordinate:r(e)+s,value:e,index:t,offset:s})):r.ticks?r.ticks(c).map(e=>({coordinate:r(e)+s,value:e,offset:s})):r.domain().map((e,t)=>({coordinate:r(e)+s,value:a?a[e]:e,index:t,offset:s}))}},ft=e3([nS,sq,uV,uU,u3,u6,sL],fe),fr=e3(sH,uV,(e,t)=>{if(null!=e&&null!=t)return sK(sK({},e),{},{scale:t})}),fn=e3([sH,uC,uM,uW],uD),fa=e3((e,t,r)=>sX(e,r),fn,(e,t)=>{if(null!=e&&null!=t)return sK(sK({},e),{},{scale:t})}),fi=e3([nS,ni,no],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}}),fo=e=>e.options.defaultTooltipEventType,fl=e=>e.options.validateTooltipEventTypes;function fc(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function fs(e,t){return fc(t,fo(e),fl(e))}var fu=(e,t)=>{var r,n=Number(t);if(!g(n)&&null!=t)return n>=0?null==e||null==(r=e[n])?void 0:r.value:void 0},ff={active:!1,index:null,dataKey:void 0,coordinate:void 0},fd=t8({name:"tooltip",initialState:{itemInteraction:{click:ff,hover:ff},axisInteraction:{click:ff,hover:ff},keyboardInteraction:ff,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push(t.payload)},removeTooltipEntrySettings(e,t){var r=tY(e).tooltipItemPayloads.indexOf(t.payload);r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:fp,removeTooltipEntrySettings:fh,setTooltipSettingsState:fy,setActiveMouseOverItemIndex:fv,mouseLeaveItem:fm,mouseLeaveChart:fg,setActiveClickItemIndex:fb,setMouseOverAxisIndex:fx,setMouseClickAxisIndex:fw,setSyncInteraction:fO,setKeyboardInteraction:fP}=fd.actions,fj=fd.reducer;function fE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fE(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var fk=(e,t,r,n)=>{if(null==t)return ff;var a=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==a)return ff;if(a.active)return a;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var i=!0===e.settings.active;if(null!=a.index){if(i)return fS(fS({},a),{},{active:!0})}else if(null!=n)return{active:!0,coordinate:void 0,dataKey:void 0,index:n};return fS(fS({},ff),{},{coordinate:a.coordinate})},fA=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var n=Number(r);if(!ab(n))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(n,a)))},fM=(e,t,r,n,a,i,o,l)=>{if(null!=i&&null!=l){var c=o[0],s=null==c?void 0:l(c.positions,i);if(null!=s)return s;var u=null==a?void 0:a[Number(i)];if(u)if("horizontal"===r)return{x:u.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:u.coordinate}}},fT=(e,t,r,n)=>{var a;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(a="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===a})},fC=e=>e.options.tooltipPayloadSearcher,fD=e=>e.tooltip;function fN(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function fI(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?fN(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):fN(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f_=(e,t,r,n,a,i,o)=>{if(null!=t&&null!=i){var{chartData:l,computedData:c,dataStartIndex:s,dataEndIndex:u}=r;return e.reduce((e,r)=>{var f,d,p,h,y,{dataDefinedOnItem:v,settings:m}=r,g=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((f=v,d=l,null!=f?f:d),s,u),b=null!=(p=null==m?void 0:m.dataKey)?p:null==n?void 0:n.dataKey,x=null==m?void 0:m.nameKey;return Array.isArray(h=null!=n&&n.dataKey&&Array.isArray(g)&&!Array.isArray(g[0])&&"axis"===o?A(g,n.dataKey,a):i(g,t,c,x))?h.forEach(t=>{var r=fI(fI({},m),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push(r9({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:rG(t.payload,t.dataKey),name:t.name}))}):e.push(r9({tooltipEntrySettings:m,dataKey:b,payload:h,value:rG(h,b),name:null!=(y=rG(h,x))?y:null==m?void 0:m.name})),e},[])}},fL=e=>{var t=nS(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},fR=e=>e.tooltip.settings.axisId,fz=e=>{var t=fL(e),r=fR(e);return sq(e,t,r)},fK=e3([fz,nS,sY,sh,fL],uT),fB=e3([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),f$=e3([fL,fR],sG),fF=e3([fB,fz,f$],sQ),fU=e3([fF],s5),fW=e3([fU,cY],s4),fV=e3([fW,fz,fF],s8),fX=e3([fz],uc),fH=e3([fW,fF,sp],ut),fq=e3([fH,cY,fL],un),fY=e3([fF],s1),fG=e3([fW,fz,fY,fL],ui),fZ=e3([uu,fL,fR],uf),fJ=e3([fZ,fL],um),fQ=e3([up,fL,fR],uf),f0=e3([fQ,fL],ub),f1=e3([uy,fL,fR],uf),f2=e3([f1,fL],uw),f5=e3([fJ,f2,f0],us),f3=e3([fz,fX,fq,fG,f5],uE),f4=e3([fz,nS,fW,fV,sp,fL,f3],uA),f6=e3([f4,fz,fK],uN),f8=e3([fz,f4,f6,fL],u_),f7=e=>{var t=fL(e),r=fR(e);return uU(e,t,r,!1)},f9=e3([fz,f7],sx),de=e3([fz,fK,f8,f9],uD),dt=e3([nS,fV,fz,fL],u5),dr=e3([nS,fV,fz,fL],u4),dn=e3([nS,fz,fK,de,f7,dt,dr,fL],(e,t,r,n,a,i,o,l)=>{if(t){var{type:c}=t,s=rZ(e,l);if(n){var u="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,f="category"===c&&n.bandwidth?n.bandwidth()/u:0;return(f="angleAxis"===l&&null!=a&&(null==a?void 0:a.length)>=2?2*m(a[0]-a[1])*f:f,s&&o)?o.map((e,t)=>({coordinate:n(e)+f,value:e,index:t,offset:f})):n.domain().map((e,t)=>({coordinate:n(e)+f,value:i?i[e]:e,index:t,offset:f}))}}}),da=e3([fo,fl,e=>e.tooltip.settings],(e,t,r)=>fc(r.shared,e,t)),di=e=>e.tooltip.settings.trigger,dl=e=>e.tooltip.settings.defaultIndex,dc=e3([fD,da,di,dl],fk),ds=e3([dc,fW],fA),du=e3([dn,ds],fu),df=e3([dc],e=>{if(e)return e.dataKey}),dd=e3([fD,da,di,dl],fT),dp=e3([nt,nr,nS,nd,dn,dl,dd,fC],fM),dh=e3([dc,dp],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),dy=e3([dc],e=>e.active),dv=e3([dd,ds,cY,fz,du,fC,da],f_),dm=e3([dv],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))});function dg(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function db(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dg(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dg(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var dx=()=>eB(sh),dw=(e,t)=>t,dO=(e,t,r)=>r,dP=(e,t,r,n)=>n,dj=e3(dn,e=>e8()(e,e=>e.coordinate)),dE=e3([fD,dw,dO,dP],fk),dS=e3([dE,fW],fA),dk=e3([fD,dw,dO,dP],fT),dA=e3([nt,nr,nS,nd,dn,dP,dk,fC],fM),dM=e3([dE,dA],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),dT=e3(dn,dS,fu),dC=e3([dk,dS,cY,fz,dT,fC,dw],f_),dD=e3([dE],e=>({isActive:e.active,activeIndex:e.index}));function dN(){return(dN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function dI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dI(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function dL(e){var t,r,n,{coordinate:a,payload:i,index:o,offset:l,tooltipAxisBandSize:c,layout:s,cursor:u,tooltipEventType:f,chartName:h}=e;if(!u||!a||"ScatterChart"!==h&&"axis"!==f)return null;if("ScatterChart"===h)r=a,n=aD;else if("BarChart"===h)t=c/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===s?a.x-t:l.left+.5,y:"horizontal"===s?l.top+.5:a.y-t,width:"horizontal"===s?c:l.width-1,height:"horizontal"===s?l.height-1:c},n=a7;else if("radial"===s){var{cx:y,cy:v,radius:m,startAngle:g,endAngle:b}=a9(a);r={cx:y,cy:v,startAngle:g,endAngle:b,innerRadius:m,outerRadius:m},n=ii}else r={points:function(e,t,r){var n,a,i,o;if("horizontal"===e)i=n=t.x,a=r.top,o=r.top+r.height;else if("vertical"===e)o=a=t.y,n=r.left,i=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return a9(t);else{var{cx:l,cy:c,innerRadius:s,outerRadius:u,angle:f}=t,d=rV(l,c,s,f),p=rV(l,c,u,f);n=d.x,a=d.y,i=p.x,o=p.y}return[{x:n,y:a},{x:i,y:o}]}(s,a,l)},n=aA;var x="object"==typeof u&&"className"in u?u.className:void 0,w=d_(d_(d_(d_({stroke:"#ccc",pointerEvents:"none"},l),r),F(u,!1)),{},{payload:i,payloadIndex:o,className:(0,p.$)("recharts-tooltip-cursor",x)});return(0,d.isValidElement)(u)?(0,d.cloneElement)(u,w):(0,d.createElement)(n,w)}function dR(e){var t,r,n,a=(t=eB(fz),r=eB(dn),n=eB(de),r7(db(db({},t),{},{scale:n}),r)),i=nO(),o=nk(),l=dx();return d.createElement(dL,dN({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:i,layout:o,tooltipAxisBandSize:a,chartName:l}))}var dz=(0,d.createContext)(null),dK=new(r(82661)),dB="recharts.syncEvent.tooltip",d$="recharts.syncEvent.brush";function dF(e,t){if(t){var r=Number.parseInt(t,10);if(!g(r))return null==e?void 0:e[r]}}var dU=t8({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),dW=dU.reducer,{createEventEmitter:dV}=dU.actions;function dX(e){return e.tooltip.syncInteraction}var dH=t8({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:dq,setDataStartEndIndexes:dY,setComputedData:dG}=dH.actions,dZ=dH.reducer,dJ=()=>{};function dQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?dQ(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):dQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d1(e){return e.dataKey}var d2=[],d5={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!n4.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function d3(e){var t,r,n,a,i,o,l,c=aI(e,d5),{active:s,allowEscapeViewBox:u,animationDuration:f,animationEasing:p,content:h,filterNull:y,isAnimationActive:v,offset:m,payloadUniqBy:g,position:b,reverseDirection:x,useTranslate3d:w,wrapperStyle:O,cursor:P,shared:j,trigger:E,defaultIndex:S,portal:k,axisId:A}=c,M=eL(),T="number"==typeof S?String(S):S;(0,d.useEffect)(()=>{M(fy({shared:j,trigger:E,axisId:A,active:s,defaultIndex:T}))},[M,j,E,A,s,T]);var C=nx(),D=n6(),N=eB(e=>fs(e,j)),{activeIndex:I,isActive:_}=eB(e=>dD(e,N,E,T)),L=eB(e=>dC(e,N,E,T)),R=eB(e=>dT(e,N,E,T)),z=eB(e=>dM(e,N,E,T)),K=(0,d.useContext)(dz),B=null!=s?s:_,[$,F]=te([L,B]),U="axis"===N?R:void 0;t=eB(e=>((e,t,r)=>{if(null!=t){var n=fD(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}})(e,N,E)),r=eB(sm),n=eB(sy),a=eB(sv),o=null==(i=eB(dX))?void 0:i.active,(0,d.useEffect)(()=>{if(!o&&null!=n&&null!=r){var e=fO({active:B,coordinate:z,dataKey:t,index:I,label:"number"==typeof U?String(U):U});dK.emit(dB,n,e,r)}},[o,z,t,I,U,r,n,a,B]);var W=null!=k?k:K;if(null==W)return null;var V=null!=L?L:d2;B||(V=d2),y&&V.length&&(V=eD(L.filter(e=>null!=e.value&&(!0!==e.hide||c.includeHidden)),g,d1));var X=V.length>0,H=d.createElement(n3,{allowEscapeViewBox:u,animationDuration:f,animationEasing:p,isAnimationActive:v,active:B,coordinate:z,hasPayload:X,offset:m,position:b,reverseDirection:x,useTranslate3d:w,viewBox:C,wrapperStyle:O,lastBoundingBox:$,innerRef:F,hasPortalFromProps:!!k},(l=d0(d0({},c),{},{payload:V,label:U,active:B,coordinate:z,accessibilityLayer:D}),d.isValidElement(h)?d.cloneElement(h,l):"function"==typeof h?d.createElement(h,l):d.createElement(nZ,l)));return d.createElement(d.Fragment,null,(0,Y.createPortal)(H,W),B&&d.createElement(dR,{cursor:P,tooltipEventType:N,coordinate:z,payload:L,index:I}))}var d4=r(20400),d6=r.n(d4),d8=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),a=2;a<r;a++)n[a-2]=arguments[a]};function d7(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d9(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d7(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d7(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var pe=(0,d.forwardRef)((e,t)=>{var{aspect:r,initialDimension:n={width:-1,height:-1},width:a="100%",height:i="100%",minWidth:o=0,minHeight:l,maxHeight:c,children:s,debounce:u=0,id:f,className:h,onResize:y,style:v={}}=e,m=(0,d.useRef)(null),g=(0,d.useRef)();g.current=y,(0,d.useImperativeHandle)(t,()=>m.current);var[x,w]=(0,d.useState)({containerWidth:n.width,containerHeight:n.height}),O=(0,d.useCallback)((e,t)=>{w(r=>{var n=Math.round(e),a=Math.round(t);return r.containerWidth===n&&r.containerHeight===a?r:{containerWidth:n,containerHeight:a}})},[]);(0,d.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;O(r,n),null==(t=g.current)||t.call(g,r,n)};u>0&&(e=d6()(e,u,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=m.current.getBoundingClientRect();return O(r,n),t.observe(m.current),()=>{t.disconnect()}},[O,u]);var P=(0,d.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=x;if(e<0||t<0)return null;d8(b(a)||b(i),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",a,i),d8(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=b(a)?e:a,u=b(i)?t:i;return r&&r>0&&(n?u=n/r:u&&(n=u*r),c&&u>c&&(u=c)),d8(n>0||u>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,u,a,i,o,l,r),d.Children.map(s,e=>(0,d.cloneElement)(e,{width:n,height:u,style:d9({width:n,height:u},e.props.style)}))},[r,s,i,c,l,o,x,a]);return d.createElement("div",{id:f?"".concat(f):void 0,className:(0,p.$)("recharts-responsive-container",h),style:d9(d9({},v),{},{width:a,height:i,minWidth:o,minHeight:l,maxHeight:c}),ref:m},d.createElement("div",{style:{width:0,height:0,overflow:"visible"}},P))}),pt=e=>null;function pr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pr(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pr(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}pt.displayName="Cell";var pa={widthCache:{},cacheCount:0},pi={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},po="recharts_measurement_span",pl=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n4.isSsr)return{width:0,height:0};var n=(Object.keys(t=pn({},r)).forEach(e=>{t[e]||delete t[e]}),t),a=JSON.stringify({text:e,copyStyle:n});if(pa.widthCache[a])return pa.widthCache[a];try{var i=document.getElementById(po);i||((i=document.createElement("span")).setAttribute("id",po),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var o=pn(pn({},pi),n);Object.assign(i.style,o),i.textContent="".concat(e);var l=i.getBoundingClientRect(),c={width:l.width,height:l.height};return pa.widthCache[a]=c,++pa.cacheCount>2e3&&(pa.cacheCount=0,pa.widthCache={}),c}catch(e){return{width:0,height:0}}},pc=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,ps=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,pu=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,pf=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,pd={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},pp=Object.keys(pd);class ph{static parse(e){var t,[,r,n]=null!=(t=pf.exec(e))?t:[];return new ph(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new ph(NaN,""):new ph(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new ph(NaN,""):new ph(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new ph(NaN,""):new ph(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new ph(NaN,""):new ph(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return g(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,g(e)&&(this.unit=""),""===t||pu.test(t)||(this.num=NaN,this.unit=""),pp.includes(t)&&(this.num=e*pd[t],this.unit="px")}}function py(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,a,i]=null!=(r=pc.exec(t))?r:[],o=ph.parse(null!=n?n:""),l=ph.parse(null!=i?i:""),c="*"===a?o.multiply(l):o.divide(l);if(c.isNaN())return"NaN";t=t.replace(pc,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var s,[,u,f,d]=null!=(s=ps.exec(t))?s:[],p=ph.parse(null!=u?u:""),h=ph.parse(null!=d?d:""),y="+"===f?p.add(h):p.subtract(h);if(y.isNaN())return"NaN";t=t.replace(ps,y.toString())}return t}var pv=/\(([^()]*)\)/;function pm(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=pv.exec(r));){var[,n]=t;r=r.replace(pv,py(n))}return r}(t),t=py(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var pg=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],pb=["dx","dy","angle","className","breakAll"];function px(){return(px=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function pw(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var pO=/[ \f\n\r\t\v\u2028\u2029]+/,pP=e=>{var{children:t,breakAll:r,style:n}=e;try{let e;var a=[];e=t,null==e||(a=r?t.toString().split(""):t.toString().split(pO));var i=a.map(e=>({word:e,width:pl(e,n).width})),o=r?0:pl("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:o}}catch(e){return null}},pj=e=>[{words:null==e?[]:e.toString().split(pO)}],pE="#808080",pS=(0,d.forwardRef)((e,t)=>{var r,{x:n=0,y:a=0,lineHeight:i="1em",capHeight:o="0.71em",scaleToFit:l=!1,textAnchor:c="start",verticalAnchor:s="end",fill:u=pE}=e,f=pw(e,pg),h=(0,d.useMemo)(()=>(e=>{var{width:t,scaleToFit:r,children:n,style:a,breakAll:i,maxLines:o}=e;if((t||r)&&!n4.isSsr){var l=pP({breakAll:i,children:n,style:a});if(!l)return pj(n);var{wordsWithComputedWidth:c,spaceWidth:s}=l;return((e,t,r,n,a)=>{var i,{maxLines:o,children:l,style:c,breakAll:s}=e,u=x(o),f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:i,width:o}=t,l=e[e.length-1];return l&&(null==n||a||l.width+o+r<Number(n))?(l.words.push(i),l.width+=o+r):e.push({words:[i],width:o}),e},[])},d=f(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!u||a||!(d.length>o||p(d).width>Number(n)))return d;for(var h=e=>{var t=f(pP({breakAll:s,style:c,children:l.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>o||p(t).width>Number(n),t]},y=0,v=l.length-1,m=0;y<=v&&m<=l.length-1;){var g=Math.floor((y+v)/2),[b,w]=h(g-1),[O]=h(g);if(b||O||(y=g+1),b&&O&&(v=g-1),!b&&O){i=w;break}m++}return i||d})({breakAll:i,children:n,maxLines:o,style:a},c,s,t,r)}return pj(n)})({breakAll:f.breakAll,children:f.children,maxLines:f.maxLines,scaleToFit:l,style:f.style,width:f.width}),[f.breakAll,f.children,f.maxLines,l,f.style,f.width]),{dx:y,dy:v,angle:m,className:g,breakAll:b}=f,O=pw(f,pb);if(!w(n)||!w(a))return null;var P=n+(x(y)?y:0),j=a+(x(v)?v:0);switch(s){case"start":r=pm("calc(".concat(o,")"));break;case"middle":r=pm("calc(".concat((h.length-1)/2," * -").concat(i," + (").concat(o," / 2))"));break;default:r=pm("calc(".concat(h.length-1," * -").concat(i,")"))}var E=[];if(l){var S=h[0].width,{width:k}=f;E.push("scale(".concat(x(k)?k/S:1,")"))}return m&&E.push("rotate(".concat(m,", ").concat(P,", ").concat(j,")")),E.length&&(O.transform=E.join(" ")),d.createElement("text",px({},F(O,!0),{ref:t,x:P,y:j,className:(0,p.$)("recharts-text",g),textAnchor:c,fill:u.includes("url")?pE:u}),h.map((e,t)=>{var n=e.words.join(b?"":" ");return d.createElement("tspan",{x:P,dy:0===t?r:i,key:"".concat(n,"-").concat(t)},n)}))});pS.displayName="Text";var pk=["offset"],pA=["labelRef"];function pM(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function pT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pC(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?pT(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):pT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pD(){return(pD=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var pN=e=>null!=e&&"function"==typeof e;function pI(e){var t,{offset:r=5}=e,n=pC({offset:r},pM(e,pk)),{viewBox:a,position:i,value:o,children:l,content:c,className:s="",textBreakAll:u,labelRef:f}=n,h=nx(),y=a||h;if(!y||null==o&&null==l&&!(0,d.isValidElement)(c)&&"function"!=typeof c)return null;if((0,d.isValidElement)(c)){var{labelRef:v}=n,g=pM(n,pA);return(0,d.cloneElement)(c,g)}if("function"==typeof c){if(t=(0,d.createElement)(c,n),(0,d.isValidElement)(t))return t}else t=(e=>{var{value:t,formatter:r}=e,n=null==e.children?t:e.children;return"function"==typeof r?r(n):n})(n);var w="cx"in y&&x(y.cx),O=F(n,!0);if(w&&("insideStart"===i||"insideEnd"===i||"end"===i))return((e,t,r)=>{let n,a;var i,o,{position:l,viewBox:c,offset:s,className:u}=e,{cx:f,cy:h,innerRadius:y,outerRadius:v,startAngle:g,endAngle:b,clockWise:x}=c,w=(y+v)/2,O=(n=g,m((a=b)-n)*Math.min(Math.abs(a-n),360)),j=O>=0?1:-1;"insideStart"===l?(i=g+j*s,o=x):"insideEnd"===l?(i=b-j*s,o=!x):"end"===l&&(i=b+j*s,o=x),o=O<=0?o:!o;var E=rV(f,h,w,i),S=rV(f,h,w,i+(o?1:-1)*359),k="M".concat(E.x,",").concat(E.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(+!o,",\n    ").concat(S.x,",").concat(S.y),A=null==e.id?P("recharts-radial-line-"):e.id;return d.createElement("text",pD({},r,{dominantBaseline:"central",className:(0,p.$)("recharts-radial-bar-label",u)}),d.createElement("defs",null,d.createElement("path",{id:A,d:k})),d.createElement("textPath",{xlinkHref:"#".concat(A)},t))})(n,t,O);var E=w?(e=>{var{viewBox:t,offset:r,position:n}=e,{cx:a,cy:i,innerRadius:o,outerRadius:l,startAngle:c,endAngle:s}=t,u=(c+s)/2;if("outside"===n){var{x:f,y:d}=rV(a,i,l+r,u);return{x:f,y:d,textAnchor:f>=a?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:a,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:a,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:a,y:i,textAnchor:"middle",verticalAnchor:"end"};var{x:p,y:h}=rV(a,i,(o+l)/2,u);return{x:p,y:h,textAnchor:"middle",verticalAnchor:"middle"}})(n):((e,t)=>{var{parentViewBox:r,offset:n,position:a}=e,{x:i,y:o,width:l,height:c}=t,s=c>=0?1:-1,u=s*n,f=s>0?"end":"start",d=s>0?"start":"end",p=l>=0?1:-1,h=p*n,y=p>0?"end":"start",v=p>0?"start":"end";if("top"===a)return pC(pC({},{x:i+l/2,y:o-s*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(o-r.y,0),width:l}:{});if("bottom"===a)return pC(pC({},{x:i+l/2,y:o+c+u,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(o+c),0),width:l}:{});if("left"===a){var m={x:i-h,y:o+c/2,textAnchor:y,verticalAnchor:"middle"};return pC(pC({},m),r?{width:Math.max(m.x-r.x,0),height:c}:{})}if("right"===a){var g={x:i+l+h,y:o+c/2,textAnchor:v,verticalAnchor:"middle"};return pC(pC({},g),r?{width:Math.max(r.x+r.width-g.x,0),height:c}:{})}var w=r?{width:l,height:c}:{};return"insideLeft"===a?pC({x:i+h,y:o+c/2,textAnchor:v,verticalAnchor:"middle"},w):"insideRight"===a?pC({x:i+l-h,y:o+c/2,textAnchor:y,verticalAnchor:"middle"},w):"insideTop"===a?pC({x:i+l/2,y:o+u,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===a?pC({x:i+l/2,y:o+c-u,textAnchor:"middle",verticalAnchor:f},w):"insideTopLeft"===a?pC({x:i+h,y:o+u,textAnchor:v,verticalAnchor:d},w):"insideTopRight"===a?pC({x:i+l-h,y:o+u,textAnchor:y,verticalAnchor:d},w):"insideBottomLeft"===a?pC({x:i+h,y:o+c-u,textAnchor:v,verticalAnchor:f},w):"insideBottomRight"===a?pC({x:i+l-h,y:o+c-u,textAnchor:y,verticalAnchor:f},w):a&&"object"==typeof a&&(x(a.x)||b(a.x))&&(x(a.y)||b(a.y))?pC({x:i+j(a.x,l),y:o+j(a.y,c),textAnchor:"end",verticalAnchor:"end"},w):pC({x:i+l/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},w)})(n,y);return d.createElement(pS,pD({ref:f,className:(0,p.$)("recharts-label",s)},O,E,{breakAll:u}),t)}pI.displayName="Label";var p_=e=>{var{cx:t,cy:r,angle:n,startAngle:a,endAngle:i,r:o,radius:l,innerRadius:c,outerRadius:s,x:u,y:f,top:d,left:p,width:h,height:y,clockWise:v,labelViewBox:m}=e;if(m)return m;if(x(h)&&x(y)){if(x(u)&&x(f))return{x:u,y:f,width:h,height:y};if(x(d)&&x(p))return{x:d,y:p,width:h,height:y}}return x(u)&&x(f)?{x:u,y:f,width:0,height:0}:x(t)&&x(r)?{cx:t,cy:r,startAngle:a||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:s||l||o||0,clockWise:v}:e.viewBox?e.viewBox:void 0};pI.parseViewBox=p_,pI.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:n,labelRef:a}=e,i=p_(e),o=B(n,pI).map((e,r)=>(0,d.cloneElement)(e,{viewBox:t||i,key:"label-".concat(r)}));return r?[((e,t,r)=>{if(!e)return null;var n={viewBox:t,labelRef:r};return!0===e?d.createElement(pI,pD({key:"label-implicit"},n)):w(e)?d.createElement(pI,pD({key:"label-implicit",value:e},n)):(0,d.isValidElement)(e)?e.type===pI?(0,d.cloneElement)(e,pC({key:"label-implicit"},n)):d.createElement(pI,pD({key:"label-implicit",content:e},n)):pN(e)?d.createElement(pI,pD({key:"label-implicit",content:e},n)):e&&"object"==typeof e?d.createElement(pI,pD({},e,{key:"label-implicit"},n)):null})(e.label,t||i,a),...o]:o};var pL=r(58080),pR=r.n(pL),pz=["valueAccessor"],pK=["data","dataKey","clockWise","id","textBreakAll"];function pB(){return(pB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function pF(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p$(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function pU(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var pW=e=>Array.isArray(e.value)?pR()(e.value):e.value;function pV(e){var{valueAccessor:t=pW}=e,r=pU(e,pz),{data:n,dataKey:a,clockWise:i,id:o,textBreakAll:l}=r,c=pU(r,pK);return n&&n.length?d.createElement(q,{className:"recharts-label-list"},n.map((e,r)=>{var n=null==a?t(e,r):rG(e&&e.payload,a),s=null==o?{}:{id:"".concat(o,"-").concat(r)};return d.createElement(pI,pB({},F(e,!0),c,s,{parentViewBox:e.parentViewBox,value:n,textBreakAll:l,viewBox:pI.parseViewBox(null==i?e:pF(pF({},e),{},{clockWise:i})),key:"label-".concat(r),index:r}))})):null}pV.displayName="LabelList",pV.renderCallByParent=function(e,t){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&n&&!e.label)return null;var{children:a}=e,i=B(a,pV).map((e,r)=>(0,d.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return n?[(r=e.label,r?!0===r?d.createElement(pV,{key:"labelList-implicit",data:t}):d.isValidElement(r)||pN(r)?d.createElement(pV,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?d.createElement(pV,pB({data:t},r,{key:"labelList-implicit"})):null:null),...i]:i};var pX=["component"];function pH(e){var t,{component:r}=e,n=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,pX);return(0,d.isValidElement)(r)?t=(0,d.cloneElement)(r,n):"function"==typeof r?t=(0,d.createElement)(r,n):d8(!1,"Customized's props `component` must be React.element or Function, but got %s.",typeof r),d.createElement(q,{className:"recharts-customized-wrapper"},t)}pH.displayName="Customized";var pq=["points","className","baseLinePoints","connectNulls"];function pY(){return(pY=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var pG=e=>e&&e.x===+e.x&&e.y===+e.y,pZ=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=[[]];return e.forEach(e=>{pG(e)?t[t.length-1].push(e):t[t.length-1].length>0&&t.push([])}),pG(e[0])&&t[t.length-1].push(e[0]),t[t.length-1].length<=0&&(t=t.slice(0,-1)),t},pJ=(e,t)=>{var r=pZ(e);t&&(r=[r.reduce((e,t)=>[...e,...t],[])]);var n=r.map(e=>e.reduce((e,t,r)=>"".concat(e).concat(0===r?"M":"L").concat(t.x,",").concat(t.y),"")).join("");return 1===r.length?"".concat(n,"Z"):n},pQ=e=>{var{points:t,className:r,baseLinePoints:n,connectNulls:a}=e,i=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,pq);if(!t||!t.length)return null;var o=(0,p.$)("recharts-polygon",r);if(n&&n.length){var l=i.stroke&&"none"!==i.stroke,c=((e,t,r)=>{var n=pJ(e,r);return"".concat("Z"===n.slice(-1)?n.slice(0,-1):n,"L").concat(pJ(t.reverse(),r).slice(1))})(t,n,a);return d.createElement("g",{className:o},d.createElement("path",pY({},F(i,!0),{fill:"Z"===c.slice(-1)?i.fill:"none",stroke:"none",d:c})),l?d.createElement("path",pY({},F(i,!0),{fill:"none",d:pJ(t,a)})):null,l?d.createElement("path",pY({},F(i,!0),{fill:"none",d:pJ(n,a)})):null)}var s=pJ(t,a);return d.createElement("path",pY({},F(i,!0),{fill:"Z"===s.slice(-1)?i.fill:"none",className:o,d:s}))};function p0(){return(p0=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var p1=e=>{var{cx:t,cy:r,r:n,className:a}=e,i=(0,p.$)("recharts-dot",a);return t===+t&&r===+r&&n===+n?d.createElement("circle",p0({},F(e,!1),I(e),{className:i,cx:t,cy:r,r:n})):null},p2=e=>e.graphicalItems.polarItems,p5=e3([sL,sR],sG),p3=e3([p2,sH,p5],sQ),p4=e3([p3],s5),p6=e3([p4,cG],s4),p8=e3([p6,sH,p3],s8),p7=e3([p6,sH,p3],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:rG(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:rG(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),p9=()=>void 0,he=e3([sH,uj,p9,p7,p9],uE),ht=e3([sH,nS,p6,p8,sp,sL,he],uA),hr=e3([ht,sH,uC],uN),hn=e3([sH,ht,hr,sL],u_),ha=(e,t,r)=>{switch(t){case"angleAxis":return sE(e,r);case"radiusAxis":return sS(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},hi=(e,t,r)=>{switch(t){case"angleAxis":return sD(e,r);case"radiusAxis":return sI(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ho=e3([ha,uC,hn,hi],uD),hl=e3([nS,p8,sq,sL],u4),hc=e3([nS,ha,uC,ho,hr,hi,u3,hl,sL],u7),hs=e3([nS,ha,ho,hi,u3,hl,sL],fe),hu=e3([(e,t)=>hc(e,"angleAxis",t,!1)],e=>{if(e)return e.map(e=>e.coordinate)}),hf=e3([(e,t)=>hc(e,"radiusAxis",t,!1)],e=>{if(e)return e.map(e=>e.coordinate)}),hd=["gridType","radialLines","angleAxisId","radiusAxisId","cx","cy","innerRadius","outerRadius"];function hp(){return(hp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hy(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hh(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var hv=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:a,polarAngles:i,radialLines:o}=e;if(!i||!i.length||!o)return null;var l=hy({stroke:"#ccc"},F(e,!1));return d.createElement("g",{className:"recharts-polar-grid-angle"},i.map(e=>{var i=rV(t,r,n,e),o=rV(t,r,a,e);return d.createElement("line",hp({},l,{key:"line-".concat(e),x1:i.x,y1:i.y,x2:o.x,y2:o.y}))}))},hm=e=>{var{cx:t,cy:r,radius:n,index:a}=e,i=hy(hy({stroke:"#ccc"},F(e,!1)),{},{fill:"none"});return d.createElement("circle",hp({},i,{className:(0,p.$)("recharts-polar-grid-concentric-circle",e.className),key:"circle-".concat(a),cx:t,cy:r,r:n}))},hg=e=>{var{radius:t,index:r}=e,n=hy(hy({stroke:"#ccc"},F(e,!1)),{},{fill:"none"});return d.createElement("path",hp({},n,{className:(0,p.$)("recharts-polar-grid-concentric-polygon",e.className),key:"path-".concat(r),d:((e,t,r,n)=>{var a="";return n.forEach((n,i)=>{var o=rV(t,r,e,n);i?a+="L ".concat(o.x,",").concat(o.y):a+="M ".concat(o.x,",").concat(o.y)}),a+="Z"})(t,e.cx,e.cy,e.polarAngles)}))},hb=e=>{var{polarRadius:t,gridType:r}=e;return t&&t.length?d.createElement("g",{className:"recharts-polar-grid-concentric"},t.map((t,n)=>"circle"===r?d.createElement(hm,hp({key:n},e,{radius:t,index:n})):d.createElement(hg,hp({key:n},e,{radius:t,index:n})))):null},hx=e=>{var t,r,n,a,i,o,l,c,{gridType:s="polygon",radialLines:u=!0,angleAxisId:f=0,radiusAxisId:p=0,cx:h,cy:y,innerRadius:v,outerRadius:m}=e,g=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,hd),b=eB(s_),x=hy({cx:null!=(t=null!=(r=null==b?void 0:b.cx)?r:h)?t:0,cy:null!=(n=null!=(a=null==b?void 0:b.cy)?a:y)?n:0,innerRadius:null!=(i=null!=(o=null==b?void 0:b.innerRadius)?o:v)?i:0,outerRadius:null!=(l=null!=(c=null==b?void 0:b.outerRadius)?c:m)?l:0},g),{polarAngles:w,polarRadius:O,cx:P,cy:j,innerRadius:E,outerRadius:S}=x,k=eB(e=>hu(e,f)),A=eB(e=>hf(e,p)),M=Array.isArray(w)?w:k,T=Array.isArray(O)?O:A;return S<=0||null==M||null==T?null:d.createElement("g",{className:"recharts-polar-grid"},d.createElement(hv,hp({cx:P,cy:j,innerRadius:E,outerRadius:S,gridType:s,radialLines:u},x,{polarAngles:M,polarRadius:T})),d.createElement(hb,hp({cx:P,cy:j,innerRadius:E,outerRadius:S,gridType:s,radialLines:u},x,{polarAngles:M,polarRadius:T})))};hx.displayName="PolarGrid";var hw=r(70027),hO=r.n(hw),hP=r(31901),hj=r.n(hP),hE=t8({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=t.payload},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=t.payload},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:hS,removeRadiusAxis:hk,addAngleAxis:hA,removeAngleAxis:hM}=hE.actions,hT=hE.reducer,hC=["cx","cy","angle","axisLine"],hD=["angle","tickFormatter","stroke","tick"];function hN(){return(hN=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hI(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h_(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hI(Object(r),!0).forEach(function(t){hL(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hI(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hL(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function hR(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var hz="radiusAxis";function hK(e){var t=eL();return(0,d.useEffect)(()=>(t(hS(e)),()=>{t(hk(e))})),null}var hB=e=>{var{radiusAxisId:t}=e,r=eB(s_),n=eB(e=>ho(e,"radiusAxis",t)),a=eB(e=>hc(e,"radiusAxis",t,!1));if(null==r||!a||!a.length)return null;var i=h_(h_(h_({},e),{},{scale:n},r),{},{radius:r.outerRadius}),{tick:o,axisLine:l}=i;return d.createElement(q,{className:(0,p.$)("recharts-polar-radius-axis",hz,i.className)},l&&((e,t)=>{var{cx:r,cy:n,angle:a,axisLine:i}=e,o=hR(e,hC),l=t.reduce((e,t)=>[Math.min(e[0],t.coordinate),Math.max(e[1],t.coordinate)],[1/0,-1/0]),c=rV(r,n,l[0],a),s=rV(r,n,l[1],a),u=h_(h_(h_({},F(o,!1)),{},{fill:"none"},F(i,!1)),{},{x1:c.x,y1:c.y,x2:s.x,y2:s.y});return d.createElement("line",hN({className:"recharts-polar-radius-axis-line"},u))})(i,a),o&&((e,t)=>{var{angle:r,tickFormatter:n,stroke:a,tick:i}=e,o=hR(e,hD),l=(e=>{var t;switch(e){case"left":t="end";break;case"right":t="start";break;default:t="middle"}return t})(e.orientation),c=F(o,!1),s=F(i,!1),u=t.map((t,o)=>{var u=((e,t,r,n)=>{var{coordinate:a}=e;return rV(r,n,a,t)})(t,e.angle,e.cx,e.cy),f=h_(h_(h_(h_({textAnchor:l,transform:"rotate(".concat(90-r,", ").concat(u.x,", ").concat(u.y,")")},c),{},{stroke:"none",fill:a},s),{},{index:o},u),{},{payload:t});return d.createElement(q,hN({className:(0,p.$)("recharts-polar-radius-axis-tick",rH(i)),key:"tick-".concat(t.coordinate)},_(e,t,o)),((e,t,r)=>d.isValidElement(e)?d.cloneElement(e,t):"function"==typeof e?e(t):d.createElement(pS,hN({},t,{className:"recharts-polar-radius-axis-tick-value"}),r))(i,f,n?n(t.value,o):t.value))});return d.createElement(q,{className:"recharts-polar-radius-axis-ticks"},u)})(i,a),pI.renderCallByParent(i,((e,t,r,n)=>{var a=hO()(n,e=>e.coordinate||0);return{cx:t,cy:r,startAngle:e,endAngle:e,innerRadius:hj()(n,e=>e.coordinate||0).coordinate||0,outerRadius:a.coordinate||0}})(i.angle,i.cx,i.cy,a)))};class h$ extends d.PureComponent{render(){return d.createElement(d.Fragment,null,d.createElement(hK,{domain:this.props.domain,id:this.props.radiusAxisId,scale:this.props.scale,type:this.props.type,dataKey:this.props.dataKey,unit:void 0,name:this.props.name,allowDuplicatedCategory:this.props.allowDuplicatedCategory,allowDataOverflow:this.props.allowDataOverflow,reversed:this.props.reversed,includeHidden:this.props.includeHidden,allowDecimals:this.props.allowDecimals,tickCount:this.props.tickCount,ticks:this.props.ticks,tick:this.props.tick}),d.createElement(hB,this.props))}}hL(h$,"displayName","PolarRadiusAxis"),hL(h$,"axisType",hz),hL(h$,"defaultProps",sb);var hF=["children"];function hU(){return(hU=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function hW(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function hV(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hW(Object(r),!0).forEach(function(t){hX(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hW(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function hX(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var hH=Math.PI/180,hq="angleAxis";function hY(e){var t=eL(),r=(0,d.useMemo)(()=>{var{children:t}=e,r=e,n=hF;if(null==r)return{};var a,i,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(r,n);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(r);for(i=0;i<l.length;i++)a=l[i],-1===n.indexOf(a)&&({}).propertyIsEnumerable.call(r,a)&&(o[a]=r[a])}return o},[e]),n=eB(e=>sE(e,r.id)),a=r===n;return((0,d.useEffect)(()=>(t(hA(r)),()=>{t(hM(r))}),[t,r]),a)?e.children:null}var hG=e=>{var{cx:t,cy:r,radius:n,axisLineType:a,axisLine:i,ticks:o}=e;if(!i)return null;var l=hV(hV({},F(e,!1)),{},{fill:"none"},F(i,!1));if("circle"===a)return d.createElement(p1,hU({className:"recharts-polar-angle-axis-line"},l,{cx:t,cy:r,r:n}));var c=o.map(e=>rV(t,r,n,e.coordinate));return d.createElement(pQ,hU({className:"recharts-polar-angle-axis-line"},l,{points:c}))},hZ=e=>{var{tick:t,tickProps:r,value:n}=e;return t?d.isValidElement(t)?d.cloneElement(t,r):"function"==typeof t?t(r):d.createElement(pS,hU({},r,{className:"recharts-polar-angle-axis-tick-value"}),n):null},hJ=e=>{var{tick:t,tickLine:r,tickFormatter:n,stroke:a,ticks:i}=e,o=F(e,!1),l=F(t,!1),c=hV(hV({},o),{},{fill:"none"},F(r,!1)),s=i.map((i,s)=>{var u=((e,t)=>{var{cx:r,cy:n,radius:a,orientation:i,tickSize:o}=t,l=rV(r,n,a,e.coordinate),c=rV(r,n,a+("inner"===i?-1:1)*(o||8),e.coordinate);return{x1:l.x,y1:l.y,x2:c.x,y2:c.y}})(i,e),f=hV(hV(hV({textAnchor:((e,t)=>{var r=Math.cos(-e.coordinate*hH);return r>1e-5?"outer"===t?"start":"end":r<-1e-5?"outer"===t?"end":"start":"middle"})(i,e.orientation)},o),{},{stroke:"none",fill:a},l),{},{index:s,payload:i,x:u.x2,y:u.y2});return d.createElement(q,hU({className:(0,p.$)("recharts-polar-angle-axis-tick",rH(t)),key:"tick-".concat(i.coordinate)},_(e,i,s)),r&&d.createElement("line",hU({className:"recharts-polar-angle-axis-tick-line"},c,u)),d.createElement(hZ,{tick:t,tickProps:f,value:n?n(i.value,s):i.value}))});return d.createElement(q,{className:"recharts-polar-angle-axis-ticks"},s)},hQ=e=>{var{angleAxisId:t}=e,r=eB(s_),n=eB(e=>ho(e,"angleAxis",t)),a=nv(),i=eB(e=>hc(e,"angleAxis",t,a));if(null==r||!i||!i.length)return null;var o=hV(hV(hV({},e),{},{scale:n},r),{},{radius:r.outerRadius});return d.createElement(q,{className:(0,p.$)("recharts-polar-angle-axis",hq,o.className)},d.createElement(hG,hU({},o,{ticks:i})),d.createElement(hJ,hU({},o,{ticks:i})))};class h0 extends d.PureComponent{render(){return this.props.radius<=0?null:d.createElement(hY,{id:this.props.angleAxisId,scale:this.props.scale,type:this.props.type,dataKey:this.props.dataKey,unit:void 0,name:this.props.name,allowDuplicatedCategory:!1,allowDataOverflow:!1,reversed:this.props.reversed,includeHidden:!1,allowDecimals:this.props.allowDecimals,tickCount:this.props.tickCount,ticks:this.props.ticks,tick:this.props.tick,domain:this.props.domain},d.createElement(hQ,this.props))}}function h1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function h2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h1(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}hX(h0,"displayName","PolarAngleAxis"),hX(h0,"axisType",hq),hX(h0,"defaultProps",sg);var h5=(e,t)=>t,h3=[],h4=(e,t,r)=>(null==r?void 0:r.length)===0?h3:r,h6=e3([cG,h5,h4],(e,t,r)=>{var n,{chartData:a}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:a)&&n.length||null==r||(n=r.map(e=>h2(h2({},t.presentationProps),e.props))),null!=n)return n}),h8=e3([h6,h5,h4],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var a,i,o=rG(e,t.nameKey,t.name);return i=null!=r&&null!=(a=r[n])&&null!=(a=a.props)&&a.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:ne(o,t.dataKey),color:i,payload:e,type:t.legendType}})}),h7=e3([p2,h5],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),h9=e3([h6,h7,h4,nd],(e,t,r,n)=>{if(null!=t&&null!=e)return function(e){var t,r,n,{pieSettings:a,displayedData:i,cells:o,offset:l}=e,{cornerRadius:c,startAngle:s,endAngle:u,dataKey:f,nameKey:d,tooltipType:p}=a,h=Math.abs(a.minAngle),y=m(u-s)*Math.min(Math.abs(u-s),360),v=Math.abs(y),g=i.length<=1?0:null!=(t=a.paddingAngle)?t:0,b=i.filter(e=>0!==rG(e,f,0)).length,w=v-b*h-(v>=360?b:b-1)*g,O=i.reduce((e,t)=>{var r=rG(t,f,0);return e+(x(r)?r:0)},0);return O>0&&(r=i.map((e,t)=>{var r,i=rG(e,f,0),u=rG(e,d,t),v=((e,t,r)=>{let n,a,i;var{top:o,left:l,width:c,height:s}=t,u=rX(c,s),f=l+j(e.cx,c,c/2),d=o+j(e.cy,s,s/2),p=j(e.innerRadius,u,0);return{cx:f,cy:d,innerRadius:p,outerRadius:(n=r,a=e.outerRadius,i=u,"function"==typeof a?a(n):j(a,i,.8*i)),maxRadius:e.maxRadius||Math.sqrt(c*c+s*s)/2}})(a,l,e),b=(x(i)?i:0)/O,P=yL(yL({},e),o&&o[t]&&o[t].props),E=(r=t?n.endAngle+m(y)*g*(0!==i):s)+m(y)*((0!==i?h:0)+b*w),S=(r+E)/2,k=(v.innerRadius+v.outerRadius)/2,A=[{name:u,value:i,payload:P,dataKey:f,type:p}],M=rV(v.cx,v.cy,k,S);return n=yL(yL(yL(yL({},a.presentationProps),{},{percent:b,cornerRadius:c,name:u,tooltipPayload:A,midAngle:S,middleRadius:k,tooltipPosition:M},P),v),{},{value:rG(e,f),startAngle:r,endAngle:E,payload:P,paddingAngle:m(y)*g})})),r}({offset:n,pieSettings:t,displayedData:e,cells:r})}),ye=t8({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push(t.payload)},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,a=tY(e).cartesianItems.indexOf(r);a>-1&&(e.cartesianItems[a]=n)},removeCartesianGraphicalItem(e,t){var r=tY(e).cartesianItems.indexOf(t.payload);r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push(t.payload)},removePolarGraphicalItem(e,t){var r=tY(e).polarItems.indexOf(t.payload);r>-1&&e.polarItems.splice(r,1)}}}),{addBar:yt,removeBar:yr,addCartesianGraphicalItem:yn,replaceCartesianGraphicalItem:ya,removeCartesianGraphicalItem:yi,addPolarGraphicalItem:yo,removePolarGraphicalItem:yl}=ye.actions,yc=ye.reducer;function ys(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ys(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ys(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yf(e){var t=eL(),r=(0,d.useRef)(null);return(0,d.useEffect)(()=>{var n=yu(yu({},e),{},{stackId:r2(e.stackId)});null===r.current?t(yn(n)):r.current!==n&&t(ya({prev:r.current,next:n})),r.current=n},[t,e]),(0,d.useEffect)(()=>()=>{r.current&&(t(yi(r.current)),r.current=null)},[t]),null}function yd(e){var t=eL();return(0,d.useEffect)(()=>(t(yo(e)),()=>{t(yl(e))}),[t,e]),null}var yp=r(80931),yh=r.n(yp);function yy(){return(yy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var yv=(e,t,r,n,a)=>{var i=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-i/2,",").concat(t+a)+"L ".concat(e+r-i/2-n,",").concat(t+a)+"L ".concat(e,",").concat(t," Z")},ym={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},yg=e=>{var t=aI(e,ym),r=(0,d.useRef)(),[n,a]=(0,d.useState)(-1);(0,d.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:i,y:o,upperWidth:l,lowerWidth:c,height:s,className:u}=t,{animationEasing:f,animationDuration:h,animationBegin:y,isUpdateAnimationActive:v}=t;if(i!==+i||o!==+o||l!==+l||c!==+c||s!==+s||0===l&&0===c||0===s)return null;var m=(0,p.$)("recharts-trapezoid",u);return v?d.createElement(a3,{canBegin:n>0,from:{upperWidth:0,lowerWidth:0,height:s,x:i,y:o},to:{upperWidth:l,lowerWidth:c,height:s,x:i,y:o},duration:h,animationEasing:f,isActive:v},e=>{var{upperWidth:a,lowerWidth:i,height:o,x:l,y:c}=e;return d.createElement(a3,{canBegin:n>0,from:"0px ".concat(-1===n?1:n,"px"),to:"".concat(n,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,easing:f},d.createElement("path",yy({},F(t,!0),{className:m,d:yv(l,c,a,i,o),ref:r})))}):d.createElement("g",null,d.createElement("path",yy({},F(t,!0),{className:m,d:yv(i,o,l,c,s)})))},yb=["option","shapeType","propTransformer","activeClassName","isActive"];function yx(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yw(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?yx(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):yx(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yO(e,t){return yw(yw({},t),e)}function yP(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return d.createElement(a7,r);case"trapezoid":return d.createElement(yg,r);case"sector":return d.createElement(ii,r);case"symbols":if("symbols"===t)return d.createElement(eE,r);break;default:return null}}function yj(e){return(0,d.isValidElement)(e)?e.props:e}function yE(e){var t,{option:r,shapeType:n,propTransformer:a=yO,activeClassName:i="recharts-active-shape",isActive:o}=e,l=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,yb);if((0,d.isValidElement)(r))t=(0,d.cloneElement)(r,yw(yw({},l),yj(r)));else if("function"==typeof r)t=r(l);else if(yh()(r)&&"boolean"!=typeof r){var c=a(r,l);t=d.createElement(yP,{shapeType:n,elementProps:c})}else t=d.createElement(yP,{shapeType:n,elementProps:l});return o?d.createElement(q,{className:i},t):t}var yS=(e,t)=>{var r=eL();return(n,a)=>i=>{null==e||e(n,a,i),r(fv({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},yk=e=>{var t=eL();return(r,n)=>a=>{null==e||e(r,n,a),t(fm())}},yA=(e,t)=>{var r=eL();return(n,a)=>i=>{null==e||e(n,a,i),r(fb({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}};function yM(e){var{fn:t,args:r}=e,n=eL(),a=nv();return(0,d.useEffect)(()=>{if(!a){var e=t(r);return n(fp(e)),()=>{n(fh(e))}}},[t,r,n,a]),null}var yT=()=>{};function yC(e){var{legendPayload:t}=e,r=eL(),n=nv();return(0,d.useEffect)(()=>n?yT:(r(nN(t)),()=>{r(nI(t))}),[r,n,t]),null}function yD(e){var{legendPayload:t}=e,r=eL(),n=eB(nS);return(0,d.useEffect)(()=>"centric"!==n&&"radial"!==n?yT:(r(nN(t)),()=>{r(nI(t))}),[r,n,t]),null}function yN(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,d.useRef)(P(t)),n=(0,d.useRef)(e);return n.current!==e&&(r.current=P(t),n.current=e),r.current}var yI=["onMouseEnter","onClick","onMouseLeave"];function y_(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function yL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y_(Object(r),!0).forEach(function(t){yR(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function yR(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function yz(){return(yz=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function yK(e){var t=(0,d.useMemo)(()=>F(e,!1),[e]),r=(0,d.useMemo)(()=>B(e.children,pt),[e.children]),n=(0,d.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),a=eB(e=>h8(e,n,r));return d.createElement(yD,{legendPayload:a})}function yB(e){var{dataKey:t,nameKey:r,sectors:n,stroke:a,strokeWidth:i,fill:o,name:l,hide:c,tooltipType:s}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:a,strokeWidth:i,fill:o,dataKey:t,nameKey:r,name:ne(l,t),hide:c,type:s,color:o,unit:""}}}function y$(e){var{sectors:t,props:r,showLabels:n}=e,{label:a,labelLine:i,dataKey:o}=r;if(!n||!a||!t)return null;var l=F(r,!1),c=F(a,!1),s=F(i,!1),u="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,f=t.map((e,t)=>{var r,n,f=(e.startAngle+e.endAngle)/2,h=rV(e.cx,e.cy,e.outerRadius+u,f),y=yL(yL(yL(yL({},l),e),{},{stroke:"none"},c),{},{index:t,textAnchor:(r=h.x)>(n=e.cx)?"start":r<n?"end":"middle"},h),v=yL(yL(yL(yL({},l),e),{},{fill:"none",stroke:e.fill},s),{},{index:t,points:[rV(e.cx,e.cy,e.outerRadius,f),h],key:"line"});return d.createElement(q,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},i&&((e,t)=>{if(d.isValidElement(e))return d.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,p.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return d.createElement(aA,yz({},t,{type:"linear",className:r}))})(i,v),((e,t,r)=>{if(d.isValidElement(e))return d.cloneElement(e,t);var n=r;if("function"==typeof e&&(n=e(t),d.isValidElement(n)))return n;var a=(0,p.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return d.createElement(pS,yz({},t,{alignmentBaseline:"middle",className:a}),n)})(a,y,rG(e,o)))});return d.createElement(q,{className:"recharts-pie-labels"},f)}function yF(e){var{sectors:t,activeShape:r,inactiveShape:n,allOtherPieProps:a,showLabels:i}=e,o=eB(ds),{onMouseEnter:l,onClick:c,onMouseLeave:s}=a,u=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(a,yI),f=yS(l,a.dataKey),p=yk(s),h=yA(c,a.dataKey);return null==t?null:d.createElement(d.Fragment,null,t.map((e,i)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var l=r&&String(i)===o,c=l?r:o?n:null,s=yL(yL({},e),{},{stroke:e.stroke,tabIndex:-1,[nc]:i,[ns]:a.dataKey});return d.createElement(q,yz({tabIndex:-1,className:"recharts-pie-sector"},_(u,e,i),{onMouseEnter:f(e,i),onMouseLeave:p(e,i),onClick:h(e,i),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(i)}),d.createElement(yE,yz({option:c,isActive:l,shapeType:"sector"},s)))}),d.createElement(y$,{sectors:t,props:a,showLabels:i}))}function yU(e){var{props:t,previousSectorsRef:r}=e,{sectors:n,isAnimationActive:a,animationBegin:i,animationDuration:o,animationEasing:l,activeShape:c,inactiveShape:s,onAnimationStart:u,onAnimationEnd:f}=t,p=yN(t,"recharts-pie-"),h=r.current,[v,m]=(0,d.useState)(!0),g=(0,d.useCallback)(()=>{"function"==typeof f&&f(),m(!1)},[f]),b=(0,d.useCallback)(()=>{"function"==typeof u&&u(),m(!0)},[u]);return d.createElement(a3,{begin:i,duration:o,isActive:a,easing:l,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:g,key:p},e=>{var{t:a}=e,i=[],o=(n&&n[0]).startAngle;return n.forEach((e,t)=>{var r=h&&h[t],n=t>0?y()(e,"paddingAngle",0):0;if(r){var l=S(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=yL(yL({},e),{},{startAngle:o+n,endAngle:o+l(a)+n});i.push(c),o=c.endAngle}else{var{endAngle:s,startAngle:u}=e,f=S(0,s-u)(a),d=yL(yL({},e),{},{startAngle:o+n,endAngle:o+f+n});i.push(d),o=d.endAngle}}),r.current=i,d.createElement(q,null,d.createElement(yF,{sectors:i,activeShape:c,inactiveShape:s,allOtherPieProps:t,showLabels:!v}))})}function yW(e){var{sectors:t,isAnimationActive:r,activeShape:n,inactiveShape:a}=e,i=(0,d.useRef)(null),o=i.current;return r&&t&&t.length&&(!o||o!==t)?d.createElement(yU,{props:e,previousSectorsRef:i}):d.createElement(yF,{sectors:t,activeShape:n,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function yV(e){var{hide:t,className:r,rootTabIndex:n}=e,a=(0,p.$)("recharts-pie",r);return t?null:d.createElement(q,{tabIndex:n,className:a},d.createElement(yW,e))}var yX={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!n4.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function yH(e){var t=aI(e,yX),r=(0,d.useMemo)(()=>B(e.children,pt),[e.children]),n=F(t,!1),a=(0,d.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:n}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,n]),i=eB(e=>h9(e,a,r));return d.createElement(d.Fragment,null,d.createElement(yM,{fn:yB,args:yL(yL({},t),{},{sectors:i})}),d.createElement(yV,yz({},t,{sectors:i})))}class yq extends d.PureComponent{render(){return d.createElement(d.Fragment,null,d.createElement(yd,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),d.createElement(yK,this.props),d.createElement(yH,this.props),this.props.children)}constructor(){super(...arguments),yR(this,"id",P("recharts-pie-"))}}yR(yq,"displayName","Pie"),yR(yq,"defaultProps",yX);var yY=e3([nd],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),yG=e3([yY,nt,nr],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),yZ=()=>eB(du),yJ=()=>eB(yY),yQ=()=>eB(yG),y0=()=>eB(dm);function y1(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y2(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y1(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y1(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y5(e){var{points:t,mainColor:r,activeDot:n,itemDataKey:a}=e,i=eB(ds),o=y0();if(null==t||null==o)return null;var l=t.find(e=>o.includes(e.payload));return null==l?null:(e=>{var t,{point:r,childIndex:n,mainColor:a,activeDot:i,dataKey:o}=e;if(!1===i||null==r.x||null==r.y)return null;var l=y2(y2({index:n,dataKey:o,cx:r.x,cy:r.y,r:4,fill:null!=a?a:"none",strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},F(i,!1)),I(i));return t=(0,d.isValidElement)(i)?(0,d.cloneElement)(i,l):"function"==typeof i?i(l):d.createElement(p1,l),d.createElement(q,{className:"recharts-active-dot"},t)})({point:l,childIndex:Number(i),mainColor:r,dataKey:a,activeDot:n})}var y3=e=>d.createElement(yd,e);function y4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y6(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y4(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y8=(e,t)=>ho(e,"radiusAxis",t),y7=e3([y8],e=>{if(null!=e)return{scale:e}}),y9=e3([sS,y8],(e,t)=>{if(null!=e&&null!=t)return y6(y6({},e),{},{scale:t})}),ve=(e,t,r)=>sE(e,r),vt=(e,t,r)=>ho(e,"angleAxis",r),vr=e3([ve,vt],(e,t)=>{if(null!=e&&null!=t)return y6(y6({},e),{},{scale:t})}),vn=e3([ve,vt,s_],(e,t,r)=>{if(null!=r&&null!=t)return{scale:t,type:e.type,dataKey:e.dataKey,cx:r.cx,cy:r.cy}}),va=e3([nS,y9,(e,t,r,n)=>hc(e,"radiusAxis",t,n),vr,(e,t,r,n)=>hc(e,"angleAxis",r,n)],(e,t,r,n,a)=>rZ(e,"radiusAxis")?r7(t,r,!1):r7(n,a,!1)),vi=e3([p2,(e,t,r,n,a)=>a],(e,t)=>{if(e.some(e=>"radar"===e.type&&t===e.dataKey))return t}),vo=e3([y7,vn,cG,vi,va],(e,t,r,n,a)=>{var{chartData:i,dataStartIndex:o,dataEndIndex:l}=r;if(null!=e&&null!=t&&null!=i&&null!=a&&null!=n)return function(e){var{radiusAxis:t,angleAxis:r,displayedData:n,dataKey:a,bandSize:i}=e,{cx:o,cy:l}=r,c=!1,s=[],u="number"!==r.type&&null!=i?i:0;n.forEach((e,n)=>{var i=rG(e,r.dataKey,n),f=rG(e,a),d=r.scale(i)+u,p=Array.isArray(f)?pR()(f):f,h=null==p?void 0:t.scale(p);Array.isArray(f)&&f.length>=2&&(c=!0),s.push(vc(vc({},rV(o,l,h,d)),{},{name:i,value:f,cx:o,cy:l,radius:h,angle:d,payload:e}))});var f=[];return c&&s.forEach(e=>{if(Array.isArray(e.value)){var r=e.value[0],n=null==r?void 0:t.scale(r);f.push(vc(vc({},e),{},{radius:n},rV(o,l,n,e.angle)))}else f.push(e)}),{points:s,isRange:c,baseLinePoints:f}}({radiusAxis:e,angleAxis:t,displayedData:i.slice(o,l+1),dataKey:n,bandSize:a})});function vl(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vc(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vl(Object(r),!0).forEach(function(t){vs(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vl(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vs(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vu(){return(vu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vf(e,t){return e&&"none"!==e?e:t}function vd(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:a,name:i,hide:o,tooltipType:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:a,nameKey:void 0,dataKey:t,name:ne(i,t),hide:o,type:l,color:vf(r,a),unit:""}}}function vp(e){var{points:t,props:r}=e,{dot:n,dataKey:a}=r;if(!n)return null;var i=F(r,!1),o=F(n,!0),l=t.map((e,t)=>{var r=vc(vc(vc({key:"dot-".concat(t),r:3},i),o),{},{dataKey:a,cx:e.x,cy:e.y,index:t,payload:e});return d.isValidElement(n)?d.cloneElement(n,r):"function"==typeof n?n(r):d.createElement(p1,vu({},r,{className:(0,p.$)("recharts-radar-dot","boolean"!=typeof n?n.className:"")}))});return d.createElement(q,{className:"recharts-radar-dots"},l)}function vh(e){var t,{points:r,props:n,showLabels:a}=e;if(null==r)return null;var{shape:i,isRange:o,baseLinePoints:l,connectNulls:c}=n;return t=d.isValidElement(i)?d.cloneElement(i,vc(vc({},n),{},{points:r})):"function"==typeof i?i(vc(vc({},n),{},{points:r})):d.createElement(pQ,vu({},F(n,!0),{onMouseEnter:e=>{var{onMouseEnter:t}=n;t&&t(n,e)},onMouseLeave:e=>{var{onMouseLeave:t}=n;t&&t(n,e)},points:r,baseLinePoints:o?l:null,connectNulls:c})),d.createElement(q,{className:"recharts-radar-polygon"},t,d.createElement(vp,{props:n,points:r}),a&&pV.renderCallByParent(n,r))}function vy(e){var{props:t,previousPointsRef:r}=e,{points:n,isAnimationActive:a,animationBegin:i,animationDuration:o,animationEasing:l,onAnimationEnd:c,onAnimationStart:s}=t,u=r.current,f=yN(t,"recharts-radar-"),[p,h]=(0,d.useState)(!0),y=(0,d.useCallback)(()=>{"function"==typeof c&&c(),h(!1)},[c]),v=(0,d.useCallback)(()=>{"function"==typeof s&&s(),h(!0)},[s]);return d.createElement(a3,{begin:i,duration:o,isActive:a,easing:l,from:{t:0},to:{t:1},key:"radar-".concat(f),onAnimationEnd:y,onAnimationStart:v},e=>{var{t:a}=e,i=u&&u.length/n.length,o=1===a?n:n.map((e,t)=>{var r=u&&u[Math.floor(t*i)];if(r){var n=S(r.x,e.x),o=S(r.y,e.y);return vc(vc({},e),{},{x:n(a),y:o(a)})}var l=S(e.cx,e.x),c=S(e.cy,e.y);return vc(vc({},e),{},{x:l(a),y:c(a)})});return a>0&&(r.current=o),d.createElement(vh,{points:o,props:t,showLabels:!p})})}function vv(e){var{points:t,isAnimationActive:r,isRange:n}=e,a=(0,d.useRef)(void 0),i=a.current;return r&&t&&t.length&&!n&&(!i||i!==t)?d.createElement(vy,{props:e,previousPointsRef:a}):d.createElement(vh,{points:t,props:e,showLabels:!0})}var vm={angleAxisId:0,radiusAxisId:0,hide:!1,activeDot:!0,dot:!1,legendType:"rect",isAnimationActive:!n4.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"};class vg extends d.PureComponent{render(){var{hide:e,className:t,points:r}=this.props;if(e)return null;var n=(0,p.$)("recharts-radar",t);return d.createElement(d.Fragment,null,d.createElement(q,{className:n},d.createElement(vv,this.props)),d.createElement(y5,{points:r,mainColor:vf(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}function vb(e){var t=nv(),r=eB(r=>vo(r,e.radiusAxisId,e.angleAxisId,t,e.dataKey));return d.createElement(vg,vu({},e,{points:null==r?void 0:r.points,baseLinePoints:null==r?void 0:r.baseLinePoints,isRange:null==r?void 0:r.isRange}))}class vx extends d.PureComponent{render(){return d.createElement(d.Fragment,null,d.createElement(y3,{data:void 0,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:this.props.angleAxisId,radiusAxisId:this.props.radiusAxisId,stackId:void 0,barSize:void 0,type:"radar"}),d.createElement(yD,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,fill:a,legendType:i,hide:o}=e;return[{inactive:o,dataKey:t,type:i,color:vf(n,a),value:ne(r,t),payload:e}]})(this.props)}),d.createElement(yM,{fn:vd,args:this.props}),d.createElement(vb,this.props))}}function vw(){return(vw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vO(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vj(e){return"string"==typeof e?parseInt(e,10):e}function vE(e,t){var r=Number("".concat(t.cx||e.cx)),n=Number("".concat(t.cy||e.cy));return vP(vP(vP({},t),e),{},{cx:r,cy:n})}function vS(e){return d.createElement(yE,vw({shapeType:"sector",propTransformer:vE},e))}vs(vx,"displayName","Radar"),vs(vx,"defaultProps",vm);var vk=()=>{var e=eL();return(0,d.useEffect)(()=>(e(yt()),()=>{e(yr())})),null},vA=["children"],vM=()=>{},vT=(0,d.createContext)({addErrorBar:vM,removeErrorBar:vM}),vC=(0,d.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function vD(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,vA);return d.createElement(vC.Provider,{value:r},t)}var vN=e=>{var{children:t,xAxisId:r,yAxisId:n,zAxisId:a,dataKey:i,data:o,stackId:l,hide:c,type:s,barSize:u}=e,[f,p]=d.useState([]),h=(0,d.useCallback)(e=>{p(t=>[...t,e])},[p]),y=(0,d.useCallback)(e=>{p(t=>t.filter(t=>t!==e))},[p]),v=nv();return d.createElement(vT.Provider,{value:{addErrorBar:h,removeErrorBar:y}},d.createElement(yf,{type:s,data:o,xAxisId:r,yAxisId:n,zAxisId:a,dataKey:i,errorBars:f,stackId:l,hide:c,barSize:u,isPanorama:v}),t)};function vI(e){var{addErrorBar:t,removeErrorBar:r}=(0,d.useContext)(vT);return(0,d.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var v_=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function vL(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function vR(){return(vR=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vz(e){var t,r,{direction:n,width:a,dataKey:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s}=e,u=F(function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,v_),!1),{data:f,dataPointFormatter:p,xAxisId:h,yAxisId:y,errorBarOffset:v}=(0,d.useContext)(vC),m=(t=nv(),eB(e=>fr(e,"xAxis",h,t))),g=(r=nv(),eB(e=>fr(e,"yAxis",y,r)));if((null==m?void 0:m.scale)==null||(null==g?void 0:g.scale)==null||null==f||"x"===n&&"number"!==m.type)return null;var b=f.map(e=>{var t,r,{x:f,y:h,value:y,errorVal:b}=p(e,i,n);if(!b)return null;var x=[];if(Array.isArray(b)?[t,r]=b:t=r=b,"x"===n){var{scale:w}=m,O=h+v,P=O+a,j=O-a,E=w(y-t),S=w(y+r);x.push({x1:S,y1:P,x2:S,y2:j}),x.push({x1:E,y1:O,x2:S,y2:O}),x.push({x1:E,y1:P,x2:E,y2:j})}else if("y"===n){var{scale:k}=g,A=f+v,M=A-a,T=A+a,C=k(y-t),D=k(y+r);x.push({x1:M,y1:D,x2:T,y2:D}),x.push({x1:A,y1:C,x2:A,y2:D}),x.push({x1:M,y1:C,x2:T,y2:C})}var N="".concat(f+v,"px ").concat(h+v,"px");return d.createElement(q,vR({className:"recharts-errorBar",key:"bar-".concat(x.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},u),x.map(e=>{var t=o?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return d.createElement(a3,{from:{transform:"scaleY(0)",transformOrigin:N},to:{transform:"scaleY(1)",transformOrigin:N},begin:l,easing:s,isActive:o,duration:c,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:N}},d.createElement("line",vR({},e,{style:t})))}))});return d.createElement(q,{className:"recharts-errorBars"},b)}var vK=(0,d.createContext)(void 0);function vB(e){var{direction:t,children:r}=e;return d.createElement(vK.Provider,{value:t},r)}var v$={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function vF(e){var t,r,n=(t=e.direction,r=(0,d.useContext)(vK),null!=t?t:null!=r?r:"x"),{width:a,isAnimationActive:i,animationBegin:o,animationDuration:l,animationEasing:c}=aI(e,v$);return d.createElement(d.Fragment,null,d.createElement(vI,{dataKey:e.dataKey,direction:n}),d.createElement(vz,vR({},e,{direction:n,width:a,isAnimationActive:i,animationBegin:o,animationDuration:l,animationEasing:c})))}class vU extends d.Component{render(){return d.createElement(vF,this.props)}}vL(vU,"defaultProps",v$),vL(vU,"displayName","ErrorBar");var vW=["x","y"];function vV(){return(vV=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function vX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function vH(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?vX(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):vX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function vq(e,t){var{x:r,y:n}=e,a=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,vW),i=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||a.height),10),c=parseInt("".concat(t.width||a.width),10);return vH(vH(vH(vH(vH({},t),a),i?{x:i}:{}),o?{y:o}:{}),{},{height:l,width:c,name:t.name,radius:t.radius})}function vY(e){return d.createElement(yE,vV({shapeType:"rectangle",propTransformer:vq,activeClassName:"recharts-active-bar"},e))}var vG=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if(x(e))return e;var a=x(r)||null==r;return a?e(r,n):(a||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}};function vZ(e,t){var r,n,a=eB(t=>sF(t,e)),i=eB(e=>sW(e,t)),o=null!=(r=null==a?void 0:a.allowDataOverflow)?r:s$.allowDataOverflow,l=null!=(n=null==i?void 0:i.allowDataOverflow)?n:sU.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function vJ(e){var{xAxisId:t,yAxisId:r,clipPathId:n}=e,a=yQ(),{needClipX:i,needClipY:o,needClip:l}=vZ(t,r);if(!l)return null;var{x:c,y:s,width:u,height:f}=a;return d.createElement("clipPath",{id:"clipPath-".concat(n)},d.createElement("rect",{x:i?c:c-u/2,y:o?s:s-f/2,width:i?u:2*u,height:o?f:2*f}))}var vQ=["onMouseEnter","onMouseLeave","onClick"],v0=["value","background","tooltipPosition"],v1=["onMouseEnter","onClick","onMouseLeave"];function v2(){return(v2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v5(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v3(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v5(Object(r),!0).forEach(function(t){v4(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v5(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v4(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function v6(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function v8(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:a,name:i,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:a,dataKey:t,nameKey:void 0,name:ne(i,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function v7(e){var t=eB(ds),{data:r,dataKey:n,background:a,allOtherBarProps:i}=e,{onMouseEnter:o,onMouseLeave:l,onClick:c}=i,s=v6(i,vQ),u=yS(o,n),f=yk(l),p=yA(c,n);if(!a||null==r)return null;var h=F(a,!1);return d.createElement(d.Fragment,null,r.map((e,r)=>{var{value:i,background:o,tooltipPosition:l}=e,c=v6(e,v0);if(!o)return null;var y=u(e,r),v=f(e,r),m=p(e,r),g=v3(v3(v3(v3(v3({option:a,isActive:String(r)===t},c),{},{fill:"#eee"},o),h),_(s,e,r)),{},{onMouseEnter:y,onMouseLeave:v,onClick:m,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return d.createElement(vY,v2({key:"background-bar-".concat(r)},g))}))}function v9(e){var{data:t,props:r,showLabels:n}=e,a=F(r,!1),{shape:i,dataKey:o,activeBar:l}=r,c=eB(ds),s=eB(df),{onMouseEnter:u,onClick:f,onMouseLeave:p}=r,h=v6(r,v1),y=yS(u,o),v=yk(p),m=yA(f,o);return t?d.createElement(d.Fragment,null,t.map((e,t)=>{var r=l&&String(t)===c&&(null==s||o===s),n=v3(v3(v3({},a),e),{},{isActive:r,option:r?l:i,index:t,dataKey:o});return d.createElement(q,v2({className:"recharts-bar-rectangle"},_(h,e,t),{onMouseEnter:y(e,t),onMouseLeave:v(e,t),onClick:m(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),d.createElement(vY,n))}),n&&pV.renderCallByParent(r,t)):null}function me(e){var{props:t,previousRectanglesRef:r}=e,{data:n,layout:a,isAnimationActive:i,animationBegin:o,animationDuration:l,animationEasing:c,onAnimationEnd:s,onAnimationStart:u}=t,f=r.current,p=yN(t,"recharts-bar-"),[h,y]=(0,d.useState)(!1),v=(0,d.useCallback)(()=>{"function"==typeof s&&s(),y(!1)},[s]),m=(0,d.useCallback)(()=>{"function"==typeof u&&u(),y(!0)},[u]);return d.createElement(a3,{begin:o,duration:l,isActive:i,easing:c,from:{t:0},to:{t:1},onAnimationEnd:v,onAnimationStart:m,key:p},e=>{var{t:i}=e,o=1===i?n:n.map((e,t)=>{var r=f&&f[t];if(r){var n=S(r.x,e.x),o=S(r.y,e.y),l=S(r.width,e.width),c=S(r.height,e.height);return v3(v3({},e),{},{x:n(i),y:o(i),width:l(i),height:c(i)})}if("horizontal"===a){var s=S(0,e.height)(i);return v3(v3({},e),{},{y:e.y+e.height-s,height:s})}var u=S(0,e.width)(i);return v3(v3({},e),{},{width:u})});return i>0&&(r.current=o),d.createElement(q,null,d.createElement(v9,{props:t,data:o,showLabels:!h}))})}function mt(e){var{data:t,isAnimationActive:r}=e,n=(0,d.useRef)(null);return r&&t&&t.length&&(null==n.current||n.current!==t)?d.createElement(me,{previousRectanglesRef:n,props:e}):d.createElement(v9,{props:e,data:t,showLabels:!0})}var mr=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:rG(e,t)}};class mn extends d.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:n,xAxisId:a,yAxisId:i,needClip:o,background:l,id:c,layout:s}=this.props;if(e)return null;var u=(0,p.$)("recharts-bar",n),f=null==c?this.id:c;return d.createElement(q,{className:u},o&&d.createElement("defs",null,d.createElement(vJ,{clipPathId:f,xAxisId:a,yAxisId:i})),d.createElement(q,{className:"recharts-bar-rectangles",clipPath:o?"url(#clipPath-".concat(f,")"):null},d.createElement(v7,{data:t,dataKey:r,background:l,allOtherBarProps:this.props}),d.createElement(mt,this.props)),d.createElement(vB,{direction:"horizontal"===s?"y":"x"},this.props.children))}constructor(){super(...arguments),v4(this,"id",P("recharts-bar-"))}}var ma={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!n4.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function mi(e){var t,{xAxisId:r,yAxisId:n,hide:a,legendType:i,minPointSize:o,activeBar:l,animationBegin:c,animationDuration:s,animationEasing:u,isAnimationActive:f}=aI(e,ma),{needClip:p}=vZ(r,n),h=nk(),y=nv(),v=(0,d.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:o,stackId:r2(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,o,e.stackId]),m=B(e.children,pt),g=eB(e=>mO(e,r,n,y,v,m));if("vertical"!==h&&"horizontal"!==h)return null;var b=null==g?void 0:g[0];return t=null==b||null==b.height||null==b.width?0:"vertical"===h?b.height/2:b.width/2,d.createElement(vD,{xAxisId:r,yAxisId:n,data:g,dataPointFormatter:mr,errorBarOffset:t},d.createElement(mn,v2({},e,{layout:h,needClip:p,data:g,xAxisId:r,yAxisId:n,hide:a,legendType:i,minPointSize:o,activeBar:l,animationBegin:c,animationDuration:s,animationEasing:u,isAnimationActive:f})))}class mo extends d.PureComponent{render(){return d.createElement(vN,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},d.createElement(vk,null),d.createElement(yC,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:t,type:a,color:n,value:ne(r,t),payload:e}]})(this.props)}),d.createElement(yM,{fn:v8,args:this.props}),d.createElement(mi,this.props))}}function ml(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mc(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ml(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ml(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}v4(mo,"displayName","Bar"),v4(mo,"defaultProps",ma);var ms=(e,t,r,n,a)=>a,mu=(e,t,r)=>{var n=null!=r?r:e;if(null!=n)return j(n,t,0)},mf=e3([nS,sZ,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,a)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===a).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function md(e){return null!=e.stackId&&null!=e.dataKey}var mp=(e,t,r)=>{var n=e.filter(md),a=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,a]=e;return{stackId:n,dataKeys:a.map(e=>e.dataKey),barSize:mu(t,r,a[0].barSize)}}),...a.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:mu(t,r,e.barSize)}))]},mh=e3([mf,sd,(e,t,r)=>"horizontal"===nS(e)?u2(e,"xAxis",t):u2(e,"yAxis",r)],mp),my=(e,t,r,n)=>{var a,i;return"horizontal"===nS(e)?(a=fr(e,"xAxis",t,n),i=ft(e,"xAxis",t,n)):(a=fr(e,"yAxis",r,n),i=ft(e,"yAxis",r,n)),r7(a,i)},mv=(e,t,r,n,a,i,o)=>{var l=function(e,t,r,n,a){var i,o=n.length;if(!(o<1)){var l=j(e,r,0,!0),c=[];if(ab(n[0].barSize)){var s=!1,u=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*l)>=r&&(f-=(o-1)*l,l=0),f>=r&&u>0&&(s=!0,u*=.9,f=o*u);var d={offset:((r-f)/2|0)-l,size:0};i=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+l,size:s?u:null!=(r=t.barSize)?r:0}}];return d=n[n.length-1].position,n},c)}else{var p=j(t,r,0,!0);r-2*p-(o-1)*l<=0&&(l=0);var h=(r-2*p-(o-1)*l)/o;h>1&&(h>>=0);var y=ab(a)?Math.min(h,a):h;i=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p+(h+l)*r+(h-y)/2,size:y}}],c)}return i}}(r,n,a!==i?a:i,e,null==o?t:o);return a!==i&&null!=l&&(l=l.map(e=>mc(mc({},e),{},{position:mc(mc({},e.position),{},{offset:e.position.offset-a/2})}))),l},mm=e3([mh,ss,su,sf,(e,t,r,n,a)=>{var i,o,l,c,s=nS(e),u=ss(e),{maxBarSize:f}=a,d=null==f?u:f;return"horizontal"===s?(l=fr(e,"xAxis",t,n),c=ft(e,"xAxis",t,n)):(l=fr(e,"yAxis",r,n),c=ft(e,"yAxis",r,n)),null!=(i=null!=(o=r7(l,c,!0))?o:d)?i:0},my,(e,t,r,n,a)=>a.maxBarSize],mv),mg=e3([mm,ms],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),mb=(e,t)=>{if(!e||(null==t?void 0:t.dataKey)==null)return;var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:a}=n;if(a)return a.find(e=>e.key===t.dataKey)}}},mx=e3([sZ,ms],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),mw=e3([(e,t,r,n)=>"horizontal"===nS(e)?ur(e,"yAxis",r,n):ur(e,"xAxis",t,n),ms],mb),mO=e3([nd,(e,t,r,n)=>fr(e,"xAxis",t,n),(e,t,r,n)=>fr(e,"yAxis",r,n),(e,t,r,n)=>ft(e,"xAxis",t,n),(e,t,r,n)=>ft(e,"yAxis",r,n),mg,nS,cZ,my,mw,mx,(e,t,r,n,a,i)=>i],(e,t,r,n,a,i,o,l,c,s,u,f)=>{var d,{chartData:p,dataStartIndex:h,dataEndIndex:y}=l;if(null!=u&&null!=i&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=a&&null!=c){var{data:v}=u;if(null!=(d=null!=v&&v.length>0?v:null==p?void 0:p.slice(h,y+1)))return function(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:a,bandSize:i,xAxis:o,yAxis:l,xAxisTicks:c,yAxisTicks:s,stackedData:u,displayedData:f,offset:d,cells:p}=e,h="horizontal"===t?l:o,y=u?h.scale.domain():null,v=r4({numericAxis:h});return f.map((e,f)=>{u?b=r0(u[f],y):Array.isArray(b=rG(e,r))||(b=[v,b]);var h=vG(n,0)(b[1],f);if("horizontal"===t){var b,x,w,O,P,j,E,[S,k]=[l.scale(b[0]),l.scale(b[1])];x=r3({axis:o,ticks:c,bandSize:i,offset:a.offset,entry:e,index:f}),w=null!=(E=null!=k?k:S)?E:void 0,O=a.size;var A=S-k;if(P=g(A)?0:A,j={x,y:d.top,width:O,height:d.height},Math.abs(h)>0&&Math.abs(P)<Math.abs(h)){var M=m(P||h)*(Math.abs(h)-Math.abs(P));w-=M,P+=M}}else{var[T,C]=[o.scale(b[0]),o.scale(b[1])];if(x=T,w=r3({axis:l,ticks:s,bandSize:i,offset:a.offset,entry:e,index:f}),O=C-T,P=a.size,j={x:d.left,y:w,width:d.width,height:P},Math.abs(h)>0&&Math.abs(O)<Math.abs(h)){var D=m(O||h)*(Math.abs(h)-Math.abs(O));O+=D}}return v3(v3({},e),{},{x,y:w,width:O,height:P,value:u?b:b[1],payload:e,background:j,tooltipPosition:{x:x+O/2,y:w+P/2}},p&&p[f]&&p[f].props)})}({layout:o,barSettings:u,pos:i,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:a,stackedData:s,displayedData:d,offset:e,cells:f})}});function mP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mP(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var mE=e3([(e,t)=>sS(e,t),(e,t)=>ho(e,"radiusAxis",t)],(e,t)=>{if(null!=e&&null!=t)return mj(mj({},e),{},{scale:t})}),mS=(e,t,r,n)=>hs(e,"radiusAxis",t,n),mk=e3([(e,t,r)=>sE(e,r),(e,t,r)=>ho(e,"angleAxis",r)],(e,t)=>{if(null!=e&&null!=t)return mj(mj({},e),{},{scale:t})}),mA=(e,t,r,n)=>hc(e,"angleAxis",r,n),mM=e3([p2,(e,t,r,n)=>n],(e,t)=>{if(e.some(e=>"radialBar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId))return t}),mT=e3([nS,mE,mS,mk,mA],(e,t,r,n,a)=>rZ(e,"radiusAxis")?r7(t,r,!1):r7(n,a,!1)),mC=e3([mk,mE,nS],(e,t,r)=>{var n="radial"===r?e:t;if(null!=n&&null!=n.scale)return r4({numericAxis:n})}),mD=(e,t,r,n,a)=>n.maxBarSize,mN=e3([nS,p2,(e,t,r,n,a)=>r,(e,t,r,n,a)=>t],(e,t,r,n)=>t.filter(t=>"centric"===e?t.angleAxisId===r:t.radiusAxisId===n).filter(e=>!1===e.hide).filter(e=>"radialBar"===e.type)),mI=e3([mN,sd,()=>void 0],mp),m_=e3([nS,ss,mk,mA,mE,mS,mD],(e,t,r,n,a,i,o)=>{var l,c,s,u,f=null==o?t:o;return"centric"===e?null!=(s=null!=(u=r7(r,n,!0))?u:f)?s:0:null!=(l=null!=(c=r7(a,i,!0))?c:f)?l:0}),mL=e3([mI,ss,su,sf,m_,mT,mD],mv),mR=e3([mL,mM],(e,t)=>{if(null!=e&&null!=t){var r=e.find(e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),mz=e3([p6,p3,sp],ut),mK=e3([(e,t,r)=>"centric"===nS(e)?mz(e,"radiusAxis",t):mz(e,"angleAxis",r),mM],mb),mB=e3([mk,mA,mE,mS,cY,mM,mT,nS,mC,s_,(e,t,r,n,a)=>a,mR,mK],(e,t,r,n,a,i,o,l,c,s,u,f,d)=>{var{chartData:p,dataStartIndex:h,dataEndIndex:y}=a;if(null==i||null==r||null==e||null==p||null==o||null==f||"centric"!==l&&"radial"!==l||null==n)return[];var{dataKey:v,minPointSize:g}=i,{cx:b,cy:x,startAngle:w,endAngle:O}=s,P=p.slice(h,y+1),j="centric"===l?r:e,E=d?j.scale.domain():null;return function(e){var{displayedData:t,stackedData:r,dataStartIndex:n,stackedDomain:a,dataKey:i,baseValue:o,layout:l,radiusAxis:c,radiusAxisTicks:s,bandSize:u,pos:f,angleAxis:d,minPointSize:p,cx:h,cy:y,angleAxisTicks:v,cells:g,startAngle:b,endAngle:x}=e;return(null!=t?t:[]).map((e,t)=>{var w,O,P,j,E,S;if(r?w=r0(r[n+t],a):Array.isArray(w=rG(e,i))||(w=[o,w]),"radial"===l){O=r3({axis:c,ticks:s,bandSize:u,offset:f.offset,entry:e,index:t}),E=d.scale(w[1]),j=d.scale(w[0]),P=(null!=O?O:0)+f.size;var k=E-j;Math.abs(p)>0&&Math.abs(k)<Math.abs(p)&&(E+=m(k||p)*(Math.abs(p)-Math.abs(k))),S={background:{cx:h,cy:y,innerRadius:O,outerRadius:P,startAngle:b,endAngle:x}}}else{O=c.scale(w[0]),P=c.scale(w[1]),E=(null!=(j=r3({axis:d,ticks:v,bandSize:u,offset:f.offset,entry:e,index:t}))?j:0)+f.size;var A=P-O;Math.abs(p)>0&&Math.abs(A)<Math.abs(p)&&(P+=m(A||p)*(Math.abs(p)-Math.abs(A)))}return mH(mH(mH({},e),S),{},{payload:e,value:r?w:w[1],cx:h,cy:y,innerRadius:O,outerRadius:P,startAngle:j,endAngle:E},g&&g[t]&&g[t].props)})}({angleAxis:e,angleAxisTicks:t,bandSize:o,baseValue:c,cells:u,cx:b,cy:x,dataKey:v,dataStartIndex:h,displayedData:P,endAngle:O,layout:l,minPointSize:g,pos:f,radiusAxis:r,radiusAxisTicks:n,stackedData:d,stackedDomain:E,startAngle:w})}),m$=e3([cG,(e,t)=>t],(e,t)=>{var{chartData:r,dataStartIndex:n,dataEndIndex:a}=e;if(null==r)return[];var i=r.slice(n,a+1);return 0===i.length?[]:i.map(e=>({type:t,value:e.name,color:e.fill,payload:e}))}),mF=["shape","activeShape","cornerRadius"],mU=["onMouseEnter","onClick","onMouseLeave"],mW=["value","background"];function mV(){return(mV=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function mX(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function mH(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?mX(Object(r),!0).forEach(function(t){mq(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):mX(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function mq(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function mY(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var mG=[];function mZ(e){var{sectors:t,allOtherRadialBarProps:r,showLabels:n}=e,{shape:a,activeShape:i,cornerRadius:o}=r,l=mY(r,mF),c=F(l,!1),s=eB(ds),{onMouseEnter:u,onClick:f,onMouseLeave:p}=r,h=mY(r,mU),y=yS(u,r.dataKey),v=yk(p),m=yA(f,r.dataKey);return null==t?null:d.createElement(d.Fragment,null,t.map((e,t)=>{var r=i&&s===String(t),n=y(e,t),u=v(e,t),f=m(e,t),p=mH(mH(mH(mH({},c),{},{cornerRadius:vj(o)},e),_(h,e,t)),{},{onMouseEnter:n,onMouseLeave:u,onClick:f,key:"sector-".concat(t),className:"recharts-radial-bar-sector ".concat(e.className),forceCornerRadius:l.forceCornerRadius,cornerIsExternal:l.cornerIsExternal,isActive:r,option:r?i:a});return d.createElement(vS,p)}),n&&pV.renderCallByParent(r,t))}function mJ(e){var{props:t,previousSectorsRef:r}=e,{data:n,isAnimationActive:a,animationBegin:i,animationDuration:o,animationEasing:l,onAnimationEnd:c,onAnimationStart:s}=t,u=yN(t,"recharts-radialbar-"),f=r.current,[p,h]=(0,d.useState)(!0),y=(0,d.useCallback)(()=>{"function"==typeof c&&c(),h(!1)},[c]),v=(0,d.useCallback)(()=>{"function"==typeof s&&s(),h(!0)},[s]);return d.createElement(a3,{begin:i,duration:o,isActive:a,easing:l,from:{t:0},to:{t:1},onAnimationStart:v,onAnimationEnd:y,key:u},e=>{var{t:a}=e,i=1===a?n:(null!=n?n:mG).map((e,t)=>{var r=f&&f[t];if(r){var n=S(r.startAngle,e.startAngle),i=S(r.endAngle,e.endAngle);return mH(mH({},e),{},{startAngle:n(a),endAngle:i(a)})}var{endAngle:o,startAngle:l}=e,c=S(l,o);return mH(mH({},e),{},{endAngle:c(a)})});return a>0&&(r.current=null!=i?i:null),d.createElement(q,null,d.createElement(mZ,{sectors:null!=i?i:mG,allOtherRadialBarProps:t,showLabels:!p}))})}function mQ(e){var{data:t=[],isAnimationActive:r}=e,n=(0,d.useRef)(null),a=n.current;return r&&t&&t.length&&(!a||a!==t)?d.createElement(mJ,{props:e,previousSectorsRef:n}):d.createElement(mZ,{sectors:t,allOtherRadialBarProps:e,showLabels:!0})}function m0(e){var t=eB(t=>m$(t,e.legendType));return d.createElement(yD,{legendPayload:null!=t?t:[]})}function m1(e){var{dataKey:t,data:r,stroke:n,strokeWidth:a,name:i,hide:o,fill:l,tooltipType:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:a,fill:l,nameKey:void 0,dataKey:t,name:ne(i,t),hide:o,type:c,color:l,unit:""}}}class m2 extends d.PureComponent{renderBackground(e){if(null==e)return null;var{cornerRadius:t}=this.props,r=F(this.props.background,!1);return e.map((e,n)=>{var{value:a,background:i}=e,o=mY(e,mW);if(!i)return null;var l=mH(mH(mH(mH(mH({cornerRadius:vj(t)},o),{},{fill:"#eee"},i),r),_(this.props,e,n)),{},{index:n,key:"sector-".concat(n),className:(0,p.$)("recharts-radial-bar-background-sector",null==r?void 0:r.className),option:i,isActive:!1});return d.createElement(vS,l)})}render(){var{hide:e,data:t,className:r,background:n}=this.props;if(e)return null;var a=(0,p.$)("recharts-area",r);return d.createElement(q,{className:a},n&&d.createElement(q,{className:"recharts-radial-bar-background"},this.renderBackground(t)),d.createElement(q,{className:"recharts-radial-bar-sectors"},d.createElement(mQ,this.props)))}}function m5(e){var t,r=B(e.children,pt),n={dataKey:e.dataKey,minPointSize:e.minPointSize,stackId:e.stackId,maxBarSize:e.maxBarSize,barSize:e.barSize},a=null!=(t=eB(t=>mB(t,e.radiusAxisId,e.angleAxisId,n,r)))?t:mG;return d.createElement(d.Fragment,null,d.createElement(yM,{fn:m1,args:mH(mH({},e),{},{data:a})}),d.createElement(m2,mV({},e,{data:a})))}var m3={angleAxisId:0,radiusAxisId:0,minPointSize:0,hide:!1,legendType:"rect",data:[],isAnimationActive:!n4.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease",forceCornerRadius:!1,cornerIsExternal:!1};class m4 extends d.PureComponent{render(){var e,t,r;return d.createElement(d.Fragment,null,d.createElement(vk,null),d.createElement(y3,{data:void 0,dataKey:this.props.dataKey,hide:null!=(e=this.props.hide)?e:m3.hide,angleAxisId:null!=(t=this.props.angleAxisId)?t:m3.angleAxisId,radiusAxisId:null!=(r=this.props.radiusAxisId)?r:m3.radiusAxisId,stackId:this.props.stackId,barSize:this.props.barSize,type:"radialBar"}),d.createElement(m0,this.props),d.createElement(m5,this.props))}}function m6(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m8(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m6(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m6(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}mq(m4,"displayName","RadialBar"),mq(m4,"defaultProps",m3);var m7=["Webkit","Moz","O","ms"],m9=e=>{var{chartData:t}=e,r=eL(),n=nv();return(0,d.useEffect)(()=>n?()=>{}:(r(dq(t)),()=>{r(dq(void 0))}),[t,r,n]),null},ge=e=>{var{computedData:t}=e,r=eL();return(0,d.useEffect)(()=>(r(dG(t)),()=>{r(dq(void 0))}),[t,r]),null},gt=e=>e.chartData.chartData,gr=e=>{var{dataStartIndex:t,dataEndIndex:r}=e.chartData;return{startIndex:t,endIndex:r}},gn=(0,d.createContext)(()=>{}),ga={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},gi=t8({name:"brush",initialState:ga,reducers:{setBrushSettings:(e,t)=>null==t.payload?ga:t.payload}}),{setBrushSettings:go}=gi.actions,gl=gi.reducer;function gc(){return(gc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gs(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gs(Object(r),!0).forEach(function(t){gf(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gs(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gf(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gd(e){var{x:t,y:r,width:n,height:a,stroke:i}=e,o=Math.floor(r+a/2)-1;return d.createElement(d.Fragment,null,d.createElement("rect",{x:t,y:r,width:n,height:a,fill:i,stroke:"none"}),d.createElement("line",{x1:t+1,y1:o,x2:t+n-1,y2:o,fill:"none",stroke:"#fff"}),d.createElement("line",{x1:t+1,y1:o+2,x2:t+n-1,y2:o+2,fill:"none",stroke:"#fff"}))}function gp(e){var{travellerProps:t,travellerType:r}=e;return d.isValidElement(r)?d.cloneElement(r,t):"function"==typeof r?r(t):d.createElement(gd,t)}function gh(e){var t,r,{otherProps:n,travellerX:a,id:i,onMouseEnter:o,onMouseLeave:l,onMouseDown:c,onTouchStart:s,onTravellerMoveKeyboard:u,onFocus:f,onBlur:p}=e,{y:h,x:y,travellerWidth:v,height:m,traveller:g,ariaLabel:b,data:x,startIndex:w,endIndex:O}=n,P=Math.max(a,y),j=gu(gu({},F(n,!1)),{},{x:P,y:h,width:v,height:m}),E=b||"Min value: ".concat(null==(t=x[w])?void 0:t.name,", Max value: ").concat(null==(r=x[O])?void 0:r.name);return d.createElement(q,{tabIndex:0,role:"slider","aria-label":E,"aria-valuenow":a,className:"recharts-brush-traveller",onMouseEnter:o,onMouseLeave:l,onMouseDown:c,onTouchStart:s,onKeyDown:e=>{["ArrowLeft","ArrowRight"].includes(e.key)&&(e.preventDefault(),e.stopPropagation(),u("ArrowRight"===e.key?1:-1,i))},onFocus:f,onBlur:p,style:{cursor:"col-resize"}},d.createElement(gp,{travellerType:g,travellerProps:j}))}function gy(e){var{index:t,data:r,tickFormatter:n,dataKey:a}=e,i=rG(r[t],a,t);return"function"==typeof n?n(i,t):i}function gv(e,t){for(var r=e.length,n=0,a=r-1;a-n>1;){var i=Math.floor((n+a)/2);e[i]>t?a=i:n=i}return t>=e[a]?a:n}function gm(e){var{startX:t,endX:r,scaleValues:n,gap:a,data:i}=e,o=i.length-1,l=Math.min(t,r),c=Math.max(t,r),s=gv(n,l),u=gv(n,c);return{startIndex:s-s%a,endIndex:u===o?o:u-u%a}}function gg(e){var{x:t,y:r,width:n,height:a,fill:i,stroke:o}=e;return d.createElement("rect",{stroke:o,fill:i,x:t,y:r,width:n,height:a})}function gb(e){var{startIndex:t,endIndex:r,y:n,height:a,travellerWidth:i,stroke:o,tickFormatter:l,dataKey:c,data:s,startX:u,endX:f}=e,p={pointerEvents:"none",fill:o};return d.createElement(q,{className:"recharts-brush-texts"},d.createElement(pS,gc({textAnchor:"end",verticalAnchor:"middle",x:Math.min(u,f)-5,y:n+a/2},p),gy({index:t,tickFormatter:l,dataKey:c,data:s})),d.createElement(pS,gc({textAnchor:"start",verticalAnchor:"middle",x:Math.max(u,f)+i+5,y:n+a/2},p),gy({index:r,tickFormatter:l,dataKey:c,data:s})))}function gx(e){var{y:t,height:r,stroke:n,travellerWidth:a,startX:i,endX:o,onMouseEnter:l,onMouseLeave:c,onMouseDown:s,onTouchStart:u}=e,f=Math.min(i,o)+a,p=Math.max(Math.abs(o-i)-a,0);return d.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:l,onMouseLeave:c,onMouseDown:s,onTouchStart:u,style:{cursor:"move"},stroke:"none",fill:n,fillOpacity:.2,x:f,y:t,width:p,height:r})}function gw(e){var{x:t,y:r,width:n,height:a,data:i,children:o,padding:l}=e;if(1!==d.Children.count(o))return null;var c=d.Children.only(o);return c?d.cloneElement(c,{x:t,y:r,width:n,height:a,margin:l,compact:!0,data:i}):null}var gO=e=>e.changedTouches&&!!e.changedTouches.length;class gP extends d.PureComponent{static getDerivedStateFromProps(e,t){var{data:r,width:n,x:a,travellerWidth:i,startIndex:o,endIndex:l,startIndexControlledFromProps:c,endIndexControlledFromProps:s}=e;if(r!==t.prevData)return gu({prevData:r,prevTravellerWidth:i,prevX:a,prevWidth:n},r&&r.length?(e=>{var{data:t,startIndex:r,endIndex:n,x:a,width:i,travellerWidth:o}=e;if(!t||!t.length)return{};var l=t.length,c=im().domain(il()(0,l)).range([a,a+i-o]),s=c.domain().map(e=>c(e));return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:s}})({data:r,width:n,x:a,travellerWidth:i,startIndex:o,endIndex:l}):{scale:null,scaleValues:null});if(t.scale&&(n!==t.prevWidth||a!==t.prevX||i!==t.prevTravellerWidth)){t.scale.range([a,a+n-i]);var u=t.scale.domain().map(e=>t.scale(e));return{prevData:r,prevTravellerWidth:i,prevX:a,prevWidth:n,startX:t.scale(e.startIndex),endX:t.scale(e.endIndex),scaleValues:u}}if(t.scale&&!t.isSlideMoving&&!t.isTravellerMoving&&!t.isTravellerFocused&&!t.isTextActive){if(null!=c&&t.prevStartIndexControlledFromProps!==c)return{startX:t.scale(c),prevStartIndexControlledFromProps:c};if(null!=s&&t.prevEndIndexControlledFromProps!==s)return{endX:t.scale(s),prevEndIndexControlledFromProps:s}}return null}componentWillUnmount(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}attachDragEndListener(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}detachDragEndListener(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}handleSlideDrag(e){var{slideMoveStartX:t,startX:r,endX:n,scaleValues:a}=this.state,{x:i,width:o,travellerWidth:l,startIndex:c,endIndex:s,onChange:u,data:f,gap:d}=this.props,p=e.pageX-t;p>0?p=Math.min(p,i+o-l-n,i+o-l-r):p<0&&(p=Math.max(p,i-r,i-n));var h=gm({startX:r+p,endX:n+p,data:f,gap:d,scaleValues:a});(h.startIndex!==c||h.endIndex!==s)&&u&&u(h),this.setState({startX:r+p,endX:n+p,slideMoveStartX:e.pageX})}handleTravellerDragStart(e,t){var r=gO(t)?t.changedTouches[0]:t;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:e,brushMoveStartX:r.pageX}),this.attachDragEndListener()}handleTravellerMove(e){var{brushMoveStartX:t,movingTravellerId:r,endX:n,startX:a,scaleValues:i}=this.state,o=this.state[r],{x:l,width:c,travellerWidth:s,onChange:u,gap:f,data:d}=this.props,p={startX:this.state.startX,endX:this.state.endX,data:d,gap:f,scaleValues:i},h=e.pageX-t;h>0?h=Math.min(h,l+c-s-o):h<0&&(h=Math.max(h,l-o)),p[r]=o+h;var y=gm(p),{startIndex:v,endIndex:m}=y;this.setState({[r]:o+h,brushMoveStartX:e.pageX},()=>{var e;u&&(e=d.length-1,"startX"===r&&(n>a?v%f==0:m%f==0)||n<a&&m===e||"endX"===r&&(n>a?m%f==0:v%f==0)||n>a&&m===e||0)&&u(y)})}render(){var{data:e,className:t,children:r,x:n,y:a,dy:i,width:o,height:l,alwaysShowText:c,fill:s,stroke:u,startIndex:f,endIndex:h,travellerWidth:y,tickFormatter:v,dataKey:m,padding:g}=this.props,{startX:b,endX:w,isTextActive:O,isSlideMoving:P,isTravellerMoving:j,isTravellerFocused:E}=this.state;if(!e||!e.length||!x(n)||!x(a)||!x(o)||!x(l)||o<=0||l<=0)return null;var S=(0,p.$)("recharts-brush",t),k=((e,t)=>{if(!e)return null;var r=e.replace(/(\w)/,e=>e.toUpperCase()),n=m7.reduce((e,n)=>m8(m8({},e),{},{[n+r]:t}),{});return n[e]=t,n})("userSelect","none"),A=a+(null!=i?i:0);return d.createElement(q,{className:S,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:k},d.createElement(gg,{x:n,y:A,width:o,height:l,fill:s,stroke:u}),d.createElement(nm,null,d.createElement(gw,{x:n,y:A,width:o,height:l,data:e,padding:g},r)),d.createElement(gx,{y:A,height:l,stroke:u,travellerWidth:y,startX:b,endX:w,onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart}),d.createElement(gh,{travellerX:b,id:"startX",otherProps:gu(gu({},this.props),{},{y:A}),onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers.startX,onTouchStart:this.travellerDragStartHandlers.startX,onTravellerMoveKeyboard:this.handleTravellerMoveKeyboard,onFocus:()=>{this.setState({isTravellerFocused:!0})},onBlur:()=>{this.setState({isTravellerFocused:!1})}}),d.createElement(gh,{travellerX:w,id:"endX",otherProps:gu(gu({},this.props),{},{y:A}),onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers.endX,onTouchStart:this.travellerDragStartHandlers.endX,onTravellerMoveKeyboard:this.handleTravellerMoveKeyboard,onFocus:()=>{this.setState({isTravellerFocused:!0})},onBlur:()=>{this.setState({isTravellerFocused:!1})}}),(O||P||j||E||c)&&d.createElement(gb,{startIndex:f,endIndex:h,y:A,height:l,travellerWidth:y,stroke:u,tickFormatter:v,dataKey:m,data:e,startX:b,endX:w}))}constructor(e){super(e),gf(this,"handleDrag",e=>{this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.state.isTravellerMoving?this.handleTravellerMove(e):this.state.isSlideMoving&&this.handleSlideDrag(e)}),gf(this,"handleTouchMove",e=>{null!=e.changedTouches&&e.changedTouches.length>0&&this.handleDrag(e.changedTouches[0])}),gf(this,"handleDragEnd",()=>{this.setState({isTravellerMoving:!1,isSlideMoving:!1},()=>{var{endIndex:e,onDragEnd:t,startIndex:r}=this.props;null==t||t({endIndex:e,startIndex:r})}),this.detachDragEndListener()}),gf(this,"handleLeaveWrapper",()=>{(this.state.isTravellerMoving||this.state.isSlideMoving)&&(this.leaveTimer=window.setTimeout(this.handleDragEnd,this.props.leaveTimeOut))}),gf(this,"handleEnterSlideOrTraveller",()=>{this.setState({isTextActive:!0})}),gf(this,"handleLeaveSlideOrTraveller",()=>{this.setState({isTextActive:!1})}),gf(this,"handleSlideDragStart",e=>{var t=gO(e)?e.changedTouches[0]:e;this.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:t.pageX}),this.attachDragEndListener()}),gf(this,"handleTravellerMoveKeyboard",(e,t)=>{var{data:r,gap:n}=this.props,{scaleValues:a,startX:i,endX:o}=this.state,l=this.state[t],c=a.indexOf(l);if(-1!==c){var s=c+e;if(-1!==s&&!(s>=a.length)){var u=a[s];"startX"===t&&u>=o||"endX"===t&&u<=i||this.setState({[t]:u},()=>{this.props.onChange(gm({startX:this.state.startX,endX:this.state.endX,data:r,gap:n,scaleValues:a}))})}}}),this.travellerDragStartHandlers={startX:this.handleTravellerDragStart.bind(this,"startX"),endX:this.handleTravellerDragStart.bind(this,"endX")},this.state={}}}function gj(e){var t,r,n,a,i=eL(),o=eB(gt),{startIndex:l,endIndex:c}=eB(gr),s=(0,d.useContext)(gn),u=e.onChange,{startIndex:f,endIndex:p}=e;(0,d.useEffect)(()=>{i(dY({startIndex:f,endIndex:p}))},[i,p,f]),t=eB(sy),r=eB(sm),n=eB(e=>e.chartData.dataStartIndex),a=eB(e=>e.chartData.dataEndIndex),(0,d.useEffect)(()=>{null!=t&&null!=n&&null!=a&&null!=r&&dK.emit(d$,t,{startIndex:n,endIndex:a},r)},[a,n,r,t]);var h=(0,d.useCallback)(e=>{(e.startIndex!==l||e.endIndex!==c)&&(null==s||s(e),null==u||u(e),i(dY(e)))},[u,s,i,l,c]),{x:y,y:v,width:m}=eB(nb);return d.createElement(gP,gc({},e,{data:o,x:y,y:v,width:m,startIndex:l,endIndex:c,onChange:h},{startIndexControlledFromProps:null!=f?f:void 0,endIndexControlledFromProps:null!=p?p:void 0}))}function gE(e){var t=eL();return(0,d.useEffect)(()=>(t(go(e)),()=>{t(go(null))}),[t,e]),null}class gS extends d.PureComponent{render(){return d.createElement(d.Fragment,null,d.createElement(gE,{height:this.props.height,x:this.props.x,y:this.props.y,width:this.props.width,padding:this.props.padding}),d.createElement(gj,this.props))}}function gk(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gk(Object(r),!0).forEach(function(t){gM(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gk(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gM(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}gf(gS,"displayName","Brush"),gf(gS,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var gT=(e,t)=>{var{x:r,y:n}=e,{x:a,y:i}=t;return{x:Math.min(r,a),y:Math.min(n,i),width:Math.abs(a-r),height:Math.abs(i-n)}};class gC{static create(e){return new gC(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var a=this.bandwidth?this.bandwidth():0;return this.scale(e)+a}if(t){var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+i}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}gM(gC,"EPS",1e-4);var gD=e=>{var t=Object.keys(e).reduce((t,r)=>gA(gA({},t),{},{[r]:gC.create(e[r])}),{});return gA(gA({},t),{},{apply(e){var{bandAware:r,position:n}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Object.fromEntries(Object.entries(e).map(e=>{var[a,i]=e;return[a,t[a].apply(i,{bandAware:r,position:n})]}))},isInRange:e=>Object.keys(e).every(r=>t[r].isInRange(e[r]))})},gN=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=(n%180+180)%180*Math.PI/180,i=Math.atan(r/t);return Math.abs(a>i&&a<Math.PI-i?r/Math.sin(a):t/Math.cos(a))},gI=t8({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=tY(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=tY(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=tY(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:g_,removeDot:gL,addArea:gR,removeArea:gz,addLine:gK,removeLine:gB}=gI.actions,g$=gI.reducer,gF=(0,d.createContext)(void 0),gU=e=>{var{children:t}=e,[r]=(0,d.useState)("".concat(P("recharts"),"-clip")),n=yQ();if(null==n)return null;var{x:a,y:i,width:o,height:l}=n;return d.createElement(gF.Provider,{value:r},d.createElement("defs",null,d.createElement("clipPath",{id:r},d.createElement("rect",{x:a,y:i,height:l,width:o}))),t)},gW=()=>(0,d.useContext)(gF);function gV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function gX(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gV(Object(r),!0).forEach(function(t){gH(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function gH(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function gq(){return(gq=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function gY(e){var t=eL();return(0,d.useEffect)(()=>(t(gK(e)),()=>{t(gB(e))})),null}function gG(e){var{x:t,y:r,segment:n,xAxisId:a,yAxisId:i,shape:o,className:l,ifOverflow:c}=e,s=nv(),u=gW(),f=eB(e=>sF(e,a)),h=eB(e=>sW(e,i)),y=eB(e=>uV(e,"xAxis",a,s)),v=eB(e=>uV(e,"yAxis",i,s)),m=nx(),b=w(t),x=w(r);if(!u||!m||null==f||null==h||null==y||null==v)return null;var O=((e,t,r,n,a,i,o,l,c)=>{var{x:s,y:u,width:f,height:d}=a;if(r){var{y:p}=c,h=e.y.apply(p,{position:i});if(g(h)||"discard"===c.ifOverflow&&!e.y.isInRange(h))return null;var y=[{x:s+f,y:h},{x:s,y:h}];return"left"===l?y.reverse():y}if(t){var{x:v}=c,m=e.x.apply(v,{position:i});if(g(m)||"discard"===c.ifOverflow&&!e.x.isInRange(m))return null;var b=[{x:m,y:u+d},{x:m,y:u}];return"top"===o?b.reverse():b}if(n){var{segment:x}=c,w=x.map(t=>e.apply(t,{position:i}));return"discard"===c.ifOverflow&&w.some(t=>!e.isInRange(t))?null:w}return null})(gD({x:y,y:v}),b,x,n&&2===n.length,m,e.position,f.orientation,h.orientation,e);if(!O)return null;var[{x:P,y:j},{x:E,y:S}]=O,k=gX(gX({clipPath:"hidden"===c?"url(#".concat(u,")"):void 0},F(e,!0)),{},{x1:P,y1:j,x2:E,y2:S});return d.createElement(q,{className:(0,p.$)("recharts-reference-line",l)},d.isValidElement(o)?d.cloneElement(o,k):"function"==typeof o?o(k):d.createElement("line",gq({},k,{className:"recharts-reference-line-line"})),pI.renderCallByParent(e,(e=>{var{x1:t,y1:r,x2:n,y2:a}=e;return gT({x:t,y:r},{x:n,y:a})})({x1:P,y1:j,x2:E,y2:S})))}function gZ(e){return d.createElement(d.Fragment,null,d.createElement(gY,{yAxisId:e.yAxisId,xAxisId:e.xAxisId,ifOverflow:e.ifOverflow,x:e.x,y:e.y}),d.createElement(gG,e))}class gJ extends d.Component{render(){return d.createElement(gZ,this.props)}}function gQ(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g0(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?gQ(Object(r),!0).forEach(function(t){g1(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):gQ(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g1(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function g2(){return(g2=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g5(e){var t=eL();return(0,d.useEffect)(()=>(t(g_(e)),()=>{t(gL(e))})),null}function g3(e){var{x:t,y:r,r:n}=e,a=gW(),i=((e,t,r,n,a)=>{var i=w(e),o=w(t),l=nv(),c=eB(e=>uV(e,"xAxis",r,l)),s=eB(e=>uV(e,"yAxis",n,l));if(!i||!o||null==c||null==s)return null;var u=gD({x:c,y:s}),f=u.apply({x:e,y:t},{bandAware:!0});return"discard"!==a||u.isInRange(f)?f:null})(t,r,e.xAxisId,e.yAxisId,e.ifOverflow);if(!i)return null;var{x:o,y:l}=i,{shape:c,className:s,ifOverflow:u}=e,f=g0(g0({clipPath:"hidden"===u?"url(#".concat(a,")"):void 0},F(e,!0)),{},{cx:o,cy:l});return d.createElement(q,{className:(0,p.$)("recharts-reference-dot",s)},d.isValidElement(c)?d.cloneElement(c,f):"function"==typeof c?c(f):d.createElement(p1,g2({},f,{cx:f.cx,cy:f.cy,className:"recharts-reference-dot-dot"})),pI.renderCallByParent(e,{x:o-n,y:l-n,width:2*n,height:2*n}))}function g4(e){var{x:t,y:r,r:n,ifOverflow:a,yAxisId:i,xAxisId:o}=e;return d.createElement(d.Fragment,null,d.createElement(g5,{y:r,x:t,r:n,yAxisId:i,xAxisId:o,ifOverflow:a}),d.createElement(g3,e))}gH(gJ,"displayName","ReferenceLine"),gH(gJ,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});class g6 extends d.Component{render(){return d.createElement(g4,this.props)}}function g8(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g7(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g8(Object(r),!0).forEach(function(t){g9(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g8(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g9(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function be(){return(be=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function bt(e){var t=eL();return(0,d.useEffect)(()=>(t(gR(e)),()=>{t(gz(e))})),null}function br(e){var t,{x1:r,x2:n,y1:a,y2:i,className:o,shape:l,xAxisId:c,yAxisId:s}=e,u=gW(),f=nv(),h=eB(e=>uV(e,"xAxis",c,f)),y=eB(e=>uV(e,"yAxis",s,f));if(null==h||null==!y)return null;var v=w(r),m=w(n),g=w(a),b=w(i);if(!v&&!m&&!g&&!b&&!l)return null;var x=((e,t,r,n,a,i,o)=>{var{x1:l,x2:c,y1:s,y2:u}=o;if(null==a||null==i)return null;var f=gD({x:a,y:i}),d={x:e?f.x.apply(l,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(s,{position:"start"}):f.y.rangeMin},p={x:t?f.x.apply(c,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return"discard"!==o.ifOverflow||f.isInRange(d)&&f.isInRange(p)?gT(d,p):null})(v,m,g,b,h,y,e);if(!x&&!l)return null;var O="hidden"===e.ifOverflow;return d.createElement(q,{className:(0,p.$)("recharts-reference-area",o)},(t=g7(g7({clipPath:O?"url(#".concat(u,")"):void 0},F(e,!0)),x),d.isValidElement(l)?d.cloneElement(l,t):"function"==typeof l?l(t):d.createElement(a7,be({},t,{className:"recharts-reference-area-rect"}))),pI.renderCallByParent(e,x))}function bn(e){return d.createElement(d.Fragment,null,d.createElement(bt,{yAxisId:e.yAxisId,xAxisId:e.xAxisId,ifOverflow:e.ifOverflow,x1:e.x1,x2:e.x2,y1:e.y1,y2:e.y2}),d.createElement(br,e))}g1(g6,"displayName","ReferenceDot"),g1(g6,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1});class ba extends d.Component{render(){return d.createElement(bn,this.props)}}function bi(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}function bo(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],a=0;a<e.length;a+=t)if(void 0!==r&&!0!==r(e[a]))return;else n.push(e[a]);return n}function bl(e,t,r,n,a){if(e*t<e*n||e*t>e*a)return!1;var i=r();return e*(t-e*i/2-n)>=0&&e*(t+e*i/2-a)<=0}function bc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bs(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bc(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bu(e,t,r){var n,{tick:a,ticks:i,viewBox:o,minTickGap:l,orientation:c,interval:s,tickFormatter:u,unit:f,angle:d}=e;if(!i||!i.length||!a)return[];if(x(s)||n4.isSsr)return null!=(n=bo(i,(x(s)?s:0)+1))?n:[];var p="top"===c||"bottom"===c?"width":"height",h=f&&"width"===p?pl(f,{fontSize:t,letterSpacing:r}):{width:0,height:0},y=(e,n)=>{var a,i="function"==typeof u?u(e.value,n):e.value;return"width"===p?(a=pl(i,{fontSize:t,letterSpacing:r}),gN({width:a.width+h.width,height:a.height+h.height},d)):pl(i,{fontSize:t,letterSpacing:r})[p]},v=i.length>=2?m(i[1].coordinate-i[0].coordinate):1,g=function(e,t,r){var n="width"===r,{x:a,y:i,width:o,height:l}=e;return 1===t?{start:n?a:i,end:n?a+o:i+l}:{start:n?a+o:i+l,end:n?a:i}}(o,v,p);return"equidistantPreserveStart"===s?function(e,t,r,n,a){for(var i,o=(n||[]).slice(),{start:l,end:c}=t,s=0,u=1,f=l;u<=o.length;)if(i=function(){var t,i=null==n?void 0:n[s];if(void 0===i)return{v:bo(n,u)};var o=s,d=()=>(void 0===t&&(t=r(i,o)),t),p=i.coordinate,h=0===s||bl(e,p,d,f,c);h||(s=0,f=l,u+=1),h&&(f=p+e*(d()/2+a),s+=u)}())return i.v;return[]}(v,g,y,i,l):("preserveStart"===s||"preserveStartEnd"===s?function(e,t,r,n,a,i){var o=(n||[]).slice(),l=o.length,{start:c,end:s}=t;if(i){var u=n[l-1],f=r(u,l-1),d=e*(u.coordinate+e*f/2-s);o[l-1]=u=bs(bs({},u),{},{tickCoord:d>0?u.coordinate-d*e:u.coordinate}),bl(e,u.tickCoord,()=>f,c,s)&&(s=u.tickCoord-e*(f/2+a),o[l-1]=bs(bs({},u),{},{isShow:!0}))}for(var p=i?l-1:l,h=function(t){var n,i=o[t],l=()=>(void 0===n&&(n=r(i,t)),n);if(0===t){var u=e*(i.coordinate-e*l()/2-c);o[t]=i=bs(bs({},i),{},{tickCoord:u<0?i.coordinate-u*e:i.coordinate})}else o[t]=i=bs(bs({},i),{},{tickCoord:i.coordinate});bl(e,i.tickCoord,l,c,s)&&(c=i.tickCoord+e*(l()/2+a),o[t]=bs(bs({},i),{},{isShow:!0}))},y=0;y<p;y++)h(y);return o}(v,g,y,i,l,"preserveStartEnd"===s):function(e,t,r,n,a){for(var i=(n||[]).slice(),o=i.length,{start:l}=t,{end:c}=t,s=function(t){var n,s=i[t],u=()=>(void 0===n&&(n=r(s,t)),n);if(t===o-1){var f=e*(s.coordinate+e*u()/2-c);i[t]=s=bs(bs({},s),{},{tickCoord:f>0?s.coordinate-f*e:s.coordinate})}else i[t]=s=bs(bs({},s),{},{tickCoord:s.coordinate});bl(e,s.tickCoord,u,l,c)&&(c=s.tickCoord-e*(u()/2+a),i[t]=bs(bs({},s),{},{isShow:!0}))},u=o-1;u>=0;u--)s(u);return i}(v,g,y,i,l)).filter(e=>e.isShow)}g9(ba,"displayName","ReferenceArea"),g9(ba,"defaultProps",{ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1});var bf=["viewBox"],bd=["viewBox"];function bp(){return(bp=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function bh(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function by(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bh(Object(r),!0).forEach(function(t){bm(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bh(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bv(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function bm(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class bg extends d.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=bv(e,bf),a=this.props,{viewBox:i}=a,o=bv(a,bd);return!bi(r,i)||!bi(n,o)||!bi(t,this.state)}getTickLineCoord(e){var t,r,n,a,i,o,{x:l,y:c,width:s,height:u,orientation:f,tickSize:d,mirror:p,tickMargin:h}=this.props,y=p?-1:1,v=e.tickSize||d,m=x(e.tickCoord)?e.tickCoord:e.coordinate;switch(f){case"top":t=r=e.coordinate,o=(n=(a=c+!p*u)-y*v)-y*h,i=m;break;case"left":n=a=e.coordinate,i=(t=(r=l+!p*s)-y*v)-y*h,o=m;break;case"right":n=a=e.coordinate,i=(t=(r=l+p*s)+y*v)+y*h,o=m;break;default:t=r=e.coordinate,o=(n=(a=c+p*u)+y*v)+y*h,i=m}return{line:{x1:t,y1:n,x2:r,y2:a},tick:{x:i,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:n,orientation:a,mirror:i,axisLine:o}=this.props,l=by(by(by({},F(this.props,!1)),F(o,!1)),{},{fill:"none"});if("top"===a||"bottom"===a){var c=+("top"===a&&!i||"bottom"===a&&i);l=by(by({},l),{},{x1:e,y1:t+c*n,x2:e+r,y2:t+c*n})}else{var s=+("left"===a&&!i||"right"===a&&i);l=by(by({},l),{},{x1:e+s*r,y1:t,x2:e+s*r,y2:t+n})}return d.createElement("line",bp({},l,{className:(0,p.$)("recharts-cartesian-axis-line",y()(o,"className"))}))}static renderTickItem(e,t,r){var n,a=(0,p.$)(t.className,"recharts-cartesian-axis-tick-value");if(d.isValidElement(e))n=d.cloneElement(e,by(by({},t),{},{className:a}));else if("function"==typeof e)n=e(by(by({},t),{},{className:a}));else{var i="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(i=(0,p.$)(i,e.className)),n=d.createElement(pS,bp({},t,{className:i}),r)}return n}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:n,stroke:a,tick:i,tickFormatter:o,unit:l}=this.props,c=bu(by(by({},this.props),{},{ticks:r}),e,t),s=this.getTickTextAnchor(),u=this.getTickVerticalAnchor(),f=F(this.props,!1),h=F(i,!1),v=by(by({},f),{},{fill:"none"},F(n,!1)),m=c.map((e,t)=>{var{line:r,tick:m}=this.getTickLineCoord(e),g=by(by(by(by({textAnchor:s,verticalAnchor:u},f),{},{stroke:"none",fill:a},h),m),{},{index:t,payload:e,visibleTicksCount:c.length,tickFormatter:o});return d.createElement(q,bp({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},_(this.props,e,t)),n&&d.createElement("line",bp({},v,r,{className:(0,p.$)("recharts-cartesian-axis-tick-line",y()(n,"className"))})),i&&bg.renderTickItem(i,g,"".concat("function"==typeof o?o(e.value,t):e.value).concat(l||"")))});return m.length>0?d.createElement("g",{className:"recharts-cartesian-axis-ticks"},m):null}render(){var{axisLine:e,width:t,height:r,className:n,hide:a}=this.props;if(a)return null;var{ticks:i}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:d.createElement(q,{className:(0,p.$)("recharts-cartesian-axis",n),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,a=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||a!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,i),pI.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=d.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}bm(bg,"displayName","CartesianAxis"),bm(bg,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var bb=["x1","y1","x2","y2","key"],bx=["offset"],bw=["xAxisId","yAxisId"],bO=["xAxisId","yAxisId"];function bP(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bj(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bP(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bP(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bE(){return(bE=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function bS(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}var bk=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:n,y:a,width:i,height:o,ry:l}=e;return d.createElement("rect",{x:n,y:a,ry:l,width:i,height:o,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function bA(e,t){var r;if(d.isValidElement(e))r=d.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:n,y1:a,x2:i,y2:o,key:l}=t,c=F(bS(t,bb),!1),{offset:s}=c,u=bS(c,bx);r=d.createElement("line",bE({},u,{x1:n,y1:a,x2:i,y2:o,fill:"none",key:l}))}return r}function bM(e){var{x:t,width:r,horizontal:n=!0,horizontalPoints:a}=e;if(!n||!a||!a.length)return null;var{xAxisId:i,yAxisId:o}=e,l=bS(e,bw),c=a.map((e,a)=>bA(n,bj(bj({},l),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(a),index:a})));return d.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function bT(e){var{y:t,height:r,vertical:n=!0,verticalPoints:a}=e;if(!n||!a||!a.length)return null;var{xAxisId:i,yAxisId:o}=e,l=bS(e,bO),c=a.map((e,a)=>bA(n,bj(bj({},l),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(a),index:a})));return d.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function bC(e){var{horizontalFill:t,fillOpacity:r,x:n,y:a,width:i,height:o,horizontalPoints:l,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var s=l.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var u=s.map((e,l)=>{var c=s[l+1]?s[l+1]-e:a+o-e;if(c<=0)return null;var u=l%t.length;return d.createElement("rect",{key:"react-".concat(l),y:e,x:n,height:c,width:i,stroke:"none",fill:t[u],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return d.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},u)}function bD(e){var{vertical:t=!0,verticalFill:r,fillOpacity:n,x:a,y:i,width:o,height:l,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var s=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var u=s.map((e,t)=>{var c=s[t+1]?s[t+1]-e:a+o-e;if(c<=0)return null;var u=t%r.length;return d.createElement("rect",{key:"react-".concat(t),x:e,y:i,width:c,height:l,stroke:"none",fill:r[u],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return d.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},u)}var bN=(e,t)=>{var{xAxis:r,width:n,height:a,offset:i}=e;return rJ(bu(bj(bj(bj({},bg.defaultProps),r),{},{ticks:rQ(r,!0),viewBox:{x:0,y:0,width:n,height:a}})),i.left,i.left+i.width,t)},bI=(e,t)=>{var{yAxis:r,width:n,height:a,offset:i}=e;return rJ(bu(bj(bj(bj({},bg.defaultProps),r),{},{ticks:rQ(r,!0),viewBox:{x:0,y:0,width:n,height:a}})),i.top,i.top+i.height,t)},b_={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function bL(e){var t=nP(),r=nj(),n=nO(),a=bj(bj({},aI(e,b_)),{},{x:x(e.x)?e.x:n.left,y:x(e.y)?e.y:n.top,width:x(e.width)?e.width:n.width,height:x(e.height)?e.height:n.height}),{xAxisId:i,yAxisId:o,x:l,y:c,width:s,height:u,syncWithTicks:f,horizontalValues:p,verticalValues:h}=a,y=nv(),v=eB(e=>u8(e,"xAxis",i,y)),m=eB(e=>u8(e,"yAxis",o,y));if(!x(s)||s<=0||!x(u)||u<=0||!x(l)||l!==+l||!x(c)||c!==+c)return null;var g=a.verticalCoordinatesGenerator||bN,b=a.horizontalCoordinatesGenerator||bI,{horizontalPoints:w,verticalPoints:O}=a;if((!w||!w.length)&&"function"==typeof b){var P=p&&p.length,j=b({yAxis:m?bj(bj({},m),{},{ticks:P?p:m.ticks}):void 0,width:t,height:r,offset:n},!!P||f);d8(Array.isArray(j),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof j,"]")),Array.isArray(j)&&(w=j)}if((!O||!O.length)&&"function"==typeof g){var E=h&&h.length,S=g({xAxis:v?bj(bj({},v),{},{ticks:E?h:v.ticks}):void 0,width:t,height:r,offset:n},!!E||f);d8(Array.isArray(S),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof S,"]")),Array.isArray(S)&&(O=S)}return d.createElement("g",{className:"recharts-cartesian-grid"},d.createElement(bk,{fill:a.fill,fillOpacity:a.fillOpacity,x:a.x,y:a.y,width:a.width,height:a.height,ry:a.ry}),d.createElement(bC,bE({},a,{horizontalPoints:w})),d.createElement(bD,bE({},a,{verticalPoints:O})),d.createElement(bM,bE({},a,{offset:n,horizontalPoints:w,xAxis:v,yAxis:m})),d.createElement(bT,bE({},a,{offset:n,verticalPoints:O,xAxis:v,yAxis:m})))}bL.displayName="CartesianGrid";var bR=(e,t,r,n)=>fr(e,"xAxis",t,n),bz=(e,t,r,n)=>ft(e,"xAxis",t,n),bK=(e,t,r,n)=>fr(e,"yAxis",r,n),bB=(e,t,r,n)=>ft(e,"yAxis",r,n),b$=e3([nS,bR,bK,bz,bB],(e,t,r,n,a)=>rZ(e,"xAxis")?r7(t,n,!1):r7(r,a,!1)),bF=e3([sZ,(e,t,r,n,a)=>a],(e,t)=>{if(e.some(e=>"line"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),bU=e3([nS,bR,bK,bz,bB,bF,b$,cZ],(e,t,r,n,a,i,o,l)=>{var c,{chartData:s,dataStartIndex:u,dataEndIndex:f}=l;if(null!=i&&null!=t&&null!=r&&null!=n&&null!=a&&0!==n.length&&0!==a.length&&null!=o){var{dataKey:d,data:p}=i;if(null!=(c=null!=p&&p.length>0?p:null==s?void 0:s.slice(u,f+1)))return function(e){var{layout:t,xAxis:r,yAxis:n,xAxisTicks:a,yAxisTicks:i,dataKey:o,bandSize:l,displayedData:c}=e;return c.map((e,c)=>{var s=rG(e,o);if("horizontal"===t)return{x:r5({axis:r,ticks:a,bandSize:l,entry:e,index:c}),y:null==s?null:n.scale(s),value:s,payload:e};return{x:null==s?null:r.scale(s),y:r5({axis:n,ticks:i,bandSize:l,entry:e,index:c}),value:s,payload:e}})}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:a,dataKey:d,bandSize:o,displayedData:c})}}),bW=["type","layout","connectNulls","needClip"],bV=["activeDot","animateNewValues","animationBegin","animationDuration","animationEasing","connectNulls","dot","hide","isAnimationActive","label","legendType","xAxisId","yAxisId"];function bX(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function bH(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function bq(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?bH(Object(r),!0).forEach(function(t){bY(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):bH(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function bY(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function bG(){return(bG=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function bZ(e){var{dataKey:t,data:r,stroke:n,strokeWidth:a,fill:i,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:a,fill:i,dataKey:t,nameKey:void 0,name:ne(o,t),hide:l,type:e.tooltipType,color:e.stroke,unit:c}}}var bJ=(e,t)=>"".concat(t,"px ").concat(e-t,"px");function bQ(e){var{clipPathId:t,points:r,props:n}=e,{dot:a,dataKey:i,needClip:o}=n;if(null==r||!a&&1!==r.length)return null;var l=$(a),c=F(n,!1),s=F(a,!0),u=r.map((e,t)=>{var n,o=bq(bq(bq({key:"dot-".concat(t),r:3},c),s),{},{index:t,cx:e.x,cy:e.y,dataKey:i,value:e.value,payload:e.payload,points:r});if(d.isValidElement(a))n=d.cloneElement(a,o);else if("function"==typeof a)n=a(o);else{var l=(0,p.$)("recharts-line-dot","boolean"!=typeof a?a.className:"");n=d.createElement(p1,bG({},o,{className:l}))}return n}),f={clipPath:o?"url(#clipPath-".concat(l?"":"dots-").concat(t,")"):null};return d.createElement(q,bG({className:"recharts-line-dots",key:"dots"},f),u)}function b0(e){var{clipPathId:t,pathRef:r,points:n,strokeDasharray:a,props:i,showLabels:o}=e,{type:l,layout:c,connectNulls:s,needClip:u}=i,f=bq(bq({},F(bX(i,bW),!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:u?"url(#clipPath-".concat(t,")"):null,points:n,type:l,layout:c,connectNulls:s,strokeDasharray:null!=a?a:i.strokeDasharray});return d.createElement(d.Fragment,null,(null==n?void 0:n.length)>1&&d.createElement(aA,bG({},f,{pathRef:r})),d.createElement(bQ,{points:n,clipPathId:t,props:i}),o&&pV.renderCallByParent(i,n))}function b1(e){var{clipPathId:t,props:r,pathRef:n,previousPointsRef:a,longestAnimatedLengthRef:i}=e,{points:o,strokeDasharray:l,isAnimationActive:c,animationBegin:s,animationDuration:u,animationEasing:f,animateNewValues:p,width:h,height:y,onAnimationEnd:v,onAnimationStart:m}=r,g=a.current,b=yN(r,"recharts-line-"),[x,w]=(0,d.useState)(!1),O=(0,d.useCallback)(()=>{"function"==typeof v&&v(),w(!1)},[v]),P=(0,d.useCallback)(()=>{"function"==typeof m&&m(),w(!0)},[m]),j=function(e){try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}(n.current),E=i.current;return d.createElement(a3,{begin:s,duration:u,isActive:c,easing:f,from:{t:0},to:{t:1},onAnimationEnd:O,onAnimationStart:P,key:b},e=>{var c,{t:s}=e,u=Math.min(S(E,j+E)(s),j);if(c=l?((e,t,r)=>{var n=r.reduce((e,t)=>e+t);if(!n)return bJ(t,e);for(var a=Math.floor(e/n),i=e%n,o=t-e,l=[],c=0,s=0;c<r.length;s+=r[c],++c)if(s+r[c]>i){l=[...r.slice(0,c),i-s];break}var u=l.length%2==0?[0,o]:[o];return[...function(e,t){for(var r=e.length%2!=0?[...e,0]:e,n=[],a=0;a<t;++a)n=[...n,...r];return n}(r,a),...l,...u].map(e=>"".concat(e,"px")).join(", ")})(u,j,"".concat(l).split(/[,\s]+/gim).map(e=>parseFloat(e))):bJ(j,u),g){var f=g.length/o.length,v=1===s?o:o.map((e,t)=>{var r=Math.floor(t*f);if(g[r]){var n=g[r],a=S(n.x,e.x),i=S(n.y,e.y);return bq(bq({},e),{},{x:a(s),y:i(s)})}if(p){var o=S(2*h,e.x),l=S(y/2,e.y);return bq(bq({},e),{},{x:o(s),y:l(s)})}return bq(bq({},e),{},{x:e.x,y:e.y})});return a.current=v,d.createElement(b0,{props:r,points:v,clipPathId:t,pathRef:n,showLabels:!x,strokeDasharray:c})}return s>0&&j>0&&(a.current=o,i.current=u),d.createElement(b0,{props:r,points:o,clipPathId:t,pathRef:n,showLabels:!x,strokeDasharray:c})})}function b2(e){var{clipPathId:t,props:r}=e,{points:n,isAnimationActive:a}=r,i=(0,d.useRef)(null),o=(0,d.useRef)(0),l=(0,d.useRef)(null),c=i.current;return a&&n&&n.length&&c!==n?d.createElement(b1,{props:r,clipPathId:t,previousPointsRef:i,longestAnimatedLengthRef:o,pathRef:l}):d.createElement(b0,{props:r,points:n,clipPathId:t,pathRef:l,showLabels:!0})}var b5=(e,t)=>({x:e.x,y:e.y,value:e.value,errorVal:rG(e.payload,t)});class b3 extends d.Component{render(){var e,{hide:t,dot:r,points:n,className:a,xAxisId:i,yAxisId:o,top:l,left:c,width:s,height:u,id:f,needClip:h,layout:y}=this.props;if(t)return null;var v=(0,p.$)("recharts-line",a),m=null==f?this.id:f,{r:g=3,strokeWidth:b=2}=null!=(e=F(r,!1))?e:{r:3,strokeWidth:2},x=$(r),w=2*g+b;return d.createElement(d.Fragment,null,d.createElement(q,{className:v},h&&d.createElement("defs",null,d.createElement(vJ,{clipPathId:m,xAxisId:i,yAxisId:o}),!x&&d.createElement("clipPath",{id:"clipPath-dots-".concat(m)},d.createElement("rect",{x:c-w/2,y:l-w/2,width:s+w,height:u+w}))),d.createElement(b2,{props:this.props,clipPathId:m}),d.createElement(vB,{direction:"horizontal"===y?"y":"x"},d.createElement(vD,{xAxisId:i,yAxisId:o,data:n,dataPointFormatter:b5,errorBarOffset:0},this.props.children))),d.createElement(y5,{activeDot:this.props.activeDot,points:n,mainColor:this.props.stroke,itemDataKey:this.props.dataKey}))}constructor(){super(...arguments),bY(this,"id",P("recharts-line-"))}}var b4={activeDot:!0,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!0,fill:"#fff",hide:!1,isAnimationActive:!n4.isSsr,label:!1,legendType:"line",stroke:"#3182bd",strokeWidth:1,xAxisId:0,yAxisId:0};function b6(e){var t=aI(e,b4),{activeDot:r,animateNewValues:n,animationBegin:a,animationDuration:i,animationEasing:o,connectNulls:l,dot:c,hide:s,isAnimationActive:u,label:f,legendType:p,xAxisId:h,yAxisId:y}=t,v=bX(t,bV),{needClip:m}=vZ(h,y),{height:g,width:b,x:x,y:w}=yQ(),O=nk(),P=nv(),j=(0,d.useMemo)(()=>({dataKey:e.dataKey,data:e.data}),[e.dataKey,e.data]),E=eB(e=>bU(e,h,y,P,j));return"horizontal"!==O&&"vertical"!==O?null:d.createElement(b3,bG({},v,{connectNulls:l,dot:c,activeDot:r,animateNewValues:n,animationBegin:a,animationDuration:i,animationEasing:o,isAnimationActive:u,hide:s,label:f,legendType:p,xAxisId:h,yAxisId:y,points:E,layout:O,height:g,width:b,left:x,top:w,needClip:m}))}class b8 extends d.PureComponent{render(){return d.createElement(vN,{type:"line",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},d.createElement(yC,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:t,type:a,color:n,value:ne(r,t),payload:e}]})(this.props)}),d.createElement(yM,{fn:bZ,args:this.props}),d.createElement(b6,this.props))}}bY(b8,"displayName","Line"),bY(b8,"defaultProps",b4);var b7=(e,t,r,n)=>fr(e,"xAxis",t,n),b9=(e,t,r,n)=>ft(e,"xAxis",t,n),xe=(e,t,r,n)=>fr(e,"yAxis",r,n),xt=(e,t,r,n)=>ft(e,"yAxis",r,n),xr=e3([nS,b7,xe,b9,xt],(e,t,r,n,a)=>rZ(e,"xAxis")?r7(t,n,!1):r7(r,a,!1)),xn=e3([sZ,(e,t,r,n,a)=>a],(e,t)=>{if(e.some(e=>"area"===e.type&&t.dataKey===e.dataKey&&r2(t.stackId)===e.stackId&&t.data===e.data))return t}),xa=e3([nS,b7,xe,b9,xt,(e,t,r,n,a)=>{if(null!=(o=rZ(nS(e),"xAxis")?ur(e,"yAxis",r,n):ur(e,"xAxis",t,n))){var i,o,{dataKey:l,stackId:c}=a;if(null!=c){var s=null==(i=o[c])?void 0:i.stackedData;return null==s?void 0:s.find(e=>e.key===l)}}},cZ,xr,xn],(e,t,r,n,a,i,o,l,c)=>{var s,{chartData:u,dataStartIndex:f,dataEndIndex:d}=o;if(null!=c&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=r&&null!=n&&null!=a&&0!==n.length&&0!==a.length&&null!=l){var{data:p}=c;if(null!=(s=p&&p.length>0?p:null==u?void 0:u.slice(f,d+1)))return function(e){var t,{areaSettings:{connectNulls:r,baseValue:n,dataKey:a},stackedData:i,layout:o,chartBaseValue:l,xAxis:c,yAxis:s,displayedData:u,dataStartIndex:f,xAxisTicks:d,yAxisTicks:p,bandSize:h}=e,y=i&&i.length,v=((e,t,r,n,a)=>{var i=null!=r?r:t;if(x(i))return i;var o="horizontal"===e?a:n,l=o.scale.domain();if("number"===o.type){var c=Math.max(l[0],l[1]),s=Math.min(l[0],l[1]);return"dataMin"===i?s:"dataMax"===i||c<0?c:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===i?l[0]:"dataMax"===i?l[1]:l[0]})(o,l,n,c,s),m="horizontal"===o,g=!1,b=u.map((e,t)=>{y?n=i[f+t]:Array.isArray(n=rG(e,a))?g=!0:n=[v,n];var n,o=null==n[1]||y&&!r&&null==rG(e,a);return m?{x:r5({axis:c,ticks:d,bandSize:h,entry:e,index:t}),y:o?null:s.scale(n[1]),value:n,payload:e}:{x:o?null:c.scale(n[1]),y:r5({axis:s,ticks:p,bandSize:h,entry:e,index:t}),value:n,payload:e}});return t=y||g?b.map(e=>{var t=Array.isArray(e.value)?e.value[0]:null;return m?{x:e.x,y:null!=t&&null!=e.y?s.scale(t):null}:{x:null!=t?c.scale(t):null,y:e.y}}):m?s.scale(v):c.scale(v),{points:b,baseLine:t,isRange:g}}({layout:e,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:a,dataStartIndex:f,areaSettings:c,stackedData:i,displayedData:s,chartBaseValue:void 0,bandSize:l})}}),xi=["layout","type","stroke","connectNulls","isRange"],xo=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function xl(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function xc(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function xs(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xc(Object(r),!0).forEach(function(t){xu(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xc(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function xu(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xf(){return(xf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function xd(e,t){return e&&"none"!==e?e:t}function xp(e){var{dataKey:t,data:r,stroke:n,strokeWidth:a,fill:i,name:o,hide:l,unit:c}=e;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:n,strokeWidth:a,fill:i,dataKey:t,nameKey:void 0,name:ne(o,t),hide:l,type:e.tooltipType,color:xd(n,i),unit:c}}}function xh(e){var{clipPathId:t,points:r,props:n}=e,{needClip:a,dot:i,dataKey:o}=n;if(null==r||!i&&1!==r.length)return null;var l=$(i),c=F(n,!1),s=F(i,!0),u=r.map((e,t)=>{var n,a=xs(xs(xs({key:"dot-".concat(t),r:3},c),s),{},{index:t,cx:e.x,cy:e.y,dataKey:o,value:e.value,payload:e.payload,points:r});if(d.isValidElement(i))n=d.cloneElement(i,a);else if("function"==typeof i)n=i(a);else{var l=(0,p.$)("recharts-area-dot","boolean"!=typeof i?i.className:"");n=d.createElement(p1,xf({},a,{className:l}))}return n}),f={clipPath:a?"url(#clipPath-".concat(l?"":"dots-").concat(t,")"):void 0};return d.createElement(q,xf({className:"recharts-area-dots"},f),u)}function xy(e){var{points:t,baseLine:r,needClip:n,clipPathId:a,props:i,showLabels:o}=e,{layout:l,type:c,stroke:s,connectNulls:u,isRange:f}=i,p=xl(i,xi);return d.createElement(d.Fragment,null,(null==t?void 0:t.length)>1&&d.createElement(q,{clipPath:n?"url(#clipPath-".concat(a,")"):void 0},d.createElement(aA,xf({},F(p,!0),{points:t,connectNulls:u,type:c,baseLine:r,layout:l,stroke:"none",className:"recharts-area-area"})),"none"!==s&&d.createElement(aA,xf({},F(i,!1),{className:"recharts-area-curve",layout:l,type:c,connectNulls:u,fill:"none",points:t})),"none"!==s&&f&&d.createElement(aA,xf({},F(i,!1),{className:"recharts-area-curve",layout:l,type:c,connectNulls:u,fill:"none",points:r}))),d.createElement(xh,{points:t,props:i,clipPathId:a}),o&&pV.renderCallByParent(i,t))}function xv(e){var{alpha:t,baseLine:r,points:n,strokeWidth:a}=e,i=n[0].y,o=n[n.length-1].y;if(!ab(i)||!ab(o))return null;var l=t*Math.abs(i-o),c=Math.max(...n.map(e=>e.x||0));return(x(r)?c=Math.max(r,c):r&&Array.isArray(r)&&r.length&&(c=Math.max(...r.map(e=>e.x||0),c)),x(c))?d.createElement("rect",{x:0,y:i<o?i:i-l,width:c+(a?parseInt("".concat(a),10):1),height:Math.floor(l)}):null}function xm(e){var{alpha:t,baseLine:r,points:n,strokeWidth:a}=e,i=n[0].x,o=n[n.length-1].x;if(!ab(i)||!ab(o))return null;var l=t*Math.abs(i-o),c=Math.max(...n.map(e=>e.y||0));return(x(r)?c=Math.max(r,c):r&&Array.isArray(r)&&r.length&&(c=Math.max(...r.map(e=>e.y||0),c)),x(c))?d.createElement("rect",{x:i<o?i:i-l,y:0,width:l,height:Math.floor(c+(a?parseInt("".concat(a),10):1))}):null}function xg(e){var{alpha:t,layout:r,points:n,baseLine:a,strokeWidth:i}=e;return"vertical"===r?d.createElement(xv,{alpha:t,points:n,baseLine:a,strokeWidth:i}):d.createElement(xm,{alpha:t,points:n,baseLine:a,strokeWidth:i})}function xb(e){var{needClip:t,clipPathId:r,props:n,previousPointsRef:a,previousBaselineRef:i}=e,{points:o,baseLine:l,isAnimationActive:c,animationBegin:s,animationDuration:u,animationEasing:f,onAnimationStart:p,onAnimationEnd:h}=n,y=yN(n,"recharts-area-"),[v,m]=(0,d.useState)(!0),b=(0,d.useCallback)(()=>{"function"==typeof h&&h(),m(!1)},[h]),w=(0,d.useCallback)(()=>{"function"==typeof p&&p(),m(!0)},[p]),O=a.current,P=i.current;return d.createElement(a3,{begin:s,duration:u,isActive:c,easing:f,from:{t:0},to:{t:1},onAnimationEnd:b,onAnimationStart:w,key:y},e=>{var{t:c}=e;if(O){var s,u=O.length/o.length,f=1===c?o:o.map((e,t)=>{var r=Math.floor(t*u);if(O[r]){var n=O[r];return xs(xs({},e),{},{x:k(n.x,e.x,c),y:k(n.y,e.y,c)})}return e});if(x(l))s=k(P,l,c);else s=null==l||g(l)?k(P,0,c):l.map((e,t)=>{var r=Math.floor(t*u);if(Array.isArray(P)&&P[r]){var n=P[r];return xs(xs({},e),{},{x:k(n.x,e.x,c),y:k(n.y,e.y,c)})}return e});return c>0&&(a.current=f,i.current=s),d.createElement(xy,{points:f,baseLine:s,needClip:t,clipPathId:r,props:n,showLabels:!v})}return c>0&&(a.current=o,i.current=l),d.createElement(q,null,d.createElement("defs",null,d.createElement("clipPath",{id:"animationClipPath-".concat(r)},d.createElement(xg,{alpha:c,points:o,baseLine:l,layout:n.layout,strokeWidth:n.strokeWidth}))),d.createElement(q,{clipPath:"url(#animationClipPath-".concat(r,")")},d.createElement(xy,{points:o,baseLine:l,needClip:t,clipPathId:r,props:n,showLabels:!0})))})}function xx(e){var{needClip:t,clipPathId:r,props:n}=e,{points:a,baseLine:i,isAnimationActive:o}=n,l=(0,d.useRef)(null),c=(0,d.useRef)(),s=l.current,u=c.current;return o&&a&&a.length&&(s!==a||u!==i)?d.createElement(xb,{needClip:t,clipPathId:r,props:n,previousPointsRef:l,previousBaselineRef:c}):d.createElement(xy,{points:a,baseLine:i,needClip:t,clipPathId:r,props:n,showLabels:!0})}class xw extends d.PureComponent{render(){var e,{hide:t,dot:r,points:n,className:a,top:i,left:o,needClip:l,xAxisId:c,yAxisId:s,width:u,height:f,id:h,baseLine:y}=this.props;if(t)return null;var v=(0,p.$)("recharts-area",a),m=null==h?this.id:h,{r:g=3,strokeWidth:b=2}=null!=(e=F(r,!1))?e:{r:3,strokeWidth:2},x=$(r),w=2*g+b;return d.createElement(d.Fragment,null,d.createElement(q,{className:v},l&&d.createElement("defs",null,d.createElement(vJ,{clipPathId:m,xAxisId:c,yAxisId:s}),!x&&d.createElement("clipPath",{id:"clipPath-dots-".concat(m)},d.createElement("rect",{x:o-w/2,y:i-w/2,width:u+w,height:f+w}))),d.createElement(xx,{needClip:l,clipPathId:m,props:this.props})),d.createElement(y5,{points:n,mainColor:xd(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(y)&&d.createElement(y5,{points:y,mainColor:xd(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}constructor(){super(...arguments),xu(this,"id",P("recharts-area-"))}}var xO={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!n4.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function xP(e){var t,r=aI(e,xO),{activeDot:n,animationBegin:a,animationDuration:i,animationEasing:o,connectNulls:l,dot:c,fill:s,fillOpacity:u,hide:f,isAnimationActive:p,legendType:h,stroke:y,xAxisId:v,yAxisId:m}=r,g=xl(r,xo),b=nk(),x=dx(),{needClip:w}=vZ(v,m),O=nv(),P=(0,d.useMemo)(()=>({baseValue:e.baseValue,stackId:e.stackId,connectNulls:l,data:e.data,dataKey:e.dataKey}),[e.baseValue,e.stackId,l,e.data,e.dataKey]),{points:j,isRange:E,baseLine:S}=null!=(t=eB(e=>xa(e,v,m,O,P)))?t:{},{height:k,width:A,x:M,y:T}=yQ();return"horizontal"!==b&&"vertical"!==b||"AreaChart"!==x&&"ComposedChart"!==x?null:d.createElement(xw,xf({},g,{activeDot:n,animationBegin:a,animationDuration:i,animationEasing:o,baseLine:S,connectNulls:l,dot:c,fill:s,fillOpacity:u,height:k,hide:f,layout:b,isAnimationActive:p,isRange:E,legendType:h,needClip:w,points:j,stroke:y,width:A,left:M,top:T,xAxisId:v,yAxisId:m}))}class xj extends d.PureComponent{render(){return d.createElement(vN,{type:"area",data:this.props.data,dataKey:this.props.dataKey,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,stackId:this.props.stackId,hide:this.props.hide,barSize:void 0},d.createElement(yC,{legendPayload:(e=>{var{dataKey:t,name:r,stroke:n,fill:a,legendType:i,hide:o}=e;return[{inactive:o,dataKey:t,type:i,color:xd(n,a),value:ne(r,t),payload:e}]})(this.props)}),d.createElement(yM,{fn:xp,args:this.props}),d.createElement(xP,this.props))}}function xE(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function xS(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xE(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xE(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}xu(xj,"displayName","Area"),xu(xj,"defaultProps",xO);var xk=t8({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=t.payload},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=t.payload},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=t.payload},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=xS(xS({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:xA,removeXAxis:xM,addYAxis:xT,removeYAxis:xC,addZAxis:xD,removeZAxis:xN,updateYAxisWidth:xI}=xk.actions,x_=xk.reducer;function xL(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xR(e){var t=eL();return(0,d.useEffect)(()=>(t(xD(e)),()=>{t(xN(e))}),[e,t]),null}class xz extends d.Component{render(){return d.createElement(xR,{domain:this.props.domain,id:this.props.zAxisId,dataKey:this.props.dataKey,name:this.props.name,unit:this.props.unit,range:this.props.range,scale:this.props.scale,type:this.props.type,allowDuplicatedCategory:sV.allowDuplicatedCategory,allowDataOverflow:sV.allowDataOverflow,reversed:sV.reversed,includeHidden:sV.includeHidden})}}xL(xz,"displayName","ZAxis"),xL(xz,"defaultProps",{zAxisId:0,range:sV.range,scale:sV.scale,type:sV.type});var xK=["option","isActive"];function xB(){return(xB=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function x$(e){var{option:t,isActive:r}=e,n=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,xK);return"string"==typeof t?d.createElement(yE,xB({option:d.createElement(eE,xB({type:t},n)),isActive:r,shapeType:"symbols"},n)):d.createElement(yE,xB({option:t,isActive:r,shapeType:"symbols"},n))}var xF=e3([sZ,(e,t,r,n,a)=>a],(e,t)=>{if(e.some(e=>"scatter"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),xU=e3([(e,t,r,n,a,i,o)=>cZ(e,t,r,o),(e,t,r,n,a,i,o)=>fr(e,"xAxis",t,o),(e,t,r,n,a,i,o)=>ft(e,"xAxis",t,o),(e,t,r,n,a,i,o)=>fr(e,"yAxis",r,o),(e,t,r,n,a,i,o)=>ft(e,"yAxis",r,o),(e,t,r,n)=>fa(e,"zAxis",n,!1),xF,(e,t,r,n,a,i)=>i],(e,t,r,n,a,i,o,l)=>{var c,{chartData:s,dataStartIndex:u,dataEndIndex:f}=e;if(null!=o&&null!=(c=(null==o?void 0:o.data)!=null&&o.data.length>0?o.data:null==s?void 0:s.slice(u,f+1))&&null!=t&&null!=n&&null!=r&&null!=a&&(null==r?void 0:r.length)!==0&&(null==a?void 0:a.length)!==0)return function(e){var{displayedData:t,xAxis:r,yAxis:n,zAxis:a,scatterSettings:i,xAxisTicks:o,yAxisTicks:l,cells:c}=e,s=null==r.dataKey?i.dataKey:r.dataKey,u=null==n.dataKey?i.dataKey:n.dataKey,f=a&&a.dataKey,d=a?a.range:xz.defaultProps.range,p=d&&d[0],h=r.scale.bandwidth?r.scale.bandwidth():0,y=n.scale.bandwidth?n.scale.bandwidth():0;return t.map((e,t)=>{var d=rG(e,s),v=rG(e,u),m=null!=f&&rG(e,f)||"-",g=[{name:null==r.dataKey?i.name:r.name||r.dataKey,unit:r.unit||"",value:d,payload:e,dataKey:s,type:i.tooltipType},{name:null==n.dataKey?i.name:n.name||n.dataKey,unit:n.unit||"",value:v,payload:e,dataKey:u,type:i.tooltipType}];"-"!==m&&g.push({name:a.name||a.dataKey,unit:a.unit||"",value:m,payload:e,dataKey:f,type:i.tooltipType});var b=r5({axis:r,ticks:o,bandSize:h,entry:e,index:t,dataKey:s}),x=r5({axis:n,ticks:l,bandSize:y,entry:e,index:t,dataKey:u}),w="-"!==m?a.scale(m):p,O=Math.sqrt(Math.max(w,0)/Math.PI);return xY(xY({},e),{},{cx:b,cy:x,x:b-O,y:x-O,width:2*O,height:2*O,size:w,node:{x:d,y:v,z:m},tooltipPayload:g,tooltipPosition:{x:b,y:x},payload:e},c&&c[t]&&c[t].props)})}({displayedData:c,xAxis:t,yAxis:n,zAxis:i,scatterSettings:o,xAxisTicks:r,yAxisTicks:a,cells:l})}),xW=["onMouseEnter","onClick","onMouseLeave"],xV=["animationBegin","animationDuration","animationEasing","hide","isAnimationActive","legendType","lineJointType","lineType","shape","xAxisId","yAxisId","zAxisId"];function xX(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function xH(){return(xH=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function xq(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function xY(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xq(Object(r),!0).forEach(function(t){xG(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xq(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function xG(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function xZ(e){var t,r,{points:n,props:a}=e,{line:i,lineType:o,lineJointType:l}=a;if(!i)return null;var c=F(a,!1),s=F(i,!1);if("joint"===o)t=n.map(e=>({x:e.cx,y:e.cy}));else if("fitting"===o){var{xmin:u,xmax:f,a:p,b:h}=(e=>{if(!e||!e.length)return null;for(var t=e.length,r=0,n=0,a=0,i=0,o=1/0,l=-1/0,c=0,s=0,u=0;u<t;u++)c=e[u].cx||0,s=e[u].cy||0,r+=c,n+=s,a+=c*s,i+=c*c,o=Math.min(o,c),l=Math.max(l,c);var f=t*i!=r*r?(t*a-r*n)/(t*i-r*r):0;return{xmin:o,xmax:l,a:f,b:(n-f*r)/t}})(n);t=[{x:u,y:p*u+h},{x:f,y:p*f+h}]}var y=xY(xY(xY({},c),{},{fill:"none",stroke:c&&c.fill},s),{},{points:t});return r=d.isValidElement(i)?d.cloneElement(i,y):"function"==typeof i?i(y):d.createElement(aA,xH({},y,{type:l})),d.createElement(q,{className:"recharts-scatter-line",key:"recharts-scatter-line"},r)}function xJ(e){var{points:t,showLabels:r,allOtherScatterProps:n}=e,{shape:a,activeShape:i,dataKey:o}=n,l=F(n,!1),c=eB(ds),{onMouseEnter:s,onClick:u,onMouseLeave:f}=n,p=xX(n,xW),h=yS(s,n.dataKey),y=yk(f),v=yA(u,n.dataKey);return null==t?null:d.createElement(d.Fragment,null,d.createElement(xZ,{points:t,props:n}),t.map((e,t)=>{var r=i&&c===String(t),n=r?i:a,s=xY(xY(xY({key:"symbol-".concat(t)},l),e),{},{[nc]:t,[ns]:String(o)});return d.createElement(q,xH({className:"recharts-scatter-symbol"},_(p,e,t),{onMouseEnter:h(e,t),onMouseLeave:y(e,t),onClick:v(e,t),key:"symbol-".concat(null==e?void 0:e.cx,"-").concat(null==e?void 0:e.cy,"-").concat(null==e?void 0:e.size,"-").concat(t)}),d.createElement(x$,xH({option:n,isActive:r},s)))}),r&&pV.renderCallByParent(n,t))}function xQ(e){var{previousPointsRef:t,props:r}=e,{points:n,isAnimationActive:a,animationBegin:i,animationDuration:o,animationEasing:l}=r,c=t.current,s=yN(r,"recharts-scatter-"),[u,f]=(0,d.useState)(!1),p=(0,d.useCallback)(()=>{f(!1)},[]),h=(0,d.useCallback)(()=>{f(!0)},[]);return d.createElement(a3,{begin:i,duration:o,isActive:a,easing:l,from:{t:0},to:{t:1},onAnimationEnd:p,onAnimationStart:h,key:s},e=>{var{t:a}=e,i=1===a?n:n.map((e,t)=>{var r=c&&c[t];if(r){var n=S(r.cx,e.cx),i=S(r.cy,e.cy),o=S(r.size,e.size);return xY(xY({},e),{},{cx:n(a),cy:i(a),size:o(a)})}var l=S(0,e.size);return xY(xY({},e),{},{size:l(a)})});return a>0&&(t.current=i),d.createElement(q,null,d.createElement(xJ,{points:i,allOtherScatterProps:r,showLabels:!u}))})}function x0(e){var{points:t,isAnimationActive:r}=e,n=(0,d.useRef)(null),a=n.current;return r&&t&&t.length&&(!a||a!==t)?d.createElement(xQ,{props:e,previousPointsRef:n}):d.createElement(xJ,{points:t,allOtherScatterProps:e,showLabels:!0})}function x1(e){var{dataKey:t,points:r,stroke:n,strokeWidth:a,fill:i,name:o,hide:l,tooltipType:c}=e;return{dataDefinedOnItem:null==r?void 0:r.map(e=>e.tooltipPayload),positions:null==r?void 0:r.map(e=>e.tooltipPosition),settings:{stroke:n,strokeWidth:a,fill:i,nameKey:void 0,dataKey:t,name:ne(o,t),hide:l,type:c,color:i,unit:""}}}var x2=(e,t,r)=>({x:e.cx,y:e.cy,value:"x"===r?+e.node.x:+e.node.y,errorVal:rG(e,t)});function x5(e){var t=(0,d.useRef)(P("recharts-scatter-")),{hide:r,points:n,className:a,needClip:i,xAxisId:o,yAxisId:l,id:c,children:s}=e;if(r)return null;var u=(0,p.$)("recharts-scatter",a),f=null==c?t.current:c;return d.createElement(q,{className:u,clipPath:i?"url(#clipPath-".concat(f,")"):null},i&&d.createElement("defs",null,d.createElement(vJ,{clipPathId:f,xAxisId:o,yAxisId:l})),d.createElement(vD,{xAxisId:o,yAxisId:l,data:n,dataPointFormatter:x2,errorBarOffset:0},s),d.createElement(q,{key:"recharts-scatter-symbols"},d.createElement(x0,e)))}var x3={xAxisId:0,yAxisId:0,zAxisId:0,legendType:"circle",lineType:"joint",lineJointType:"linear",data:[],shape:"circle",hide:!1,isAnimationActive:!n4.isSsr,animationBegin:0,animationDuration:400,animationEasing:"linear"};function x4(e){var t=aI(e,x3),{animationBegin:r,animationDuration:n,animationEasing:a,hide:i,isAnimationActive:o,legendType:l,lineJointType:c,lineType:s,shape:u,xAxisId:f,yAxisId:p,zAxisId:h}=t,y=xX(t,xV),{needClip:v}=vZ(f,p),m=(0,d.useMemo)(()=>B(e.children,pt),[e.children]),g=(0,d.useMemo)(()=>({name:e.name,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey}),[e.data,e.dataKey,e.name,e.tooltipType]),b=nv(),x=eB(e=>xU(e,f,p,h,g,m,b));return null==v?null:d.createElement(d.Fragment,null,d.createElement(yM,{fn:x1,args:xY(xY({},e),{},{points:x})}),d.createElement(x5,xH({},y,{xAxisId:f,yAxisId:p,zAxisId:h,lineType:s,lineJointType:c,legendType:l,shape:u,hide:i,isAnimationActive:o,animationBegin:r,animationDuration:n,animationEasing:a,points:x,needClip:v})))}class x6 extends d.Component{render(){return d.createElement(vN,{type:"scatter",data:this.props.data,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:this.props.zAxisId,dataKey:this.props.dataKey,stackId:void 0,hide:this.props.hide,barSize:void 0},d.createElement(yC,{legendPayload:(e=>{var{dataKey:t,name:r,fill:n,legendType:a,hide:i}=e;return[{inactive:i,dataKey:t,type:a,color:n,value:ne(r,t),payload:e}]})(this.props)}),d.createElement(x4,this.props))}}xG(x6,"displayName","Scatter"),xG(x6,"defaultProps",x3);var x8=["children"],x7=["dangerouslySetInnerHTML","ticks"];function x9(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function we(){return(we=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function wt(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function wr(e){var t=eL(),r=(0,d.useMemo)(()=>{var{children:t}=e;return wt(e,x8)},[e]),n=eB(e=>sF(e,r.id)),a=r===n;return((0,d.useEffect)(()=>(t(xA(r)),()=>{t(xM(r))}),[r,t]),a)?e.children:null}var wn=e=>{var{xAxisId:t,className:r}=e,n=eB(nh),a=nv(),i="xAxis",o=eB(e=>uV(e,i,t,a)),l=eB(e=>u9(e,i,t,a)),c=eB(e=>uJ(e,t)),s=eB(e=>((e,t)=>{var r=nd(e),n=sF(e,t);if(null!=n){var a=uQ(e,n.orientation,n.mirror)[t];return null==a?{x:r.left,y:0}:{x:r.left,y:a}}})(e,t));if(null==c||null==s)return null;var{dangerouslySetInnerHTML:u,ticks:f}=e,h=wt(e,x7);return d.createElement(bg,we({},h,{scale:o,x:s.x,y:s.y,width:c.width,height:c.height,className:(0,p.$)("recharts-".concat(i," ").concat(i),r),viewBox:n,ticks:l}))},wa=e=>{var t,r,n,a,i;return d.createElement(wr,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(n=e.angle)?n:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(i=e.tick)||i,tickFormatter:e.tickFormatter},d.createElement(wn,e))};class wi extends d.Component{render(){return d.createElement(wa,this.props)}}x9(wi,"displayName","XAxis"),x9(wi,"defaultProps",{allowDataOverflow:s$.allowDataOverflow,allowDecimals:s$.allowDecimals,allowDuplicatedCategory:s$.allowDuplicatedCategory,height:s$.height,hide:!1,mirror:s$.mirror,orientation:s$.orientation,padding:s$.padding,reversed:s$.reversed,scale:s$.scale,tickCount:s$.tickCount,type:s$.type,xAxisId:0});var wo=["dangerouslySetInnerHTML","ticks"];function wl(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function wc(){return(wc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ws(e){var t=eL();return(0,d.useEffect)(()=>(t(xT(e)),()=>{t(xC(e))}),[e,t]),null}var wu=e=>{var t,{yAxisId:r,className:n,width:a,label:i}=e,o=(0,d.useRef)(null),l=(0,d.useRef)(null),c=eB(nh),s=nv(),u=eL(),f="yAxis",h=eB(e=>uV(e,f,r,s)),y=eB(e=>u1(e,r)),v=eB(e=>((e,t)=>{var r=nd(e),n=sW(e,t);if(null!=n){var a=u0(e,n.orientation,n.mirror)[t];return null==a?{x:0,y:r.top}:{x:a,y:r.top}}})(e,r)),m=eB(e=>u9(e,f,r,s));if((0,d.useLayoutEffect)(()=>{if(!("auto"!==a||!y||pN(i)||(0,d.isValidElement)(i))){var e,t=o.current,n=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:c,tickMargin:s}=t.props,f=(e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:a=0,tickMargin:i=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(a+i)+l+(r?n:0))}return 0})({ticks:n,label:l.current,labelGapWithTick:5,tickSize:c,tickMargin:s});Math.round(y.width)!==Math.round(f)&&u(xI({id:r,width:f}))}},[o,null==o||null==(t=o.current)||null==(t=t.tickRefs)?void 0:t.current,null==y?void 0:y.width,y,u,i,r,a]),null==y||null==v)return null;var{dangerouslySetInnerHTML:g,ticks:b}=e,x=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,wo);return d.createElement(bg,wc({},x,{ref:o,labelRef:l,scale:h,x:v.x,y:v.y,width:y.width,height:y.height,className:(0,p.$)("recharts-".concat(f," ").concat(f),n),viewBox:c,ticks:m}))},wf=e=>{var t,r,n,a,i;return d.createElement(d.Fragment,null,d.createElement(ws,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(n=e.angle)?n:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(i=e.tick)||i,tickFormatter:e.tickFormatter}),d.createElement(wu,e))},wd={allowDataOverflow:sU.allowDataOverflow,allowDecimals:sU.allowDecimals,allowDuplicatedCategory:sU.allowDuplicatedCategory,hide:!1,mirror:sU.mirror,orientation:sU.orientation,padding:sU.padding,reversed:sU.reversed,scale:sU.scale,tickCount:sU.tickCount,type:sU.type,width:sU.width,yAxisId:0};class wp extends d.Component{render(){return d.createElement(wf,this.props)}}wl(wp,"displayName","YAxis"),wl(wp,"defaultProps",wd),r(39611);var wh={notify(){},get:()=>[]},wy="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,wv="undefined"!=typeof navigator&&"ReactNative"===navigator.product,wm=wy||wv?d.useLayoutEffect:d.useEffect;Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype;var wg=Symbol.for("react-redux-context"),wb="undefined"!=typeof globalThis?globalThis:{},wx=function(){if(!d.createContext)return{};let e=wb[wg]??=new Map,t=e.get(d.createContext);return t||(t=d.createContext(null),e.set(d.createContext,t)),t}(),ww=function(e){let{children:t,context:r,serverState:n,store:a}=e,i=d.useMemo(()=>{let e=function(e,t){let r,n=wh,a=0,i=!1;function o(){s.onStateChange&&s.onStateChange()}function l(){if(a++,!r){let t,a;r=e.subscribe(o),t=null,a=null,n={clear(){t=null,a=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=a={callback:e,next:null,prev:a};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:a=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){a--,r&&0===a&&(r(),r=void 0,n.clear(),n=wh)}let s={addNestedSub:function(e){l();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return i},trySubscribe:function(){i||(i=!0,l())},tryUnsubscribe:function(){i&&(i=!1,c())},getListeners:()=>n};return s}(a);return{store:a,subscription:e,getServerState:n?()=>n:void 0}},[a,n]),o=d.useMemo(()=>a.getState(),[a]);return wm(()=>{let{subscription:e}=i;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),o!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[i,o]),d.createElement((r||wx).Provider,{value:i},t)},wO=e3([(e,t)=>t,nS,s_,fL,f9,dn,dj,nd],(e,t,r,n,a,i,o,l)=>{if(e&&t&&n&&a&&i){var c=function(e,t,r,n,a){return"horizontal"===r||"vertical"===r?e>=a.left&&e<=a.left+a.width&&t>=a.top&&t<=a.top+a.height?{x:e,y:t}:null:n?((e,t)=>{var r,{x:n,y:a}=e,{radius:i,angle:o}=((e,t)=>{var{x:r,y:n}=e,{cx:a,cy:i}=t,o=((e,t)=>{var{x:r,y:n}=e,{x:a,y:i}=t;return Math.sqrt((r-a)**2+(n-i)**2)})({x:r,y:n},{x:a,y:i});if(o<=0)return{radius:o,angle:0};var l=Math.acos((r-a)/o);return n>i&&(l=2*Math.PI-l),{radius:o,angle:180*l/Math.PI,angleInRadian:l}})({x:n,y:a},t),{innerRadius:l,outerRadius:c}=t;if(i<l||i>c||0===i)return null;var{startAngle:s,endAngle:u}=(e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}})(t),f=o;if(s<=u){for(;f>u;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=u}else{for(;f>s;)f-=360;for(;f<u;)f+=360;r=f>=u&&f<=s}return r?rU(rU({},t),{},{radius:i,angle:((e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))})(f,t)}):null})({x:e,y:t},n):null}(e.chartX,e.chartY,t,r,l);if(c){var s=((e,t,r,n,a)=>{var i,o=-1,l=null!=(i=null==t?void 0:t.length)?i:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=a&&1e-6>=Math.abs(Math.abs(a[1]-a[0])-360))for(var c=0;c<l;c++){var s=c>0?r[c-1].coordinate:r[l-1].coordinate,u=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,d=void 0;if(m(u-s)!==m(f-u)){var p=[];if(m(f-u)===m(a[1]-a[0])){d=f;var h=u+a[1]-a[0];p[0]=Math.min(h,(h+s)/2),p[1]=Math.max(h,(h+s)/2)}else{d=s;var y=f+a[1]-a[0];p[0]=Math.min(u,(y+u)/2),p[1]=Math.max(u,(y+u)/2)}var v=[Math.min(u,(d+u)/2),Math.max(u,(d+u)/2)];if(e>v[0]&&e<=v[1]||e>=p[0]&&e<=p[1]){({index:o}=r[c]);break}}else{var g=Math.min(s,f),b=Math.max(s,f);if(e>(g+u)/2&&e<=(b+u)/2){({index:o}=r[c]);break}}}else if(t){for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}}return o})(((e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius)(c,t),o,i,n,a),u=((e,t,r,n)=>{var a=t.find(e=>e&&e.index===r);if(a){if("horizontal"===e)return{x:a.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:a.coordinate};if("centric"===e){var i=a.coordinate,{radius:o}=n;return rY(rY(rY({},n),rV(n.cx,n.cy,o,i)),{},{angle:i,radius:o})}var l=a.coordinate,{angle:c}=n;return rY(rY(rY({},n),rV(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}})(t,i,s,c);return{activeIndex:String(s),activeCoordinate:u}}}}),wP=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}},wj=tQ("mouseClick"),wE=rM();wE.startListening({actionCreator:wj,effect:(e,t)=>{var r=e.payload,n=wO(t.getState(),wP(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch(fw({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var wS=tQ("mouseMove"),wk=rM();function wA(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}wk.startListening({actionCreator:wS,effect:(e,t)=>{var r=e.payload,n=t.getState(),a=fs(n,n.tooltip.settings.shared),i=wO(n,wP(r));"axis"===a&&((null==i?void 0:i.activeIndex)!=null?t.dispatch(fx({activeIndex:i.activeIndex,activeDataKey:void 0,activeCoordinate:i.activeCoordinate})):t.dispatch(fg()))}});var wM={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},wT=t8({name:"rootProps",initialState:wM,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:wM.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),wC=wT.reducer,{updateOptions:wD}=wT.actions,wN=t8({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:wI}=wN.actions,w_=wN.reducer,wL=tQ("keyDown"),wR=tQ("focus"),wz=rM();wz.startListening({actionCreator:wL,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,a=e.payload;if("ArrowRight"===a||"ArrowLeft"===a||"Enter"===a){var i=Number(fA(n,fW(r))),o=dn(r);if("Enter"===a){var l=dA(r,"axis","hover",String(n.index));t.dispatch(fP({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:l}));return}var c=i+("ArrowRight"===a?1:-1)*("left-to-right"===fi(r)?1:-1);if(null!=o&&!(c>=o.length)&&!(c<0)){var s=dA(r,"axis","hover",String(c));t.dispatch(fP({active:!0,activeIndex:c.toString(),activeDataKey:void 0,activeCoordinate:s}))}}}}}),wz.startListening({actionCreator:wR,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=dA(r,"axis","hover",String("0"));t.dispatch(fP({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}});var wK=tQ("externalEvent"),wB=rM();wB.startListening({actionCreator:wK,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:dh(r),activeDataKey:df(r),activeIndex:ds(r),activeLabel:du(r),activeTooltipIndex:ds(r),isTooltipActive:dy(r)};e.payload.handler(n,e.payload.reactEvent)}}});var w$=e3([fD],e=>e.tooltipItemPayloads),wF=e3([w$,fC,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var a=e.find(e=>e.settings.dataKey===n);if(null!=a){var{positions:i}=a;if(null!=i)return t(i,r)}}),wU=tQ("touchMove"),wW=rM();wW.startListening({actionCreator:wU,effect:(e,t)=>{var r=e.payload,n=t.getState(),a=fs(n,n.tooltip.settings.shared);if("axis"===a){var i=wO(n,wP({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==i?void 0:i.activeIndex)!=null&&t.dispatch(fx({activeIndex:i.activeIndex,activeDataKey:void 0,activeCoordinate:i.activeCoordinate}))}else if("item"===a){var o,l=r.touches[0],c=document.elementFromPoint(l.clientX,l.clientY);if(!c||!c.getAttribute)return;var s=c.getAttribute(nc),u=null!=(o=c.getAttribute(ns))?o:void 0,f=wF(t.getState(),s,u);t.dispatch(fv({activeDataKey:u,activeIndex:s,activeCoordinate:f}))}}});var wV=to({brush:gl,cartesianAxis:x_,chartData:dZ,graphicalItems:yc,layout:rL,legend:n_,options:dW,polarAxis:hT,polarOptions:w_,referenceElements:g$,rootProps:wC,tooltip:fj}),wX=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return function(e){let t,r,n,a=function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:a=!0}=e??{},i=new t0;return t&&("boolean"==typeof t?i.push(tu):i.push(ts(t.extraArgument))),i},{reducer:i,middleware:o,devTools:l=!0,duplicateMiddlewareCheck:c=!0,preloadedState:s,enhancers:u}=e||{};if("function"==typeof i)t=i;else if(ti(i))t=to(i);else throw Error(rT(1));r="function"==typeof o?o(a):a();let f=tl;l&&(f=tJ({trace:!1,..."object"==typeof l&&l}));let d=(n=function(...e){return t=>(r,n)=>{let a=t(r,n),i=()=>{throw Error(tt(15))},o={getState:a.getState,dispatch:(e,...t)=>i(e,...t)};return i=tl(...e.map(e=>e(o)))(a.dispatch),{...a,dispatch:i}}}(...r),function(e){let{autoBatch:t=!0}=e??{},r=new t0(n);return t&&r.push(((e={type:"raf"})=>t=>(...r)=>{let n=t(...r),a=!0,i=!1,o=!1,l=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:t5(10):"callback"===e.type?e.queueNotification:t5(e.timeout),s=()=>{o=!1,i&&(i=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>a&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(i=!(a=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,c(s)),n.dispatch(e)}finally{a=!0}}})})("object"==typeof t?t:void 0)),r});return function e(t,r,n){if("function"!=typeof t)throw Error(tt(2));if("function"==typeof r&&"function"==typeof n||"function"==typeof n&&"function"==typeof arguments[3])throw Error(tt(0));if("function"==typeof r&&void 0===n&&(n=r,r=void 0),void 0!==n){if("function"!=typeof n)throw Error(tt(1));return n(e)(t,r)}let a=t,i=r,o=new Map,l=o,c=0,s=!1;function u(){l===o&&(l=new Map,o.forEach((e,t)=>{l.set(t,e)}))}function f(){if(s)throw Error(tt(3));return i}function d(e){if("function"!=typeof e)throw Error(tt(4));if(s)throw Error(tt(5));let t=!0;u();let r=c++;return l.set(r,e),function(){if(t){if(s)throw Error(tt(6));t=!1,u(),l.delete(r),o=null}}}function p(e){if(!ti(e))throw Error(tt(7));if(void 0===e.type)throw Error(tt(8));if("string"!=typeof e.type)throw Error(tt(17));if(s)throw Error(tt(9));try{s=!0,i=a(i,e)}finally{s=!1}return(o=l).forEach(e=>{e()}),e}return p({type:ta.INIT}),{dispatch:p,subscribe:d,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw Error(tt(10));a=e,p({type:ta.REPLACE})},[tr]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(tt(11));function t(){e.next&&e.next(f())}return t(),{unsubscribe:d(t)}},[tr](){return this}}}}}(t,s,f(..."function"==typeof u?u(d):d()))}({reducer:wV,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([wE.middleware,wk.middleware,wz.middleware,wB.middleware,wW.middleware]),devTools:{serialize:{replacer:wA},name:"recharts-".concat(t)}})};function wH(e){var{preloadedState:t,children:r,reduxStoreName:n}=e,a=nv(),i=(0,d.useRef)(null);return a?r:(null==i.current&&(i.current=wX(t,n)),d.createElement(ww,{context:eI,store:i.current},r))}function wq(e){var{layout:t,width:r,height:n,margin:a}=e,i=eL(),o=nv();return(0,d.useEffect)(()=>{o||(i(rN(t)),i(rI({width:r,height:n})),i(rD(a)))},[i,o,t,r,n,a]),null}function wY(e){var t=eL();return(0,d.useEffect)(()=>{t(wD(e))},[t,e]),null}var wG=["children"];function wZ(){return(wZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var wJ={width:"100%",height:"100%"},wQ=(0,d.forwardRef)((e,t)=>{var r,n,a=nP(),i=nj(),o=n6();if(!ax(a)||!ax(i))return null;var{children:l,otherAttributes:c,title:s,desc:u}=e;return r="number"==typeof c.tabIndex?c.tabIndex:o?0:void 0,n="string"==typeof c.role?c.role:o?"application":void 0,d.createElement(V,wZ({},c,{title:s,desc:u,role:n,tabIndex:r,width:a,height:i,style:wJ,ref:t}),l)}),w0=e=>{var{children:t}=e,r=eB(nb);if(!r)return null;var{width:n,height:a,y:i,x:o}=r;return d.createElement(V,{width:n,height:a,x:o,y:i},t)},w1=(0,d.forwardRef)((e,t)=>{var{children:r}=e,n=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,wG);return nv()?d.createElement(w0,null,r):d.createElement(wQ,wZ({ref:t},n),r)});function w2(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var w5=(0,d.forwardRef)((e,t)=>{var{children:r,className:n,height:a,onClick:i,onContextMenu:o,onDoubleClick:l,onMouseDown:c,onMouseEnter:s,onMouseLeave:u,onMouseMove:f,onMouseUp:h,onTouchEnd:y,onTouchMove:v,onTouchStart:m,style:g,width:b}=e,x=eL(),[w,O]=(0,d.useState)(null),[P,j]=(0,d.useState)(null);!function(){var e,t,r,n,a,i,o,l,c,s,u,f=eL();(0,d.useEffect)(()=>{f(dV())},[f]),e=eB(sy),t=eB(sm),r=eL(),n=eB(sv),a=eB(dn),i=nk(),o=nx(),l=eB(e=>e.rootProps.className),(0,d.useEffect)(()=>{if(null==e)return dJ;var l=(l,c,s)=>{if(t!==s&&e===l){if("index"===n)return void r(c);if(null!=a){if("function"==typeof n){var u,f=n(a,{activeTooltipIndex:null==c.payload.index?void 0:Number(c.payload.index),isTooltipActive:c.payload.active,activeIndex:null==c.payload.index?void 0:Number(c.payload.index),activeLabel:c.payload.label,activeDataKey:c.payload.dataKey,activeCoordinate:c.payload.coordinate});u=a[f]}else"value"===n&&(u=a.find(e=>String(e.value)===c.payload.label));var{coordinate:d}=c.payload;if(null==u||!1===c.payload.active||null==d||null==o)return void r(fO({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:p,y:h}=d,y=Math.min(p,o.x+o.width),v=Math.min(h,o.y+o.height),m={x:"horizontal"===i?u.coordinate:y,y:"horizontal"===i?v:u.coordinate};r(fO({active:c.payload.active,coordinate:m,dataKey:c.payload.dataKey,index:String(u.index),label:c.payload.label}))}}};return dK.on(dB,l),()=>{dK.off(dB,l)}},[l,r,t,e,n,a,i,o]),c=eB(sy),s=eB(sm),u=eL(),(0,d.useEffect)(()=>{if(null==c)return dJ;var e=(e,t,r)=>{s!==r&&c===e&&u(dY(t))};return dK.on(d$,e),()=>{dK.off(d$,e)}},[u,s,c])}();var E=function(){var e=eL(),[t,r]=(0,d.useState)(null),n=eB(nn);return(0,d.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;ab(r)&&r!==n&&e(r_(r))}},[t,e,n]),r}(),S=(0,d.useCallback)(e=>{E(e),"function"==typeof t&&t(e),O(e),j(e)},[E,t,O,j]),k=(0,d.useCallback)(e=>{x(wj(e)),x(wK({handler:i,reactEvent:e}))},[x,i]),A=(0,d.useCallback)(e=>{x(wS(e)),x(wK({handler:s,reactEvent:e}))},[x,s]),M=(0,d.useCallback)(e=>{x(fg()),x(wK({handler:u,reactEvent:e}))},[x,u]),T=(0,d.useCallback)(e=>{x(wS(e)),x(wK({handler:f,reactEvent:e}))},[x,f]),C=(0,d.useCallback)(()=>{x(wR())},[x]),D=(0,d.useCallback)(e=>{x(wL(e.key))},[x]),N=(0,d.useCallback)(e=>{x(wK({handler:o,reactEvent:e}))},[x,o]),I=(0,d.useCallback)(e=>{x(wK({handler:l,reactEvent:e}))},[x,l]),_=(0,d.useCallback)(e=>{x(wK({handler:c,reactEvent:e}))},[x,c]),L=(0,d.useCallback)(e=>{x(wK({handler:h,reactEvent:e}))},[x,h]),R=(0,d.useCallback)(e=>{x(wK({handler:m,reactEvent:e}))},[x,m]),z=(0,d.useCallback)(e=>{x(wU(e)),x(wK({handler:v,reactEvent:e}))},[x,v]),K=(0,d.useCallback)(e=>{x(wK({handler:y,reactEvent:e}))},[x,y]);return d.createElement(dz.Provider,{value:w},d.createElement(G.Provider,{value:P},d.createElement("div",{className:(0,p.$)("recharts-wrapper",n),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?w2(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):w2(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:b,height:a},g),onClick:k,onContextMenu:N,onDoubleClick:I,onFocus:C,onKeyDown:D,onMouseDown:_,onMouseEnter:A,onMouseLeave:M,onMouseMove:T,onMouseUp:L,onTouchEnd:K,onTouchMove:z,onTouchStart:R,ref:S},r)))}),w3=["children","className","width","height","style","compact","title","desc"],w4=(0,d.forwardRef)((e,t)=>{var{children:r,className:n,width:a,height:i,style:o,compact:l,title:c,desc:s}=e,u=F(function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,w3),!1);return l?d.createElement(w1,{otherAttributes:u,title:c,desc:s},r):d.createElement(w5,{className:n,style:o,width:a,height:i,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},d.createElement(w1,{otherAttributes:u,title:c,desc:s,ref:t},d.createElement(gU,null,r)))}),w6=["width","height"];function w8(){return(w8=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var w7={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},w9=(0,d.forwardRef)(function(e,t){var r,n=aI(e.categoricalChartProps,w7),{width:a,height:i}=n,o=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(n,w6);if(!ax(a)||!ax(i))return null;var{chartName:l,defaultTooltipEventType:c,validateTooltipEventTypes:s,tooltipPayloadSearcher:u,categoricalChartProps:f}=e;return d.createElement(wH,{preloadedState:{options:{chartName:l,defaultTooltipEventType:c,validateTooltipEventTypes:s,tooltipPayloadSearcher:u,eventEmitter:void 0}},reduxStoreName:null!=(r=f.id)?r:l},d.createElement(m9,{chartData:f.data}),d.createElement(wq,{width:a,height:i,layout:n.layout,margin:n.margin}),d.createElement(wY,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),d.createElement(w4,w8({},o,{width:a,height:i,ref:t})))}),Oe=["axis"],Ot=(0,d.forwardRef)((e,t)=>d.createElement(w9,{chartName:"LineChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Oe,tooltipPayloadSearcher:dF,categoricalChartProps:e,ref:t})),Or=["axis","item"],On=(0,d.forwardRef)((e,t)=>d.createElement(w9,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Or,tooltipPayloadSearcher:dF,categoricalChartProps:e,ref:t}));function Oa(e){var t=eL();return(0,d.useEffect)(()=>{t(wI(e))},[t,e]),null}var Oi=["width","height","layout"];function Oo(){return(Oo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var Ol={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},Oc=(0,d.forwardRef)(function(e,t){var r,n=aI(e.categoricalChartProps,Ol),{width:a,height:i,layout:o}=n,l=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(n,Oi);if(!ax(a)||!ax(i))return null;var{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:u,tooltipPayloadSearcher:f}=e;return d.createElement(wH,{preloadedState:{options:{chartName:c,defaultTooltipEventType:s,validateTooltipEventTypes:u,tooltipPayloadSearcher:f,eventEmitter:void 0}},reduxStoreName:null!=(r=n.id)?r:c},d.createElement(m9,{chartData:n.data}),d.createElement(wq,{width:a,height:i,layout:o,margin:n.margin}),d.createElement(wY,{accessibilityLayer:n.accessibilityLayer,barCategoryGap:n.barCategoryGap,maxBarSize:n.maxBarSize,stackOffset:n.stackOffset,barGap:n.barGap,barSize:n.barSize,syncId:n.syncId,syncMethod:n.syncMethod,className:n.className}),d.createElement(Oa,{cx:n.cx,cy:n.cy,startAngle:n.startAngle,endAngle:n.endAngle,innerRadius:n.innerRadius,outerRadius:n.outerRadius}),d.createElement(w4,Oo({width:a,height:i},l,{ref:t})))}),Os=["item"],Ou={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},Of=(0,d.forwardRef)((e,t)=>{var r=aI(e,Ou);return d.createElement(Oc,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:Os,tooltipPayloadSearcher:dF,categoricalChartProps:r,ref:t})}),Od=r(20697),Op=r.n(Od),Oh=["width","height","className","style","children","type"];function Oy(){return(Oy=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Ov(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Om(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ov(Object(r),!0).forEach(function(t){Og(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ov(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Og(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Ob="value",Ox=(e,t)=>y()(e,t),Ow=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"".concat(t,"children[").concat(e,"]")},OO={chartName:"Treemap",defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],tooltipPayloadSearcher:Ox,eventEmitter:void 0},OP=e=>{var t,{depth:r,node:n,index:a,dataKey:i,nameKey:o,nestedActiveTooltipIndex:l}=e,c=0===r?"":Ow(a,l),{children:s}=n,u=r+1,f=s&&s.length?s.map((e,t)=>OP({depth:u,node:e,index:t,dataKey:i,nameKey:o,nestedActiveTooltipIndex:c})):null;return t=s&&s.length?f.reduce((e,t)=>e+t[Ob],0):g(n[i])||n[i]<=0?0:n[i],Om(Om({},n),{},{children:f,name:rG(n,o,""),[Ob]:t,depth:r,index:a,tooltipIndex:c})},Oj=(e,t,r)=>{var n=t*t,a=e.area*e.area,{min:i,max:o}=e.reduce((e,t)=>({min:Math.min(e.min,t.area),max:Math.max(e.max,t.area)}),{min:1/0,max:0});return a?Math.max(n*o*r/a,a/(n*i*r)):1/0},OE=(e,t,r,n)=>t===r.width?((e,t,r,n)=>{var a,i=t?Math.round(e.area/t):0;(n||i>r.height)&&(i=r.height);for(var o=r.x,l=0,c=e.length;l<c;l++)(a=e[l]).x=o,a.y=r.y,a.height=i,a.width=Math.min(i?Math.round(a.area/i):0,r.x+r.width-o),o+=a.width;return a.width+=r.x+r.width-o,Om(Om({},r),{},{y:r.y+i,height:r.height-i})})(e,t,r,n):((e,t,r,n)=>{var a,i=t?Math.round(e.area/t):0;(n||i>r.width)&&(i=r.width);for(var o=r.y,l=0,c=e.length;l<c;l++)(a=e[l]).x=r.x,a.y=o,a.width=i,a.height=Math.min(i?Math.round(a.area/i):0,r.y+r.height-o),o+=a.height;return a&&(a.height+=r.y+r.height-o),Om(Om({},r),{},{x:r.x+i,width:r.width-i})})(e,t,r,n),OS=(e,t)=>{var{children:r}=e;if(r&&r.length){var n,a,i=(e=>({x:e.x,y:e.y,width:e.width,height:e.height}))(e),o=[],l=1/0,c=Math.min(i.width,i.height),s=((e,t)=>{var r=t<0?0:t;return e.map(e=>{var t=e[Ob]*r;return Om(Om({},e),{},{area:g(t)||t<=0?0:t})})})(r,i.width*i.height/e[Ob]),u=s.slice();for(o.area=0;u.length>0;)o.push(n=u[0]),o.area+=n.area,(a=Oj(o,c,t))<=l?(u.shift(),l=a):(o.area-=o.pop().area,c=Math.min((i=OE(o,c,i,!1)).width,i.height),o.length=o.area=0,l=1/0);return o.length&&(i=OE(o,c,i,!0),o.length=o.area=0),Om(Om({},e),{},{children:s.map(e=>OS(e,t))})}return e},Ok={isAnimationFinished:!1,formatRoot:null,currentRoot:null,nestIndex:[]};function OA(e){var{content:t,nodeProps:r,type:n,colorPanel:a,onMouseEnter:i,onMouseLeave:o,onClick:l}=e;if(d.isValidElement(t))return d.createElement(q,{onMouseEnter:i,onMouseLeave:o,onClick:l},d.cloneElement(t,r));if("function"==typeof t)return d.createElement(q,{onMouseEnter:i,onMouseLeave:o,onClick:l},t(r));var{x:c,y:s,width:u,height:f,index:p}=r,h=null;u>10&&f>10&&r.children&&"nest"===n&&(h=d.createElement(pQ,{points:[{x:c+2,y:s+f/2},{x:c+6,y:s+f/2+3},{x:c+2,y:s+f/2+6}]}));var y=null,v=pl(r.name);u>20&&f>20&&v.width<u&&v.height<f&&(y=d.createElement("text",{x:c+8,y:s+f/2+7,fontSize:14},r.name));var m=a||nl;return d.createElement("g",null,d.createElement(a7,Oy({fill:r.depth<2?m[p%m.length]:"rgba(255,255,255,0)",stroke:"#fff"},Op()(r,["children"]),{onMouseEnter:i,onMouseLeave:o,onClick:l,"data-recharts-item-index":r.tooltipIndex})),h,y)}function OM(e){var t=eL(),r=e.nodeProps?{x:e.nodeProps.x+e.nodeProps.width/2,y:e.nodeProps.y+e.nodeProps.height/2}:null;return d.createElement(OA,Oy({},e,{onMouseEnter:()=>{t(fv({activeIndex:e.nodeProps.tooltipIndex,activeDataKey:e.dataKey,activeCoordinate:r}))},onMouseLeave:()=>{},onClick:()=>{t(fb({activeIndex:e.nodeProps.tooltipIndex,activeDataKey:e.dataKey,activeCoordinate:r}))}}))}function OT(e){var{props:t,currentRoot:r}=e,{dataKey:n,nameKey:a,stroke:i,fill:o}=t;return{dataDefinedOnItem:r,positions:void 0,settings:{stroke:i,strokeWidth:void 0,fill:o,dataKey:n,nameKey:a,name:void 0,hide:!1,type:void 0,color:o,unit:""}}}var OC={top:0,right:0,bottom:0,left:0};class OD extends d.PureComponent{static getDerivedStateFromProps(e,t){if(e.data!==t.prevData||e.type!==t.prevType||e.width!==t.prevWidth||e.height!==t.prevHeight||e.dataKey!==t.prevDataKey||e.aspectRatio!==t.prevAspectRatio){var r=OP({depth:0,node:{children:e.data,x:0,y:0,width:e.width,height:e.height},index:0,dataKey:e.dataKey,nameKey:e.nameKey}),n=OS(r,e.aspectRatio);return Om(Om({},t),{},{formatRoot:n,currentRoot:r,nestIndex:[r],prevAspectRatio:e.aspectRatio,prevData:e.data,prevWidth:e.width,prevHeight:e.height,prevDataKey:e.dataKey,prevType:e.type})}return null}handleMouseEnter(e,t){t.persist();var{onMouseEnter:r}=this.props;r&&r(e,t)}handleMouseLeave(e,t){t.persist();var{onMouseLeave:r}=this.props;r&&r(e,t)}handleClick(e){var{onClick:t,type:r}=this.props;if("nest"===r&&e.children){var{width:n,height:a,dataKey:i,nameKey:o,aspectRatio:l}=this.props,c=OP({depth:0,node:Om(Om({},e),{},{x:0,y:0,width:n,height:a}),index:0,dataKey:i,nameKey:o,nestedActiveTooltipIndex:e.tooltipIndex}),s=OS(c,l),{nestIndex:u}=this.state;u.push(e),this.setState({formatRoot:s,currentRoot:c,nestIndex:u})}t&&t(e)}handleNestIndex(e,t){var{nestIndex:r}=this.state,{width:n,height:a,dataKey:i,nameKey:o,aspectRatio:l}=this.props,c=OS(OP({depth:0,node:Om(Om({},e),{},{x:0,y:0,width:n,height:a}),index:0,dataKey:i,nameKey:o,nestedActiveTooltipIndex:e.tooltipIndex}),l);r=r.slice(0,t+1),this.setState({formatRoot:c,currentRoot:e,nestIndex:r})}renderItem(e,t,r){var{isAnimationActive:n,animationBegin:a,animationDuration:i,animationEasing:o,isUpdateAnimationActive:l,type:c,animationId:s,colorPanel:u,dataKey:f}=this.props,{isAnimationFinished:p}=this.state,{width:h,height:y,x:v,y:m,depth:g}=t,b=parseInt("".concat((2*Math.random()-1)*h),10),x={};return((r||"nest"===c)&&(x={onMouseEnter:this.handleMouseEnter.bind(this,t),onMouseLeave:this.handleMouseLeave.bind(this,t),onClick:this.handleClick.bind(this,t)}),n)?d.createElement(a3,{begin:a,duration:i,isActive:n,easing:o,key:"treemap-".concat(s),from:{x:v,y:m,width:h,height:y},to:{x:v,y:m,width:h,height:y},onAnimationStart:this.handleAnimationStart,onAnimationEnd:this.handleAnimationEnd},r=>{var{x:s,y:h,width:y,height:v}=r;return d.createElement(a3,{from:"translate(".concat(b,"px, ").concat(b,"px)"),to:"translate(0, 0)",attributeName:"transform",begin:a,easing:o,isActive:n,duration:i},d.createElement(q,x,g>2&&!p?null:d.createElement(OM,{content:e,dataKey:f,nodeProps:Om(Om({},t),{},{isAnimationActive:n,isUpdateAnimationActive:!l,width:y,height:v,x:s,y:h}),type:c,colorPanel:u})))}):d.createElement(q,x,d.createElement(OM,{content:e,dataKey:f,nodeProps:Om(Om({},t),{},{isAnimationActive:!1,isUpdateAnimationActive:!1,width:h,height:y,x:v,y:m}),type:c,colorPanel:u}))}renderNode(e,t){var{content:r,type:n}=this.props,a=Om(Om(Om({},F(this.props,!1)),t),{},{root:e}),i=!t.children||!t.children.length,{currentRoot:o}=this.state;return!(o.children||[]).filter(e=>e.depth===t.depth&&e.name===t.name).length&&e.depth&&"nest"===n?null:d.createElement(q,{key:"recharts-treemap-node-".concat(a.x,"-").concat(a.y,"-").concat(a.name),className:"recharts-treemap-depth-".concat(t.depth)},this.renderItem(r,a,i),t.children&&t.children.length?t.children.map(e=>this.renderNode(t,e)):null)}renderAllNodes(){var{formatRoot:e}=this.state;return e?this.renderNode(e,e):null}renderNestIndex(){var{nameKey:e,nestIndexContent:t}=this.props,{nestIndex:r}=this.state;return d.createElement("div",{className:"recharts-treemap-nest-index-wrapper",style:{marginTop:"8px",textAlign:"center"}},r.map((r,n)=>{var a=y()(r,e,"root"),i=null;return d.isValidElement(t)&&(i=d.cloneElement(t,r,n)),i="function"==typeof t?t(r,n):a,d.createElement("div",{onClick:this.handleNestIndex.bind(this,r,n),key:"nest-index-".concat(P()),className:"recharts-treemap-nest-index-box",style:{cursor:"pointer",display:"inline-block",padding:"0 7px",background:"#000",color:"#fff",marginRight:"3px"}},i)}))}render(){var e=this.props,{width:t,height:r,className:n,style:a,children:i,type:o}=e,l=F(function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,Oh),!1);return d.createElement(dz.Provider,{value:this.state.tooltipPortal},d.createElement(yM,{fn:OT,args:{props:this.props,currentRoot:this.state.currentRoot}}),d.createElement(w5,{className:n,style:a,width:t,height:r,ref:e=>{null==this.state.tooltipPortal&&this.setState({tooltipPortal:e})},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:this.handleTouchMove,onTouchEnd:void 0},d.createElement(V,Oy({},l,{width:t,height:"nest"===o?r-30:r}),this.renderAllNodes(),i),"nest"===o&&this.renderNestIndex()))}constructor(){super(...arguments),Og(this,"state",Om({},Ok)),Og(this,"handleAnimationEnd",()=>{var{onAnimationEnd:e}=this.props;this.setState({isAnimationFinished:!0}),"function"==typeof e&&e()}),Og(this,"handleAnimationStart",()=>{var{onAnimationStart:e}=this.props;this.setState({isAnimationFinished:!1}),"function"==typeof e&&e()}),Og(this,"handleTouchMove",(e,t)=>{var r=t.touches[0],n=document.elementFromPoint(r.clientX,r.clientY);if(n&&n.getAttribute){var a=n.getAttribute("data-recharts-item-index"),i=Ox(this.state.formatRoot,a);if(i){var{dataKey:o,dispatch:l}=this.props;l(fv({activeIndex:a,activeDataKey:o,activeCoordinate:{x:i.x+i.width/2,y:i.y+i.height/2}}))}}})}}function ON(e){var t=eL();return d.createElement(OD,Oy({},e,{dispatch:t}))}function OI(e){var t,{width:r,height:n}=e;return ax(r)&&ax(n)?d.createElement(wH,{preloadedState:{options:OO},reduxStoreName:null!=(t=e.className)?t:"Treemap"},d.createElement(nA,{width:r,height:n}),d.createElement(nM,{margin:OC}),d.createElement(ON,e)):null}Og(OD,"displayName","Treemap"),Og(OD,"defaultProps",{aspectRatio:.5*(1+Math.sqrt(5)),dataKey:"value",nameKey:"name",type:"flat",isAnimationActive:!n4.isSsr,isUpdateAnimationActive:!n4.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"linear"});var O_=r(81682),OL=r.n(O_),OR=["sourceX","sourceY","sourceControlX","targetX","targetY","targetControlX","linkWidth"],Oz=["width","height","className","style","children"];function OK(){return(OK=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function OB(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function O$(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function OF(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?O$(Object(r),!0).forEach(function(t){OU(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):O$(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function OU(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var OW=e=>e.y+e.dy/2,OV=e=>e&&e.value||0,OX=(e,t)=>t.reduce((t,r)=>t+OV(e[r]),0),OH=(e,t,r)=>r.reduce((r,n)=>r+OW(e[t[n].source])*OV(t[n]),0),Oq=(e,t,r)=>r.reduce((r,n)=>r+OW(e[t[n].target])*OV(t[n]),0),OY=(e,t)=>e.y-t.y,OG=(e,t)=>{for(var{targetNodes:r}=t,n=0,a=r.length;n<a;n++){var i=e[r[n]];i&&(i.depth=Math.max(t.depth+1,i.depth),OG(e,i))}},OZ=function(e,t,r){for(var n=!(arguments.length>3)||void 0===arguments[3]||arguments[3],a=0,i=e.length;a<i;a++){var o=e[a],l=o.length;n&&o.sort(OY);for(var c=0,s=0;s<l;s++){var u=o[s],f=c-u.y;f>0&&(u.y+=f),c=u.y+u.dy+r}c=t+r;for(var d=l-1;d>=0;d--){var p=o[d],h=p.y+p.dy+r-c;if(h>0)p.y-=h,c=p.y;else break}}},OJ=(e,t,r,n)=>{for(var a=0,i=t.length;a<i;a++)for(var o=t[a],l=0,c=o.length;l<c;l++){var s=o[l];if(s.sourceLinks.length){var u=OX(r,s.sourceLinks),f=OH(e,r,s.sourceLinks)/u;s.y+=(f-OW(s))*n}}},OQ=(e,t,r,n)=>{for(var a=t.length-1;a>=0;a--)for(var i=t[a],o=0,l=i.length;o<l;o++){var c=i[o];if(c.targetLinks.length){var s=OX(r,c.targetLinks),u=Oq(e,r,c.targetLinks)/s;c.y+=(u-OW(c))*n}}},O0=(e,t)=>"node"===t?{x:+e.x+e.width/2,y:+e.y+e.height/2}:"sourceX"in e&&{x:(e.sourceX+e.targetX)/2,y:(e.sourceY+e.targetY)/2},O1={chartName:"Sankey",defaultTooltipEventType:"item",validateTooltipEventTypes:["item"],tooltipPayloadSearcher:(e,t,r,n)=>{if(null!=t&&"string"==typeof t){var[a,i]=t.split("-"),o=y()(r,"".concat(a,"s[").concat(i,"]"));if(o)return((e,t,r)=>{var{payload:n}=e;if("node"===t)return{payload:n,name:rG(n,r,""),value:rG(n,"value")};if("source"in n&&n.source&&n.target){var a=rG(n.source,r,""),i=rG(n.target,r,"");return{payload:n,name:"".concat(a," - ").concat(i),value:rG(n,"value")}}return null})(o,a,n)}},eventEmitter:void 0};function O2(e){var{dataKey:t,nameKey:r,stroke:n,strokeWidth:a,fill:i,name:o,data:l}=e;return{dataDefinedOnItem:l,positions:void 0,settings:{stroke:n,strokeWidth:a,fill:i,dataKey:t,name:o,nameKey:r,color:i,unit:""}}}var O5={top:0,right:0,bottom:0,left:0};function O3(e){var{props:t,i:r,linkContent:n,onMouseEnter:a,onMouseLeave:i,onClick:o,dataKey:l}=e,c=O0(t,"link"),s="link-".concat(r),u=eL();return d.createElement(q,{onMouseEnter:e=>{u(fv({activeIndex:s,activeDataKey:l,activeCoordinate:c})),a(t,e)},onMouseLeave:e=>{u(fm()),i(t,e)},onClick:e=>{u(fb({activeIndex:s,activeDataKey:l,activeCoordinate:c})),o(t,e)}},function(e,t){if(d.isValidElement(e))return d.cloneElement(e,t);if("function"==typeof e)return e(t);var{sourceX:r,sourceY:n,sourceControlX:a,targetX:i,targetY:o,targetControlX:l,linkWidth:c}=t,s=OB(t,OR);return d.createElement("path",OK({className:"recharts-sankey-link",d:"\n          M".concat(r,",").concat(n,"\n          C").concat(a,",").concat(n," ").concat(l,",").concat(o," ").concat(i,",").concat(o,"\n        "),fill:"none",stroke:"#333",strokeWidth:c,strokeOpacity:"0.2"},F(s,!1)))}(n,t))}function O4(e){var{modifiedLinks:t,links:r,linkContent:n,onMouseEnter:a,onMouseLeave:i,onClick:o,dataKey:l}=e;return d.createElement(q,{className:"recharts-sankey-links",key:"recharts-sankey-links"},r.map((e,r)=>{var c=t[r];return d.createElement(O3,{key:"link-".concat(e.source,"-").concat(e.target,"-").concat(e.value),props:c,linkContent:n,i:r,onMouseEnter:a,onMouseLeave:i,onClick:o,dataKey:l})}))}function O6(e){var{props:t,nodeContent:r,i:n,onMouseEnter:a,onMouseLeave:i,onClick:o,dataKey:l}=e,c=eL(),s=O0(t,"node"),u="node-".concat(n);return d.createElement(q,{onMouseEnter:e=>{c(fv({activeIndex:u,activeDataKey:l,activeCoordinate:s})),a(t,e)},onMouseLeave:e=>{c(fm()),i(t,e)},onClick:e=>{c(fb({activeIndex:u,activeDataKey:l,activeCoordinate:s})),o(t,e)}},d.isValidElement(r)?d.cloneElement(r,t):"function"==typeof r?r(t):d.createElement(a7,OK({className:"recharts-sankey-node",fill:"#0088fe",fillOpacity:"0.8"},F(t,!1))))}function O8(e){var{modifiedNodes:t,nodeContent:r,onMouseEnter:n,onMouseLeave:a,onClick:i,dataKey:o}=e;return d.createElement(q,{className:"recharts-sankey-nodes",key:"recharts-sankey-nodes"},t.map((e,t)=>d.createElement(O6,{props:e,nodeContent:r,i:t,onMouseEnter:n,onMouseLeave:a,onClick:i,dataKey:o})))}class O7 extends d.PureComponent{static getDerivedStateFromProps(e,t){var{data:r,width:n,height:a,margin:i,iterations:o,nodeWidth:l,nodePadding:c,sort:s,linkCurvature:u}=e;if(r!==t.prevData||n!==t.prevWidth||a!==t.prevHeight||!bi(i,t.prevMargin)||o!==t.prevIterations||l!==t.prevNodeWidth||c!==t.prevNodePadding||s!==t.sort){var{links:f,nodes:d}=(e=>{var{data:t,width:r,height:n,iterations:a,nodeWidth:i,nodePadding:o,sort:l}=e,{links:c}=t,{tree:s}=((e,t,r)=>{for(var{nodes:n,links:a}=e,i=n.map((e,t)=>{var r=((e,t)=>{for(var r=[],n=[],a=[],i=[],o=0,l=e.length;o<l;o++){var c=e[o];c.source===t&&(a.push(c.target),i.push(o)),c.target===t&&(r.push(c.source),n.push(o))}return{sourceNodes:r,sourceLinks:n,targetLinks:i,targetNodes:a}})(a,t);return OF(OF(OF({},e),r),{},{value:Math.max(OX(a,r.sourceLinks),OX(a,r.targetLinks)),depth:0})}),o=0,l=i.length;o<l;o++){var c=i[o];c.sourceNodes.length||OG(i,c)}var s=hO()(i,e=>e.depth).depth;if(s>=1)for(var u=(t-r)/s,f=0,d=i.length;f<d;f++){var p=i[f];p.targetNodes.length||(p.depth=s),p.x=p.depth*u,p.dx=r}return{tree:i,maxDepth:s}})(t,r,i),u=(e=>{for(var t=[],r=0,n=e.length;r<n;r++){var a=e[r];t[a.depth]||(t[a.depth]=[]),t[a.depth].push(a)}return t})(s),f=((e,t,r,n)=>{for(var a=Math.min(...e.map(e=>(t-(e.length-1)*r)/OL()(e,OV))),i=0,o=e.length;i<o;i++)for(var l=0,c=e[i].length;l<c;l++){var s=e[i][l];s.y=l,s.dy=s.value*a}return n.map(e=>OF(OF({},e),{},{dy:OV(e)*a}))})(u,n,o,c);OZ(u,n,o,l);for(var d=1,p=1;p<=a;p++)OQ(s,u,f,d*=.99),OZ(u,n,o,l),OJ(s,u,f,d),OZ(u,n,o,l);return((e,t)=>{for(var r=0,n=e.length;r<n;r++){var a=e[r],i=0,o=0;a.targetLinks.sort((r,n)=>e[t[r].target].y-e[t[n].target].y),a.sourceLinks.sort((r,n)=>e[t[r].source].y-e[t[n].source].y);for(var l=0,c=a.targetLinks.length;l<c;l++){var s=t[a.targetLinks[l]];s&&(s.sy=i,i+=s.dy)}for(var u=0,f=a.sourceLinks.length;u<f;u++){var d=t[a.sourceLinks[u]];d&&(d.ty=o,o+=d.dy)}}})(s,f),{nodes:s,links:f}})({data:r,width:n-(i&&i.left||0)-(i&&i.right||0),height:a-(i&&i.top||0)-(i&&i.bottom||0),iterations:o,nodeWidth:l,nodePadding:c,sort:s}),p=y()(i,"top")||0,h=y()(i,"left")||0,v=f.map((t,r)=>(e=>{var t,r,{link:n,nodes:a,left:i,top:o,i:l,linkContent:c,linkCurvature:s}=e,{sy:u,ty:f,dy:d}=n,p=a[n.source],h=a[n.target],y=p.x+p.dx+i,v=h.x+i,m=(r=v-(t=+y),e=>t+r*e),g=m(s),b=m(1-s);return OF({sourceX:y,targetX:v,sourceY:p.y+u+d/2+o,targetY:h.y+f+d/2+o,sourceControlX:g,targetControlX:b,sourceRelativeY:u,targetRelativeY:f,linkWidth:d,index:l,payload:OF(OF({},n),{},{source:p,target:h})},F(c,!1))})({link:t,nodes:d,i:r,top:p,left:h,linkContent:e.link,linkCurvature:u})),m=d.map((t,r)=>(e=>{var{node:t,nodeContent:r,top:n,left:a,i}=e,{x:o,y:l,dx:c,dy:s}=t;return OF(OF({},F(r,!1)),{},{x:o+a,y:l+n,width:c,height:s,index:i,payload:t})})({node:t,nodeContent:e.node,i:r,top:p,left:h}));return OF(OF({},t),{},{nodes:d,links:f,modifiedLinks:v,modifiedNodes:m,prevData:r,prevWidth:o,prevHeight:a,prevMargin:i,prevNodePadding:c,prevNodeWidth:l,prevIterations:o,prevSort:s})}return null}handleMouseEnter(e,t,r){var{onMouseEnter:n}=this.props;n&&n(e,t,r)}handleMouseLeave(e,t,r){var{onMouseLeave:n}=this.props;n&&n(e,t,r)}handleClick(e,t,r){var{onClick:n}=this.props;n&&n(e,t,r)}render(){var e=this.props,{width:t,height:r,className:n,style:a,children:i}=e,o=OB(e,Oz);if(!ax(t)||!ax(r))return null;var{links:l,modifiedNodes:c,modifiedLinks:s}=this.state,u=F(o,!1);return d.createElement(wH,{preloadedState:{options:O1},reduxStoreName:null!=n?n:"Sankey"},d.createElement(yM,{fn:O2,args:this.props}),d.createElement(ge,{computedData:{links:s,nodes:c}}),d.createElement(nA,{width:t,height:r}),d.createElement(nM,{margin:O5}),d.createElement(dz.Provider,{value:this.state.tooltipPortal},d.createElement(w5,{className:n,style:a,width:t,height:r,ref:e=>{null==this.state.tooltipPortal&&this.setState({tooltipPortal:e})},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:void 0,onTouchEnd:void 0},d.createElement(V,OK({},u,{width:t,height:r}),i,d.createElement(O4,{links:l,modifiedLinks:s,linkContent:this.props.link,dataKey:this.props.dataKey,onMouseEnter:(e,t)=>this.handleMouseEnter(e,"link",t),onMouseLeave:(e,t)=>this.handleMouseLeave(e,"link",t),onClick:(e,t)=>this.handleClick(e,"link",t)}),d.createElement(O8,{modifiedNodes:c,nodeContent:this.props.node,dataKey:this.props.dataKey,onMouseEnter:(e,t)=>this.handleMouseEnter(e,"node",t),onMouseLeave:(e,t)=>this.handleMouseLeave(e,"node",t),onClick:(e,t)=>this.handleClick(e,"node",t)})))))}constructor(){super(...arguments),OU(this,"state",{nodes:[],links:[],modifiedLinks:[],modifiedNodes:[]})}}OU(O7,"displayName","Sankey"),OU(O7,"defaultProps",{nameKey:"name",dataKey:"value",nodePadding:10,nodeWidth:10,linkCurvature:.5,iterations:32,margin:{top:5,right:5,bottom:5,left:5},sort:!0});var O9=["axis"],Pe={layout:"centric",startAngle:90,endAngle:-270,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},Pt=(0,d.forwardRef)((e,t)=>{var r=aI(e,Pe);return d.createElement(Oc,{chartName:"RadarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:O9,tooltipPayloadSearcher:dF,categoricalChartProps:r,ref:t})}),Pr=["item"],Pn=(0,d.forwardRef)((e,t)=>d.createElement(w9,{chartName:"ScatterChart",defaultTooltipEventType:"item",validateTooltipEventTypes:Pr,tooltipPayloadSearcher:dF,categoricalChartProps:e,ref:t})),Pa=["axis"],Pi=(0,d.forwardRef)((e,t)=>d.createElement(w9,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Pa,tooltipPayloadSearcher:dF,categoricalChartProps:e,ref:t})),Po=["axis","item"],Pl={layout:"radial",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},Pc=(0,d.forwardRef)((e,t)=>{var r=aI(e,Pl);return d.createElement(Oc,{chartName:"RadialBarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Po,tooltipPayloadSearcher:dF,categoricalChartProps:r,ref:t})}),Ps=["axis"],Pu=(0,d.forwardRef)((e,t)=>d.createElement(w9,{chartName:"ComposedChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:Ps,tooltipPayloadSearcher:dF,categoricalChartProps:e,ref:t}));function Pf(){return(Pf=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Pd(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Pp(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Pd(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Pd(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var Ph={fontWeight:"bold",paintOrder:"stroke fill",fontSize:".75rem",stroke:"#FFF",fill:"black",pointerEvents:"none"};function Py(e){var t,{dataKey:r,nameKey:n,data:a,stroke:i,fill:o,positions:l}=e;return{dataDefinedOnItem:a.children,positions:(t={},l.forEach((e,r)=>{t[r]=e}),t),settings:{stroke:i,strokeWidth:void 0,fill:o,nameKey:n,dataKey:r,name:n?void 0:r,hide:!1,type:void 0,color:o,unit:""}}}var Pv={top:0,right:0,bottom:0,left:0},Pm=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"".concat(t,"children[").concat(e,"]")},Pg={options:{validateTooltipEventTypes:["item"],defaultTooltipEventType:"item",chartName:"Sunburst",tooltipPayloadSearcher:(e,t)=>y()(e,t),eventEmitter:void 0}},Pb=e=>{var{className:t,data:r,children:n,width:a,height:i,padding:o=2,dataKey:l="value",nameKey:c="name",ringPadding:s=2,innerRadius:u=50,fill:f="#333",stroke:h="#FFF",textOptions:y=Ph,outerRadius:v=Math.min(a,i)/2,cx:m=a/2,cy:g=i/2,startAngle:b=0,endAngle:x=360,onClick:w,onMouseEnter:O,onMouseLeave:P}=e,j=eL(),E=oC([0,r[l]],[0,x]),S=function e(t){return t.children&&0!==t.children.length?1+Math.max(...t.children.map(t=>e(t))):1}(r),k=[],A=new Map([]),[M,T]=(0,d.useState)(null);!function e(t,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,{radius:a,innerR:i,initialAngle:c,childColor:u,nestedActiveTooltipIndex:p}=r,v=c;t&&t.forEach((t,r)=>{var c,b,x=1===n?"[".concat(r,"]"):Pm(r,p),S=Pp(Pp({},t),{},{tooltipIndex:x}),M=E(t[l]),T=v,C=null!=(c=null!=(b=null==t?void 0:t.fill)?b:u)?c:f,{x:D,y:N}=rV(0,0,i+a/2,-(T+M-M/2));v+=M,k.push(d.createElement("g",{key:"sunburst-sector-".concat(t.name,"-").concat(r)},d.createElement(ii,{onClick:()=>{w&&w(S),j(fb({activeIndex:S.tooltipIndex,activeDataKey:l,activeCoordinate:A.get(S.name)}))},onMouseEnter:e=>{O&&O(S,e),j(fv({activeIndex:S.tooltipIndex,activeDataKey:l,activeCoordinate:A.get(S.name)}))},onMouseLeave:e=>{P&&P(S,e),j(fm())},fill:C,stroke:h,strokeWidth:o,startAngle:T,endAngle:T+M,innerRadius:i,outerRadius:i+a,cx:m,cy:g}),d.createElement(pS,Pf({},y,{alignmentBaseline:"middle",textAnchor:"middle",x:D+m,y:g-N}),t[l])));var{x:I,y:_}=rV(m,g,i+a/2,T);return A.set(t.name,{x:I,y:_}),e(t.children,{radius:a,innerR:i+a+s,initialAngle:T,childColor:C,nestedActiveTooltipIndex:x},n+1)})}(r.children,{radius:(v-u)/S,innerR:u,initialAngle:b});var C=(0,p.$)("recharts-sunburst",t);return d.createElement(dz.Provider,{value:M},d.createElement(w5,{className:t,width:a,height:i,ref:e=>{null==M&&null!=e&&T(e)},onMouseEnter:void 0,onMouseLeave:void 0,onClick:void 0,onMouseMove:void 0,onMouseDown:void 0,onMouseUp:void 0,onContextMenu:void 0,onDoubleClick:void 0,onTouchStart:void 0,onTouchMove:void 0,onTouchEnd:void 0},d.createElement(V,{width:a,height:i},d.createElement(q,{className:C},k),d.createElement(yM,{fn:Py,args:{dataKey:l,data:r,stroke:h,fill:f,nameKey:c,positions:A}}),n)))},Px=e=>{var t;return d.createElement(wH,{preloadedState:Pg,reduxStoreName:null!=(t=e.className)?t:"SunburstChart"},d.createElement(nA,{width:e.width,height:e.height}),d.createElement(nM,{margin:Pv}),d.createElement(Pb,e))};function Pw(){return(Pw=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function PO(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function PP(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?PO(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):PO(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Pj(e,t){var r=parseInt("".concat(t.x||e.x),10),n=parseInt("".concat(t.y||e.y),10),a=parseInt("".concat((null==t?void 0:t.height)||(null==e?void 0:e.height)),10);return PP(PP(PP({},t),yj(e)),{},{height:a,x:r,y:n})}function PE(e){return d.createElement(yE,Pw({shapeType:"trapezoid",propTransformer:Pj},e))}function PS(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Pk(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?PS(Object(r),!0).forEach(function(t){var n,a,i;n=e,a=t,i=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[a]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):PS(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var PA=e3([nd,(e,t)=>t,cG],(e,t,r)=>{var n,{data:a,dataKey:i,nameKey:o,tooltipType:l,lastShapeType:c,reversed:s,customWidth:u,cells:f,presentationProps:d}=t,{chartData:p}=r;if(null!=a&&a.length>0?n=a:null!=p&&p.length>0&&(n=p),n&&n.length)n=n.map((e,t)=>Pk(Pk(Pk({payload:e},d),e),f&&f[t]&&f[t].props));else{if(!f||!f.length)return{trapezoids:[],data:n};n=f.map(e=>Pk(Pk({},d),e.props))}return function(e){var{dataKey:t,nameKey:r,displayedData:n,tooltipType:a,lastShapeType:i,reversed:o,offset:l,customWidth:c}=e,{left:s,top:u}=l,{realHeight:f,realWidth:d,offsetX:p,offsetY:h}=((e,t)=>{var{width:r,height:n,left:a,right:i,top:o,bottom:l}=t,c=r;return x(e)?c=e:"string"==typeof e&&(c=c*parseFloat(e)/100),{realWidth:c-a-i-50,realHeight:n-l-o,offsetX:(r-c)/2,offsetY:(n-n)/2}})(c,l),y=Math.max.apply(null,n.map(e=>rG(e,t,0))),v=n.length,m=f/v,g={x:l.left,y:l.top,width:l.width,height:l.height},b=n.map((e,o)=>{var l,c=rG(e,t,0),f=rG(e,r,o),b=c;o!==v-1?(l=rG(n[o+1],t,0))instanceof Array&&([l]=l):c instanceof Array&&2===c.length?[b,l]=c:l="rectangle"===i?b:0;var x=(y-b)*d/(2*y)+u+25+p,w=m*o+s+h,O=b/y*d,P=l/y*d,j=[{name:f,value:b,payload:e,dataKey:t,type:a}];return PN(PN({x,y:w,width:Math.max(O,P),upperWidth:O,lowerWidth:P,height:m,name:f,val:b,tooltipPayload:j,tooltipPosition:{x:x+O/2,y:w+m/2}},Op()(e,["width"])),{},{payload:e,parentViewBox:g,labelViewBox:{x:x+(O-P)/4,y:w,width:Math.abs(O-P)/2+Math.min(O,P),height:m}})});return o&&(b=b.map((e,t)=>{var r=e.y-t*m+(v-1-t)*m;return PN(PN({},e),{},{upperWidth:e.lowerWidth,lowerWidth:e.upperWidth,x:e.x-(e.lowerWidth-e.upperWidth)/2,y:e.y-t*m+(v-1-t)*m,tooltipPosition:PN(PN({},e.tooltipPosition),{},{y:r+m/2}),labelViewBox:PN(PN({},e.labelViewBox),{},{y:r})})})),{trapezoids:b,data:n}}({dataKey:i,nameKey:o,displayedData:n,tooltipType:l,lastShapeType:c,reversed:s,offset:e,customWidth:u})}),PM=["onMouseEnter","onClick","onMouseLeave","shape","activeShape"],PT=["stroke","fill","legendType","hide","isAnimationActive","animationBegin","animationDuration","animationEasing","nameKey","lastShapeType"];function PC(){return(PC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function PD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function PN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?PD(Object(r),!0).forEach(function(t){PI(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):PD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function PI(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function P_(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(n=0;n<i.length;n++)r=i[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function PL(e){var{dataKey:t,nameKey:r,stroke:n,strokeWidth:a,fill:i,name:o,hide:l,tooltipType:c,data:s}=e;return{dataDefinedOnItem:s,positions:e.trapezoids.map(e=>{var{tooltipPosition:t}=e;return t}),settings:{stroke:n,strokeWidth:a,fill:i,dataKey:t,name:o,nameKey:r,hide:l,type:c,color:i,unit:""}}}function PR(e){var{trapezoids:t,allOtherFunnelProps:r,showLabels:n}=e,a=eB(e=>dS(e,"item",e.tooltip.settings.trigger,void 0)),{onMouseEnter:i,onClick:o,onMouseLeave:l,shape:c,activeShape:s}=r,u=P_(r,PM),f=yS(i,r.dataKey),p=yk(l),h=yA(o,r.dataKey);return d.createElement(d.Fragment,null,t.map((e,t)=>{var r=s&&a===String(t),n=PN(PN({},e),{},{option:r?s:c,isActive:r,stroke:e.stroke});return d.createElement(q,PC({className:"recharts-funnel-trapezoid"},_(u,e,t),{onMouseEnter:f(e,t),onMouseLeave:p(e,t),onClick:h(e,t),key:"trapezoid-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.name,"-").concat(null==e?void 0:e.value)}),d.createElement(PE,n))}),n&&pV.renderCallByParent(r,t))}var Pz=0;function PK(e){var t,r,{previousTrapezoidsRef:n,props:a}=e,{trapezoids:i,isAnimationActive:o,animationBegin:l,animationDuration:c,animationEasing:s,onAnimationEnd:u,onAnimationStart:f}=a,p=n.current,[h,y]=(0,d.useState)(!0),v=(t=(0,d.useRef)(Pz),(r=(0,d.useRef)(i)).current!==i&&(t.current+=1,Pz=t.current,r.current=i),t.current),m=(0,d.useCallback)(()=>{"function"==typeof u&&u(),y(!1)},[u]),g=(0,d.useCallback)(()=>{"function"==typeof f&&f(),y(!0)},[f]);return d.createElement(a3,{begin:l,duration:c,isActive:o,easing:s,from:{t:0},to:{t:1},key:v,onAnimationStart:g,onAnimationEnd:m},e=>{var{t}=e,r=1===t?i:i.map((e,r)=>{var n=p&&p[r];if(n){var a=S(n.x,e.x),i=S(n.y,e.y),o=S(n.upperWidth,e.upperWidth),l=S(n.lowerWidth,e.lowerWidth),c=S(n.height,e.height);return PN(PN({},e),{},{x:a(t),y:i(t),upperWidth:o(t),lowerWidth:l(t),height:c(t)})}var s=S(e.x+e.upperWidth/2,e.x),u=S(e.y+e.height/2,e.y),f=S(0,e.upperWidth),d=S(0,e.lowerWidth),h=S(0,e.height);return PN(PN({},e),{},{x:s(t),y:u(t),upperWidth:f(t),lowerWidth:d(t),height:h(t)})});return t>0&&(n.current=r),d.createElement(q,null,d.createElement(PR,{trapezoids:r,allOtherFunnelProps:a,showLabels:!h}))})}function PB(e){var{trapezoids:t,isAnimationActive:r}=e,n=(0,d.useRef)(null),a=n.current;return r&&t&&t.length&&(!a||a!==t)?d.createElement(PK,{props:e,previousTrapezoidsRef:n}):d.createElement(PR,{trapezoids:t,allOtherFunnelProps:e,showLabels:!0})}class P$ extends d.PureComponent{render(){var{className:e}=this.props,t=(0,p.$)("recharts-trapezoids",e);return d.createElement(q,{className:t},d.createElement(PB,this.props))}}var PF={stroke:"#fff",fill:"#808080",legendType:"rect",hide:!1,isAnimationActive:!n4.isSsr,animationBegin:400,animationDuration:1500,animationEasing:"ease",nameKey:"name",lastShapeType:"triangle"};function PU(e){var{height:t,width:r}=yQ(),n=aI(e,PF),{stroke:a,fill:i,legendType:o,hide:l,isAnimationActive:c,animationBegin:s,animationDuration:u,animationEasing:f,nameKey:p,lastShapeType:h}=n,y=P_(n,PT),v=F(e,!1),m=B(e.children,pt),g=(0,d.useMemo)(()=>({dataKey:e.dataKey,nameKey:p,data:e.data,tooltipType:e.tooltipType,lastShapeType:h,reversed:e.reversed,customWidth:e.width,cells:m,presentationProps:v}),[e.dataKey,p,e.data,e.tooltipType,h,e.reversed,e.width,m,v]),{trapezoids:b}=eB(e=>PA(e,g));return d.createElement(d.Fragment,null,d.createElement(yM,{fn:PL,args:PN(PN({},e),{},{trapezoids:b})}),l?null:d.createElement(P$,PC({},y,{stroke:a,fill:i,nameKey:p,lastShapeType:h,animationBegin:s,animationDuration:u,animationEasing:f,isAnimationActive:c,hide:l,legendType:o,height:t,width:r,trapezoids:b})))}class PW extends d.PureComponent{render(){return d.createElement(PU,this.props)}}PI(PW,"displayName","Funnel"),PI(PW,"defaultProps",PF);var PV=["item"],PX=(0,d.forwardRef)((e,t)=>d.createElement(w9,{chartName:"FunnelChart",defaultTooltipEventType:"item",validateTooltipEventTypes:PV,tooltipPayloadSearcher:dF,categoricalChartProps:e,ref:t}))},98132:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(72744);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},98221:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},98412:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},99279:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}}}]);