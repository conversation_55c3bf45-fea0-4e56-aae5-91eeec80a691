"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[193,2574,4955,5431,7153,7336,7812],{8619:(e,t,n)=>{n.d(t,{d:()=>i});var r=n(60098),o=n(12115),s=n(51508),u=n(82885);function i(e){let t=(0,u.M)(()=>(0,r.OQ)(e)),{isStatic:n}=(0,o.useContext)(s.Q);if(n){let[,n]=(0,o.useState)(e);(0,o.useEffect)(()=>t.on("change",n),[])}return t}},35695:(e,t,n)=>{var r=n(18999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},37602:(e,t,n)=>{n.d(t,{z:()=>f});var r=n(64803),o=n(30532),s=n(69515);function u(e){return"number"==typeof e?e:parseFloat(e)}var i=n(12115),l=n(51508),c=n(8619),a=n(58829);function f(e,t={}){let{isStatic:n}=(0,i.useContext)(l.Q),h=()=>(0,r.S)(e)?e.get():e;if(n)return(0,a.G)(h);let p=(0,c.d)(h());return(0,i.useInsertionEffect)(()=>(function(e,t,n){let i,l,c=e.get(),a=null,f=c,h="string"==typeof c?c.replace(/[\d.-]/g,""):void 0,p=()=>{a&&(a.stop(),a=null)},d=()=>{p(),a=new o.s({keyframes:[u(e.get()),u(f)],velocity:e.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...n,onUpdate:i})};return e.attach((t,n)=>(f=t,i=e=>{var t,r;return n((t=e,(r=h)?t+r:t))},s.Gt.postRender(d),e.get()),p),(0,r.S)(t)&&(l=t.on("change",t=>{var n,r;return e.set((n=t,(r=h)?n+r:n))}),e.on("destroy",l)),l})(p,e,t),[p,JSON.stringify(t)]),p}},58829:(e,t,n)=>{n.d(t,{G:()=>a});var r=n(6775),o=n(82885),s=n(69515),u=n(97494),i=n(8619);function l(e,t){let n=(0,i.d)(t()),r=()=>n.set(t());return r(),(0,u.E)(()=>{let t=()=>s.Gt.preRender(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,s.WG)(r)}}),n}var c=n(60098);function a(e,t,n,o){if("function"==typeof e){c.bt.current=[],e();let t=l(c.bt.current,e);return c.bt.current=void 0,t}let s="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),n=t?0:-1,o=e[0+n],s=e[1+n],u=e[2+n],i=e[3+n],l=(0,r.G)(s,u,i);return t?l(o):l}(t,n,o);return Array.isArray(e)?f(e,s):f([e],([e])=>s(e))}function f(e,t){let n=(0,o.M)(()=>[]);return l(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}},60760:(e,t,n)=>{n.d(t,{N:()=>y});var r=n(95155),o=n(12115),s=n(90869),u=n(82885),i=n(97494),l=n(80845),c=n(27351),a=n(51508);class f extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function h(e){let{children:t,isPresent:n,anchorX:s,root:u}=e,i=(0,o.useId)(),l=(0,o.useRef)(null),c=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,o.useContext)(a.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:o,right:a}=c.current;if(n||!l.current||!e||!t)return;l.current.dataset.motionPopId=i;let f=document.createElement("style");h&&(f.nonce=h);let p=null!=u?u:document.head;return p.appendChild(f),f.sheet&&f.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===s?"left: ".concat(o):"right: ".concat(a),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{p.removeChild(f),p.contains(f)&&p.removeChild(f)}},[n]),(0,r.jsx)(f,{isPresent:n,childRef:l,sizeRef:c,children:o.cloneElement(t,{ref:l})})}let p=e=>{let{children:t,initial:n,isPresent:s,onExitComplete:i,custom:c,presenceAffectsLayout:a,mode:f,anchorX:p,root:m}=e,g=(0,u.M)(d),v=(0,o.useId)(),y=!0,x=(0,o.useMemo)(()=>(y=!1,{id:v,initial:n,isPresent:s,custom:c,onExitComplete:e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;i&&i()},register:e=>(g.set(e,!1),()=>g.delete(e))}),[s,g,i]);return a&&y&&(x={...x}),(0,o.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[s]),o.useEffect(()=>{s||g.size||!i||i()},[s]),"popLayout"===f&&(t=(0,r.jsx)(h,{isPresent:s,anchorX:p,root:m,children:t})),(0,r.jsx)(l.t.Provider,{value:x,children:t})};function d(){return new Map}var m=n(32082);let g=e=>e.key||"";function v(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let y=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:c,presenceAffectsLayout:a=!0,mode:f="sync",propagate:h=!1,anchorX:d="left",root:y}=e,[x,E]=(0,m.xQ)(h),R=(0,o.useMemo)(()=>v(t),[t]),C=h&&!x?[]:R.map(g),P=(0,o.useRef)(!0),w=(0,o.useRef)(R),M=(0,u.M)(()=>new Map),[S,b]=(0,o.useState)(R),[j,k]=(0,o.useState)(R);(0,i.E)(()=>{P.current=!1,w.current=R;for(let e=0;e<j.length;e++){let t=g(j[e]);C.includes(t)?M.delete(t):!0!==M.get(t)&&M.set(t,!1)}},[j,C.length,C.join("-")]);let G=[];if(R!==S){let e=[...R];for(let t=0;t<j.length;t++){let n=j[t],r=g(n);C.includes(r)||(e.splice(t,0,n),G.push(n))}return"wait"===f&&G.length&&(e=G),k(v(e)),b(R),null}let{forceRender:A}=(0,o.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:j.map(e=>{let t=g(e),o=(!h||!!x)&&(R===j||C.includes(t));return(0,r.jsx)(p,{isPresent:o,initial:(!P.current||!!l)&&void 0,custom:n,presenceAffectsLayout:a,mode:f,root:y,onExitComplete:o?void 0:()=>{if(!M.has(t))return;M.set(t,!0);let e=!0;M.forEach(t=>{t||(e=!1)}),e&&(null==A||A(),k(w.current),h&&(null==E||E()),c&&c())},anchorX:d,children:e},t)})})}}}]);