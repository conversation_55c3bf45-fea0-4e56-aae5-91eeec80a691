1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.kojapay.betterinterest"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:40:5-67
13-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:40:22-64
14
15    <permission
15-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.kojapay.betterinterest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.kojapay.betterinterest.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:4:5-36:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:5:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ea82f0e4740fea90ca38a885280cdee8\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:icon="@mipmap/ic_launcher"
26-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:6:9-43
27        android:label="@string/app_name"
27-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:7:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:8:9-54
29        android:supportsRtl="true"
29-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:9:9-35
30        android:testOnly="true"
31        android:theme="@style/AppTheme" >
31-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:10:9-40
32        <activity
32-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:12:9-25:20
33            android:name="com.kojapay.betterinterest.MainActivity"
33-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:14:13-41
34            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
34-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:13:13-140
35            android:exported="true"
35-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:18:13-36
36            android:label="@string/title_activity_main"
36-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:15:13-56
37            android:launchMode="singleTask"
37-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:17:13-44
38            android:theme="@style/AppTheme.NoActionBarLaunch" >
38-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:16:13-62
39            <intent-filter>
39-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:20:13-23:29
40                <action android:name="android.intent.action.MAIN" />
40-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:21:17-69
40-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:21:25-66
41
42                <category android:name="android.intent.category.LAUNCHER" />
42-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:22:17-77
42-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:22:27-74
43            </intent-filter>
44        </activity>
45
46        <provider
47            android:name="androidx.core.content.FileProvider"
47-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:28:13-62
48            android:authorities="com.kojapay.betterinterest.fileprovider"
48-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:29:13-64
49            android:exported="false"
49-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:30:13-37
50            android:grantUriPermissions="true" >
50-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:31:13-47
51            <meta-data
51-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:32:13-34:64
52                android:name="android.support.FILE_PROVIDER_PATHS"
52-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:33:17-67
53                android:resource="@xml/file_paths" />
53-->C:\Users\<USER>\OneDrive\Desktop\DDAV\betterinte\android\app\src\main\AndroidManifest.xml:34:17-51
54        </provider>
55        <provider
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
56            android:name="androidx.startup.InitializationProvider"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
57            android:authorities="com.kojapay.betterinterest.androidx-startup"
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
58            android:exported="false" >
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
59            <meta-data
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
60                android:name="androidx.emoji2.text.EmojiCompatInitializer"
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
61                android:value="androidx.startup" />
61-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e9683f504198d3d554b1474ed6ab66f7\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
62            <meta-data
62-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
63                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
63-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
64                android:value="androidx.startup" />
64-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fcaa2d94568f1111aa0d45f264971fb\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
65            <meta-data
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
67                android:value="androidx.startup" />
67-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
68        </provider>
69
70        <receiver
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
71            android:name="androidx.profileinstaller.ProfileInstallReceiver"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
72            android:directBootAware="false"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
73            android:enabled="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
74            android:exported="true"
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
75            android:permission="android.permission.DUMP" >
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
76            <intent-filter>
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
77                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
77-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
78            </intent-filter>
79            <intent-filter>
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
80                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
80-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
81            </intent-filter>
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
83                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
83-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
86                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
86-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\9004adbda7bfb1b3286dec38d922dd38\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
87            </intent-filter>
88        </receiver>
89    </application>
90
91</manifest>
