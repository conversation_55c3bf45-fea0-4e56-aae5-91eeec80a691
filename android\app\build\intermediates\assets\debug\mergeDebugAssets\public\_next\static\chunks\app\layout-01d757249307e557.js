(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{10924:(e,t,r)=>{"use strict";r.d(t,{KR:()=>a});let a=()=>{var e,t;if(null==(t=window.Capacitor)||null==(e=t.isNativePlatform)?void 0:e.call(t))return!0;let r=navigator.userAgent.toLowerCase();return[/android/i,/iphone/i,/ipad/i,/ipod/i,/blackberry/i,/windows phone/i,/mobile/i].some(e=>e.test(r))}},19324:()=>{},21395:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var a=r(95155),o=r(35695),n=r(12115),i=r(98030),s=r(10924),l=r(68289),c=r(48016);function d(e){let{onComplete:t,duration:r=1e4}=e,[s,d]=(0,n.useState)(!0),u=(0,o.useRouter)(),{user:h,isLoading:g}=(0,i.A)();return((0,n.useEffect)(()=>{let e=setTimeout(()=>{d(!1),setTimeout(()=>{g||(h?u.push("/dashboard"):u.push("/auth/login")),null==t||t()},500)},r);return()=>clearTimeout(e)},[r,t,u,h,g]),s)?(0,a.jsxs)(l.P.div,{initial:{opacity:1},exit:{opacity:0},transition:{duration:.8},className:"fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-green-600 via-green-500 to-green-700 overflow-hidden",children:[(0,a.jsx)(l.P.div,{animate:{background:["linear-gradient(45deg, #059669, #10b981, #34d399)","linear-gradient(135deg, #10b981, #34d399, #059669)","linear-gradient(225deg, #34d399, #059669, #10b981)","linear-gradient(315deg, #059669, #10b981, #34d399)"]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute inset-0"}),(0,a.jsxs)("div",{className:"absolute inset-0 opacity-20",children:[(0,a.jsx)(l.P.div,{animate:{rotate:360},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute inset-0 bg-[radial-gradient(circle_at_30%_70%,rgba(255,255,255,0.1),transparent_50%)]"}),(0,a.jsx)(l.P.div,{animate:{rotate:-360},transition:{duration:25,repeat:1/0,ease:"linear"},className:"absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(255,255,255,0.08),transparent_60%)]"})]}),(0,a.jsxs)(l.P.div,{initial:{scale:.3,opacity:0,y:50},animate:{scale:1,opacity:1,y:0},transition:{duration:1.2,ease:"easeOut",delay:.3},className:"relative z-10 flex flex-col items-center px-8",children:[(0,a.jsx)(l.P.div,{animate:{scale:[1,1.08,1],rotateY:[0,5,-5,0],filter:["drop-shadow(0 0 20px rgba(255,255,255,0.3))","drop-shadow(0 0 30px rgba(255,255,255,0.5))","drop-shadow(0 0 20px rgba(255,255,255,0.3))"]},transition:{duration:3,repeat:1/0,ease:"easeInOut"},className:"mb-12",children:(0,a.jsx)(c.UU,{size:"xl",variant:"gradient",showIcon:!0,className:"text-7xl text-white drop-shadow-lg"})}),(0,a.jsx)(l.P.h1,{initial:{y:30,opacity:0},animate:{y:0,opacity:1},transition:{delay:1.2,duration:.8,ease:"easeOut"},className:"text-3xl font-bold text-white mb-4 text-center tracking-wide",children:"Better Interest"}),(0,a.jsx)(l.P.p,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},transition:{delay:1.8,duration:.8},className:"text-white/90 text-center text-lg font-medium max-w-sm mb-12",children:"Smart Savings, Better Returns"}),(0,a.jsxs)("div",{className:"w-64 max-w-xs",children:[(0,a.jsx)(l.P.div,{initial:{width:0},animate:{width:"100%"},transition:{delay:2.5,duration:6,ease:"easeInOut"},className:"h-2 bg-white/30 rounded-full overflow-hidden",children:(0,a.jsx)(l.P.div,{animate:{x:["-100%","100%"],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},className:"h-full w-1/3 bg-gradient-to-r from-transparent via-white to-transparent"})}),(0,a.jsx)(l.P.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:3,duration:.5},className:"text-white/80 text-center text-sm mt-4 font-medium",children:"Loading your financial future..."})]})]}),(0,a.jsxs)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:[[...Array(12)].map((e,t)=>(0,a.jsx)(l.P.div,{initial:{x:Math.random()*window.innerWidth,y:window.innerHeight+100,opacity:0,scale:0},animate:{y:-100,opacity:[0,.8,.4,0],scale:[0,1,.8,0],x:Math.random()*window.innerWidth,rotate:[0,180,360]},transition:{duration:4+3*Math.random(),delay:8*Math.random(),repeat:1/0,ease:"easeInOut"},className:"absolute rounded-full ".concat(t%3==0?"w-3 h-3 bg-white/40":t%3==1?"w-2 h-2 bg-white/60":"w-1 h-1 bg-white/80")},t)),[void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsx)(l.P.div,{initial:{x:Math.random()*window.innerWidth,y:window.innerHeight+200,opacity:0,scale:0},animate:{y:-200,opacity:[0,.3,.1,0],scale:[0,1.5,1,0],rotate:[0,-90,-180]},transition:{duration:6+2*Math.random(),delay:5*Math.random(),repeat:1/0,ease:"easeInOut"},className:"absolute w-6 h-6 bg-white/20 rounded-full blur-sm"},"large-".concat(t)))]})]}):null}function u(e){let{children:t}=e,[r,l]=(0,n.useState)(!1),[c,u]=(0,n.useState)(!1);(0,o.useRouter)();let h=(0,o.usePathname)(),{user:g,isLoading:p}=(0,i.A)();return((0,n.useEffect)(()=>{let e=setTimeout(()=>{(0,s.KR)()&&!c?l(!0):u(!0)},100);return()=>clearTimeout(e)},[h]),r)?(0,a.jsx)(d,{onComplete:()=>{l(!1),u(!0)},duration:1e4}):c?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-green-900 to-black flex items-center justify-center",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400"})})}},24241:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.t.bind(r,15863,23)),Promise.resolve().then(r.t.bind(r,19324,23)),Promise.resolve().then(r.bind(r,21395)),Promise.resolve().then(r.bind(r,64198)),Promise.resolve().then(r.bind(r,57740)),Promise.resolve().then(r.bind(r,98030))},48016:(e,t,r)=>{"use strict";r.d(t,{Qh:()=>c,UU:()=>l});var a=r(95155),o=r(68289),n=r(66766);let i={sm:{container:"h-8",text:"text-lg",icon:"w-6 h-6"},md:{container:"h-10",text:"text-xl",icon:"w-8 h-8"},lg:{container:"h-12",text:"text-2xl",icon:"w-10 h-10"},xl:{container:"h-16",text:"text-3xl",icon:"w-12 h-12"}},s={light:"text-white",dark:"text-gray-900",gradient:"bg-gradient-to-r from-green-400 to-green-600 bg-clip-text text-transparent"};function l(e){let{size:t="md",variant:r="gradient",showIcon:l=!0,className:c=""}=e,d=i[t],u=s[r];return(0,a.jsxs)(o.P.div,{className:"flex items-center space-x-2 ".concat(d.container," ").concat(c),whileHover:{scale:1.05},transition:{type:"spring",stiffness:400,damping:10},children:[l&&(0,a.jsx)(o.P.div,{className:"".concat(d.icon," flex items-center justify-center relative"),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(n.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(d.icon.split(" ")[0].replace("w-","")),height:4*parseInt(d.icon.split(" ")[1].replace("h-","")),className:"".concat(d.icon," object-contain"),priority:!0})}),(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)(o.P.h1,{className:"font-inter font-bold leading-tight ".concat(d.text," ").concat(u," relative"),initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{duration:.5},style:{letterSpacing:"0.02em",textShadow:"0 0 20px rgba(255, 255, 255, 0.1)"},children:[(0,a.jsx)("span",{className:"text-white relative",style:{textShadow:"0 0 15px rgba(255, 255, 255, 0.2)",fontWeight:"700"},children:"Better"}),(0,a.jsx)("span",{className:"text-green-400 ml-1 relative",style:{textShadow:"0 0 20px rgba(34, 197, 94, 0.4), 0 0 40px rgba(34, 197, 94, 0.2)",fontWeight:"800",letterSpacing:"0.05em"},children:"Interest"})]}),"lg"===t||"xl"===t?(0,a.jsx)(o.P.p,{className:"text-xs text-gray-400 -mt-1",initial:{opacity:0},animate:{opacity:1},transition:{duration:.5,delay:.2},children:"Smart Savings Platform"}):null]})]})}function c(e){let{size:t="md",className:r=""}=e,s=i[t];return(0,a.jsx)(o.P.div,{className:"".concat(s.icon," flex items-center justify-center ").concat(r),whileHover:{scale:1.1},whileTap:{scale:.95},transition:{type:"spring",stiffness:400,damping:10},children:(0,a.jsx)(n.default,{src:"/images/logo-text.png",alt:"Better Interest Logo",width:4*parseInt(s.icon.split(" ")[0].replace("w-","")),height:4*parseInt(s.icon.split(" ")[1].replace("h-","")),className:"".concat(s.icon," object-contain"),priority:!0})})}},57740:(e,t,r)=>{"use strict";r.d(t,{DP:()=>s,ThemeProvider:()=>i,Yx:()=>c});var a=r(95155),o=r(12115);let n=(0,o.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,o.useState)("dark"),[s,l]=(0,o.useState)(!1);return((0,o.useEffect)(()=>{l(!0);let e=localStorage.getItem("theme");e?i(e):i("dark")},[]),(0,o.useEffect)(()=>{s&&(document.documentElement.classList.remove("light","dark"),document.documentElement.classList.add(r),localStorage.setItem("theme",r))},[r,s]),s)?(0,a.jsx)(n.Provider,{value:{theme:r,toggleTheme:()=>{i(e=>"light"===e?"dark":"light")},setTheme:e=>{i(e)}},children:t}):(0,a.jsx)("div",{className:"min-h-screen bg-black",children:t})}function s(){let e=(0,o.useContext)(n);return void 0===e?(console.warn("useTheme called outside of ThemeProvider, using fallback values"),{theme:"dark",toggleTheme:()=>{},setTheme:()=>{}}):e}let l={light:{bg:{primary:"bg-white",secondary:"bg-gray-50",tertiary:"bg-gray-100",card:"bg-white",sidebar:"bg-white",header:"bg-white/80"},text:{primary:"text-gray-900",secondary:"text-gray-600",tertiary:"text-gray-500",inverse:"text-white"},border:{primary:"border-gray-200",secondary:"border-gray-300",accent:"border-green-200"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-500 text-green-600 hover:bg-green-500 hover:text-white"},accent:{green:"text-green-600",blue:"text-blue-600",purple:"text-purple-600",yellow:"text-yellow-600"}},dark:{bg:{primary:"bg-black",secondary:"bg-gray-900",tertiary:"bg-gray-800",card:"bg-gray-900",sidebar:"bg-gray-900",header:"bg-gray-900/80"},text:{primary:"text-white",secondary:"text-gray-300",tertiary:"text-gray-400",inverse:"text-black"},border:{primary:"border-gray-800",secondary:"border-gray-700",accent:"border-green-500/30"},button:{primary:"bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white hover:shadow-green-500/25",secondary:"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white hover:shadow-blue-500/25",outline:"bg-transparent border-2 border-green-400 text-green-400 hover:bg-green-400 hover:text-black"},accent:{green:"text-green-400",blue:"text-blue-400",purple:"text-purple-400",yellow:"text-yellow-400"}}};function c(e){return l[e]}},64198:(e,t,r)=>{"use strict";r.d(t,{CustomToaster:()=>l,P0:()=>s,oR:()=>n.Ay});var a=r(95155),o=r(68289),n=r(13568),i=r(10351);let s={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,t)=>n.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function l(){return(0,a.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,a.jsx)(n.bv,{toast:e,children:t=>{let{icon:r,message:s}=t;return(0,a.jsxs)(o.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:r}),(0,a.jsx)("div",{className:"flex-1",children:s}),"loading"!==e.type&&(0,a.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,a.jsx)(i.yGN,{className:"w-4 h-4"})})]})}})})}},96365:(e,t,r)=>{"use strict";r.d(t,{authService:()=>y});class a extends Error{static fromResponse(e){return new a(e.message||e.error,500,e.code,e.details,e.timestamp)}toJSON(){return{name:this.name,message:this.message,statusCode:this.statusCode,code:this.code,details:this.details,timestamp:this.timestamp}}constructor(e,t,r,a,o){super(e),this.statusCode=t,this.code=r,this.details=a,this.timestamp=o,this.name="ApiError",this.timestamp=o||new Date().toISOString()}}class o extends a{constructor(e,t){super(e,422,"VALIDATION_ERROR",t),this.validationErrors=t,this.name="ValidationError"}}class n extends a{constructor(e="Authentication required"){super(e,401,"AUTHENTICATION_ERROR"),this.name="AuthenticationError"}}class i extends a{constructor(e="Access denied"){super(e,403,"AUTHORIZATION_ERROR"),this.name="AuthorizationError"}}class s extends a{constructor(e="Resource not found"){super(e,404,"NOT_FOUND_ERROR"),this.name="NotFoundError"}}class l extends a{constructor(e="Network error occurred"){super(e,0,"NETWORK_ERROR"),this.name="NetworkError"}}class c extends a{constructor(e="Request timeout"){super(e,408,"TIMEOUT_ERROR"),this.name="TimeoutError"}}class d extends a{constructor(e="Internal server error"){super(e,500,"SERVER_ERROR"),this.name="ServerError"}}class u extends a{constructor(e="Service temporarily unavailable"){super(e,503,"SERVICE_UNAVAILABLE"),this.name="ServiceUnavailableError"}}let h=async e=>{let t;try{t=await e.json()}catch(r){t={error:"Unknown error occurred",message:"HTTP ".concat(e.status,": ").concat(e.statusText)}}switch(e.status){case 400:if(t.errors)throw new o(t.message||"Validation failed",t.errors);throw new a(t.message||t.error||"Bad request",400,t.code,t.details);case 401:throw new n(t.message||t.error);case 403:throw new i(t.message||t.error);case 404:throw new s(t.message||t.error);case 408:throw new c(t.message||t.error);case 422:throw new o(t.message||"Validation failed",t.errors||t.details||{});case 500:throw new d(t.message||t.error);case 503:throw new u(t.message||t.error);default:throw new a(t.message||t.error||"Request failed",e.status,t.code,t.details)}},g=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e4,o=new AbortController,n=setTimeout(()=>o.abort(),r);try{let r=await fetch(e,{...t,signal:o.signal,headers:{"Content-Type":"application/json",...t.headers}});clearTimeout(n),r.ok||await h(r);let i=r.headers.get("content-type");if(!i||!i.includes("application/json"))return{};let s=await r.json();if(!1===s.success)throw a.fromResponse(s);return s.data||s}catch(e){if(clearTimeout(n),e instanceof a)throw e;throw(e=>{if("AbortError"===e.name)throw new c("Request was cancelled");if("TypeError"===e.name&&e.message.includes("fetch"))throw new l("Network connection failed");throw new l(e.message||"Network error occurred")})(e)}},p="http://localhost:8080/api";class m{getAuthHeaders(){let e=localStorage.getItem("auth_token");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}}async login(e){var t,r;let a=await g("".concat(p,"/api/auth/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});console.log("LOGIN RESPONSE:",a);let o=(null==a||null==(t=a.data)?void 0:t.token)||(null==a?void 0:a.token),n=(null==a||null==(r=a.data)?void 0:r.refreshToken)||(null==a?void 0:a.refreshToken);return o?(console.log("Saving auth_token:",o),localStorage.setItem("auth_token",o)):console.warn("No token found in login response:",a),n&&localStorage.setItem("refresh_token",n),a}async signup(e){var t,r,a,o;let n=await g("".concat(p,"/api/auth/signup"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),i=(null==n||null==(t=n.data)?void 0:t.token)||(null==n?void 0:n.token),s=(null==n||null==(r=n.data)?void 0:r.refreshToken)||(null==n?void 0:n.refreshToken),l=(null==n||null==(a=n.data)?void 0:a.user)||(null==n?void 0:n.user);return i&&localStorage.setItem("auth_token",i),s&&localStorage.setItem("refresh_token",s),{user:l,token:i,refreshToken:s,expiresIn:(null==n||null==(o=n.data)?void 0:o.expiresIn)||(null==n?void 0:n.expiresIn)||3600}}async adminLogin(e){let t=await fetch("".concat(p,"/auth/admin/login"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Admin login failed");let r=await t.json();return localStorage.setItem("auth_token",r.token),r.refreshToken&&localStorage.setItem("refresh_token",r.refreshToken),r}async logout(){try{await fetch("".concat(p,"/auth/logout"),{method:"POST",headers:this.getAuthHeaders()})}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token")}}async getCurrentUser(){let e=await fetch("".concat(p,"/api/auth/user-info"),{headers:this.getAuthHeaders()});if(!e.ok)throw Error("Failed to get current user");return(await e.json()).user}async refreshToken(){let e=localStorage.getItem("refresh_token");if(!e)throw Error("No refresh token available");let t=await fetch("".concat(p,"/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)throw Error("Token refresh failed");let r=await t.json();return localStorage.setItem("auth_token",r.data.token),r.data.refreshToken&&localStorage.setItem("refresh_token",r.data.refreshToken),{success:r.success,token:r.data.token,refreshToken:r.data.refreshToken,expiresIn:r.data.expiresIn||3600}}async requestPasswordReset(e){let t=await fetch("".concat(p,"/auth/password-reset/request"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset request failed")}async confirmPasswordReset(e){let t=await fetch("".concat(p,"/auth/password-reset/confirm"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password reset failed")}async changePassword(e){let t=await fetch("".concat(p,"/auth/change-password"),{method:"POST",headers:this.getAuthHeaders(),body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Password change failed")}getStoredToken(){return localStorage.getItem("auth_token")}isTokenExpired(e){try{let t=JSON.parse(atob(e.split(".")[1]));return 1e3*t.exp<Date.now()}catch(e){return!0}}}let y=new m},98030:(e,t,r)=>{"use strict";r.d(t,{A:()=>d,AuthProvider:()=>c});var a=r(95155),o=r(12115),n=r(96365);let i={user:null,token:null,isAuthenticated:!1,isLoading:!0,error:null};function s(e,t){switch(t.type){case"AUTH_START":return{...e,isLoading:!0,error:null};case"AUTH_SUCCESS":return{...e,user:t.payload.user,token:t.payload.token,isAuthenticated:!0,isLoading:!1,error:null};case"AUTH_FAILURE":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:t.payload};case"LOGOUT":return{...e,user:null,token:null,isAuthenticated:!1,isLoading:!1,error:null};case"UPDATE_USER":return{...e,user:t.payload};case"CLEAR_ERROR":return{...e,error:null};default:return e}}let l=(0,o.createContext)(void 0);function c(e){let{children:t}=e,[r,c]=(0,o.useReducer)(s,i);(0,o.useEffect)(()=>{d()},[]),(0,o.useEffect)(()=>{if(r.token&&r.isAuthenticated){let e=setInterval(async()=>{try{r.token&&n.authService.isTokenExpired(r.token)&&await n.authService.refreshToken()}catch(e){console.error("Token refresh failed:",e),p()}},3e5);return()=>clearInterval(e)}},[r.token,r.isAuthenticated]);let d=async()=>{try{let e=n.authService.getStoredToken();if(!e)return void c({type:"AUTH_FAILURE",payload:"No token found"});if(n.authService.isTokenExpired(e))try{await n.authService.refreshToken();let e=n.authService.getStoredToken();if(e){let t=await n.authService.getCurrentUser();c({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}else throw Error("Token refresh failed")}catch(e){c({type:"AUTH_FAILURE",payload:"Session expired"});return}else{let t=await n.authService.getCurrentUser();c({type:"AUTH_SUCCESS",payload:{user:t,token:e}})}}catch(e){c({type:"AUTH_FAILURE",payload:"Authentication check failed"})}},u=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.login(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Login failed"}),e}},h=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.signup(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Signup failed"}),e}},g=async e=>{try{c({type:"AUTH_START"});let t=await n.authService.adminLogin(e);c({type:"AUTH_SUCCESS",payload:{user:t.user,token:t.token}})}catch(e){throw c({type:"AUTH_FAILURE",payload:e instanceof Error?e.message:"Admin login failed"}),e}},p=async()=>{try{await n.authService.logout()}catch(e){console.error("Logout error:",e)}finally{c({type:"LOGOUT"})}},m={...r,login:u,signup:h,adminLogin:g,logout:p,updateUser:e=>{c({type:"UPDATE_USER",payload:e})},clearError:()=>{c({type:"CLEAR_ERROR"})},checkAuth:d};return(0,a.jsx)(l.Provider,{value:m,children:t})}function d(){let e=(0,o.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}}},e=>{e.O(0,[354,2533,844,5236,6766,3568,7974,8441,5964,7358],()=>e(e.s=24241)),_N_E=e.O()}]);