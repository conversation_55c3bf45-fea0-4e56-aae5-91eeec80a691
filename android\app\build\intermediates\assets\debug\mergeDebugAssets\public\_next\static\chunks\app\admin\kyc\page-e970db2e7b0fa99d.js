(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1064],{21162:(e,s,t)=>{Promise.resolve().then(t.bind(t,23367))},23367:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var a=t(95155),l=t(12115),r=t(10351),n=t(26071),i=t(52814),c=t(13741),d=t(17703),o=t(93915),x=t(66440),m=t(29925),h=t(30353),u=t(75399),j=t(41659),g=t(64198),p=t(24630);let v={NATIONAL_ID:"\uD83C\uDD94",PASSPORT:"\uD83D\uDCD8",DRIVERS_LICENSE:"\uD83D\uDE97",VOTERS_CARD:"\uD83D\uDDF3️",UTILITY_BILL:"\uD83D\uDCC4",BANK_STATEMENT:"\uD83C\uDFE6",SELFIE:"\uD83E\uDD33",SIGNATURE:"✍️",OTHER:"\uD83D\uDCCE"},N=function(){let[e,s]=(0,l.useState)([]),[t,N]=(0,l.useState)(!0),[f,b]=(0,l.useState)(null),[y,w]=(0,l.useState)(!1),[E,R]=(0,l.useState)("APPROVED"),[C,A]=(0,l.useState)(""),[I,D]=(0,l.useState)(""),[P,T]=(0,l.useState)({search:"",status:void 0,level:void 0,dateFrom:"",dateTo:"",sortBy:"submittedAt",sortOrder:"desc",page:1,limit:20}),[S,k]=(0,l.useState)({total:0,page:1,limit:20,totalPages:0});(0,l.useEffect)(()=>{O()},[P]);let O=async()=>{try{N(!0);let e=await p.$p.getAllKYC(P);s(e.kycs),k({total:e.total,page:e.page,limit:e.limit,totalPages:e.totalPages})}catch(e){g.oR.error("Failed to load KYC records")}finally{N(!1)}},V=async()=>{if(f)try{await p.$p.reviewKYC({kycId:f.id,status:E,reviewNotes:C,rejectionReason:"REJECTED"===E?I:void 0}),s(e.map(e=>e.id===f.id?{...e,status:E,reviewNotes:C,rejectionReason:I}:e)),g.oR.success("KYC ".concat(E.toLowerCase()," successfully")),w(!1),A(""),D("")}catch(e){g.oR.error("Failed to review KYC")}},L=async(e,s)=>{try{let t=await p.$p.downloadDocument(e,s),a=window.URL.createObjectURL(t),l=document.createElement("a");l.href=a,l.download="document-".concat(s),document.body.appendChild(l),l.click(),window.URL.revokeObjectURL(a),document.body.removeChild(l)}catch(e){g.oR.error("Failed to download document")}},_=e=>new Date(e).toLocaleDateString("en-NG"),K=e=>{switch(e){case"APPROVED":return"success";case"PENDING":return"warning";case"UNDER_REVIEW":return"info";case"REJECTED":return"error";default:return"default"}},Y=e=>{switch(e){case"BASIC":return"success";case"INTERMEDIATE":return"warning";case"ADVANCED":return"info";default:return"default"}},U=[{key:"personalInfo",title:"User",render:(e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"w-10 h-10 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white font-semibold",children:[s.personalInfo.firstName.charAt(0),s.personalInfo.lastName.charAt(0)]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium text-white",children:[s.personalInfo.firstName," ",s.personalInfo.lastName]}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:s.contactInfo.email})]})]})},{key:"level",title:"Level",render:(e,s)=>(0,a.jsx)(i.E,{variant:Y(s.level),children:s.level})},{key:"status",title:"Status",render:(e,s)=>(0,a.jsx)(i.E,{variant:K(s.status),children:s.status.replace("_"," ")})},{key:"documents",title:"Documents",render:(e,s)=>(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("p",{className:"text-white",children:[s.documents.length," uploaded"]}),(0,a.jsxs)("p",{className:"text-gray-400",children:[s.documents.filter(e=>"VERIFIED"===e.status).length," verified"]})]})},{key:"submittedAt",title:"Submitted",render:(e,s)=>(0,a.jsxs)("div",{className:"text-sm text-gray-400",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(r.wIk,{className:"mr-1"}),s.submittedAt?_(s.submittedAt):"Not submitted"]}),s.reviewedAt&&(0,a.jsxs)("p",{className:"mt-1",children:["Reviewed: ",_(s.reviewedAt)]})]})},{key:"id",title:"Actions",render:(e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.$n,{size:"sm",variant:"outline",onClick:()=>{b(s),w(!0)},children:(0,a.jsx)(r.Vap,{})}),"PENDING"===s.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.$n,{size:"sm",variant:"outline",onClick:()=>{b(s),R("APPROVED"),w(!0)},className:"text-green-400 hover:text-green-300",children:(0,a.jsx)(r.YrT,{})}),(0,a.jsx)(c.$n,{size:"sm",variant:"outline",onClick:()=>{b(s),R("REJECTED"),w(!0)},className:"text-red-400 hover:text-red-300",children:(0,a.jsx)(r.yGN,{})})]})]})}];return(0,a.jsxs)(n.A,{title:"KYC Management",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"KYC Management"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Review and manage user verification documents"})]}),(0,a.jsx)("div",{className:"flex space-x-2",children:(0,a.jsxs)(c.$n,{variant:"outline",onClick:O,children:[(0,a.jsx)(r.jTZ,{className:"mr-2"}),"Refresh"]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Pending Review"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-yellow-400",children:e.filter(e=>"PENDING"===e.status).length})]}),(0,a.jsx)(r.Ohp,{className:"text-yellow-500 text-2xl"})]})}),(0,a.jsx)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Approved"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-green-400",children:e.filter(e=>"APPROVED"===e.status).length})]}),(0,a.jsx)(r.YrT,{className:"text-green-500 text-2xl"})]})}),(0,a.jsx)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Rejected"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-red-400",children:e.filter(e=>"REJECTED"===e.status).length})]}),(0,a.jsx)(r.yGN,{className:"text-red-500 text-2xl"})]})}),(0,a.jsx)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Under Review"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-400",children:e.filter(e=>"UNDER_REVIEW"===e.status).length})]}),(0,a.jsx)(r.pcC,{className:"text-blue-500 text-2xl"})]})})]}),(0,a.jsx)(d.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsx)(o.p,{placeholder:"Search by name or email...",value:P.search,onChange:e=>T({...P,search:e.target.value}),leftIcon:(0,a.jsx)(r.CKj,{})}),(0,a.jsx)(h.l,{value:P.status||"",onChange:e=>T({...P,status:e.target.value}),options:[{value:"",label:"All Status"},{value:"PENDING",label:"Pending"},{value:"UNDER_REVIEW",label:"Under Review"},{value:"APPROVED",label:"Approved"},{value:"REJECTED",label:"Rejected"}]}),(0,a.jsx)(h.l,{value:P.level||"",onChange:e=>T({...P,level:e.target.value}),options:[{value:"",label:"All Levels"},{value:"BASIC",label:"Basic"},{value:"INTERMEDIATE",label:"Intermediate"},{value:"ADVANCED",label:"Advanced"}]}),(0,a.jsx)(o.p,{type:"date",value:P.dateFrom,onChange:e=>T({...P,dateFrom:e.target.value}),placeholder:"From Date"}),(0,a.jsx)(o.p,{type:"date",value:P.dateTo,onChange:e=>T({...P,dateTo:e.target.value}),placeholder:"To Date"})]})}),(0,a.jsxs)(d.Z,{className:"bg-gray-800 border-gray-700",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-white",children:["KYC Records (",S.total.toLocaleString(),")"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.l,{value:P.sortBy,onChange:e=>T({...P,sortBy:e}),options:[{value:"submittedAt",label:"Date Submitted"},{value:"status",label:"Status"},{value:"level",label:"Level"}]}),(0,a.jsx)(c.$n,{size:"sm",variant:"outline",onClick:()=>T({...P,sortOrder:"asc"===P.sortOrder?"desc":"asc"}),children:"asc"===P.sortOrder?"↑":"↓"})]})]})}),(0,a.jsx)(u.XI,{columns:U,data:e,loading:t,emptyMessage:"No KYC records found"}),(0,a.jsx)("div",{className:"p-6 border-t border-gray-700",children:(0,a.jsx)(m.dK,{currentPage:S.page,totalPages:S.totalPages,onPageChange:e=>T({...P,page:e}),showInfo:!0,totalItems:S.total,itemsPerPage:S.limit})})]})]}),(0,a.jsx)(x.aF,{isOpen:y,onClose:()=>w(!1),title:"Review KYC Application",size:"xl",children:f&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"w-16 h-16 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-white text-xl font-bold",children:[f.personalInfo.firstName.charAt(0),f.personalInfo.lastName.charAt(0)]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-white",children:[f.personalInfo.firstName," ",f.personalInfo.lastName]}),(0,a.jsx)("p",{className:"text-gray-400",children:f.contactInfo.email}),(0,a.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,a.jsx)(i.E,{variant:Y(f.level),children:f.level}),(0,a.jsx)(i.E,{variant:K(f.status),children:f.status})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-white mb-2",children:"Personal Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Date of Birth:"}),(0,a.jsx)("span",{className:"text-white",children:f.personalInfo.dateOfBirth})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Nationality:"}),(0,a.jsx)("span",{className:"text-white",children:f.personalInfo.nationality})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Gender:"}),(0,a.jsx)("span",{className:"text-white",children:f.personalInfo.gender})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-white mb-2",children:"Contact Information"}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Phone:"}),(0,a.jsx)("span",{className:"text-white",children:f.contactInfo.phoneNumber})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Address:"}),(0,a.jsx)("span",{className:"text-white",children:f.addressInfo.street})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"City:"}),(0,a.jsx)("span",{className:"text-white",children:f.addressInfo.city})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-semibold text-white mb-4",children:"Uploaded Documents"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:f.documents.map(e=>(0,a.jsxs)("div",{className:"p-4 bg-gray-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:v[e.type]}),(0,a.jsx)("span",{className:"font-medium text-white",children:e.name})]}),(0,a.jsx)(i.E,{variant:K(e.status),children:e.status})]}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mb-2",children:e.type.replace("_"," ")}),(0,a.jsxs)(c.$n,{size:"sm",variant:"outline",onClick:()=>L(f.id,e.id),children:[(0,a.jsx)(r.a4x,{className:"mr-1"}),"Download"]})]},e.id))})]}),(0,a.jsxs)("div",{className:"space-y-4 p-4 bg-gray-700 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"reviewAction",value:"APPROVED",checked:"APPROVED"===E,onChange:e=>R(e.target.value),className:"text-green-600"}),(0,a.jsx)("span",{className:"text-white",children:"Approve"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"radio",name:"reviewAction",value:"REJECTED",checked:"REJECTED"===E,onChange:e=>R(e.target.value),className:"text-red-600"}),(0,a.jsx)("span",{className:"text-white",children:"Reject"})]})]}),(0,a.jsx)(j.T,{label:"Review Notes",value:C,onChange:e=>A(e.target.value),placeholder:"Add your review notes...",rows:3}),"REJECTED"===E&&(0,a.jsx)(j.T,{label:"Rejection Reason",value:I,onChange:e=>D(e.target.value),placeholder:"Specify the reason for rejection...",rows:2,required:!0})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-700",children:[(0,a.jsx)(c.$n,{variant:"outline",onClick:()=>w(!1),children:"Cancel"}),(0,a.jsx)(c.$n,{variant:"APPROVED"===E?"primary":"danger",onClick:V,disabled:"REJECTED"===E&&!I,children:"APPROVED"===E?"Approve KYC":"Reject KYC"})]})]})})]})}},41659:(e,s,t)=>{"use strict";t.d(s,{A:()=>r,T:()=>l});var a=t(95155);let l=(0,t(12115).forwardRef)((e,s)=>{let{label:t,error:l,helperText:r,variant:n="default",resize:i="vertical",className:c="",...d}=e,o="\n    ".concat("\n    w-full px-4 py-3 rounded-lg transition-all duration-200\n    focus:outline-none focus:ring-2 focus:ring-green-500/50\n    disabled:opacity-50 disabled:cursor-not-allowed\n    min-h-[100px]\n  "," \n    ").concat({default:"\n      bg-gray-800 border border-gray-700 text-white\n      placeholder-gray-400 hover:border-gray-600\n      focus:border-green-500\n    ",filled:"\n      bg-gray-700 border-0 text-white\n      placeholder-gray-400 hover:bg-gray-600\n    ",outline:"\n      bg-transparent border-2 border-gray-600 text-white\n      placeholder-gray-400 hover:border-gray-500\n      focus:border-green-500\n    "}[n]," \n    ").concat({none:"resize-none",vertical:"resize-y",horizontal:"resize-x",both:"resize"}[i]," \n    ").concat(c," \n    ").concat(l?"border-red-500 focus:border-red-500 focus:ring-red-500/50":"","\n  ");return(0,a.jsxs)("div",{className:"w-full",children:[t&&(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:t}),(0,a.jsx)("textarea",{ref:s,className:o,...d}),l&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-400",children:l}),r&&!l&&(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:r})]})});l.displayName="Textarea";let r=l}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,411,6071,8940,8441,5964,7358],()=>e(e.s=21162)),_N_E=e.O()}]);