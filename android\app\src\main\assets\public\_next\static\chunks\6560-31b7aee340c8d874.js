"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6560],{14585:(e,a,s)=>{s.d(a,{TM:()=>c,ZQ:()=>n,pp:()=>i});var r=s(95155),t=s(12115),l=s(68289);let n=(0,t.forwardRef)((e,a)=>{let{label:s,error:t,icon:n,helperText:i,className:c="",...d}=e;return(0,r.jsxs)("div",{className:"space-y-2",children:[s&&(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-300",children:[s,d.required&&(0,r.jsx)("span",{className:"text-red-400 ml-1",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[n&&(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(n,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)("input",{ref:a,className:"\n              w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg\n              text-white placeholder-gray-400\n              focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\n              disabled:bg-gray-900 disabled:text-gray-500\n              ".concat(n?"pl-10":"","\n              ").concat(t?"border-red-500 focus:ring-red-500":"","\n              ").concat(c,"\n            "),...d})]}),t&&(0,r.jsx)(l.P.p,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"text-red-400 text-sm",children:t}),i&&!t&&(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:i})]})});n.displayName="FormInput";let i=(0,t.forwardRef)((e,a)=>{let{label:s,error:t,options:n,placeholder:i,className:c="",...d}=e;return(0,r.jsxs)("div",{className:"space-y-2",children:[s&&(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-300",children:[s,d.required&&(0,r.jsx)("span",{className:"text-red-400 ml-1",children:"*"})]}),(0,r.jsxs)("select",{ref:a,className:"\n            w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg\n            text-white\n            focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\n            disabled:bg-gray-900 disabled:text-gray-500\n            ".concat(t?"border-red-500 focus:ring-red-500":"","\n            ").concat(c,"\n          "),...d,children:[i&&(0,r.jsx)("option",{value:"",disabled:!0,children:i}),n.map(e=>(0,r.jsx)("option",{value:e.value,children:e.label},e.value))]}),t&&(0,r.jsx)(l.P.p,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"text-red-400 text-sm",children:t})]})});i.displayName="SelectInput";let c=(0,t.forwardRef)((e,a)=>{let{label:s,error:t,helperText:n,className:i="",...c}=e;return(0,r.jsxs)("div",{className:"space-y-2",children:[s&&(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-300",children:[s,c.required&&(0,r.jsx)("span",{className:"text-red-400 ml-1",children:"*"})]}),(0,r.jsx)("textarea",{ref:a,className:"\n            w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg\n            text-white placeholder-gray-400\n            focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent\n            disabled:bg-gray-900 disabled:text-gray-500\n            resize-vertical\n            ".concat(t?"border-red-500 focus:ring-red-500":"","\n            ").concat(i,"\n          "),...c}),t&&(0,r.jsx)(l.P.p,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"text-red-400 text-sm",children:t}),n&&!t&&(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:n})]})});c.displayName="Textarea"},66440:(e,a,s)=>{s.d(a,{Ay:()=>o,aF:()=>c,k5:()=>d});var r=s(95155),t=s(60760),l=s(68289);s(12115);var n=s(10351);let i={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"};function c(e){let{isOpen:a,onClose:s,title:c,children:d,size:o="md",showCloseButton:x=!0}=e;return(0,r.jsx)(t.N,{children:a&&(0,r.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,r.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:s}),(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,r.jsxs)(l.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"\n                relative w-full ".concat(i[o]," \n                bg-gray-900 border border-gray-800 rounded-lg shadow-xl\n              "),onClick:e=>e.stopPropagation(),children:[(c||x)&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800",children:[c&&(0,r.jsx)("h2",{className:"text-xl font-semibold text-white",children:c}),x&&(0,r.jsx)("button",{onClick:s,className:"p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,r.jsx)(n.yGN,{className:"w-5 h-5 text-gray-400"})})]}),(0,r.jsx)("div",{className:"p-6",children:d})]})})]})})}function d(e){let{isOpen:a,onClose:s,title:t,children:l,onSubmit:n,submitText:i="Submit",isLoading:d=!1,size:o="md"}=e;return(0,r.jsx)(c,{isOpen:a,onClose:s,title:t,size:o,children:(0,r.jsxs)("form",{onSubmit:n,className:"space-y-6",children:[l,(0,r.jsxs)("div",{className:"flex space-x-3 justify-end pt-4 border-t border-gray-800",children:[(0,r.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-300 hover:text-white transition-colors",disabled:d,children:"Cancel"}),(0,r.jsxs)("button",{type:"submit",disabled:d,className:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2",children:[d&&(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:i})]})]})]})})}let o=c}}]);