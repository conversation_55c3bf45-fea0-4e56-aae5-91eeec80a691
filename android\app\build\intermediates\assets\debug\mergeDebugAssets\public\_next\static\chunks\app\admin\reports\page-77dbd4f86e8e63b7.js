(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7618],{17345:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>j});var r=t(95155),s=t(68289),l=t(12115),i=t(10351),n=t(26071),c=t(52814),d=t(13741),o=t(17703),m=t(93915),x=t(66440),p=t(30353),u=t(75399),h=t(64198),g=t(24630);let y=[{id:"user-activity",name:"User Activity Report",description:"Detailed user engagement and activity metrics",icon:i.cfS,color:"bg-blue-600"},{id:"financial-summary",name:"Financial Summary Report",description:"Comprehensive financial transactions and balances",icon:i.z8N,color:"bg-green-600"},{id:"savings-performance",name:"Savings Performance Report",description:"Savings plans performance and completion rates",icon:i.ARf,color:"bg-purple-600"},{id:"kyc-compliance",name:"KYC Compliance Report",description:"KYC verification status and compliance metrics",icon:i.jH2,color:"bg-orange-600"},{id:"transaction-analysis",name:"Transaction Analysis Report",description:"Detailed transaction patterns and analysis",icon:i.hht,color:"bg-indigo-600"},{id:"revenue-breakdown",name:"Revenue Breakdown Report",description:"Revenue sources and fee collection analysis",icon:i.eXT,color:"bg-pink-600"}];function j(){let[e,a]=(0,l.useState)([]),[t,j]=(0,l.useState)(!0),[b,v]=(0,l.useState)(!1),[f,N]=(0,l.useState)(""),[w,k]=(0,l.useState)(!1),[C,R]=(0,l.useState)({dateFrom:"",dateTo:"",format:"PDF",includeCharts:!0,emailDelivery:!1,recipients:""}),[E,D]=(0,l.useState)({type:"",status:"",dateFrom:"",dateTo:""});(0,l.useEffect)(()=>{F()},[E]);let F=async()=>{try{j(!0);let e=await g.ZJ.getReports(E);a(e.reports)}catch(e){h.oR.error("Failed to load reports")}finally{j(!1)}},S=async()=>{try{if(!f||!C.dateFrom||!C.dateTo)return void h.oR.error("Please fill in all required fields");k(!0),await g.ZJ.generateReport({name:"".concat(f," Report"),type:f,parameters:{},...C,format:C.format,recipients:C.recipients?C.recipients.split(",").map(e=>e.trim()):[]}),h.oR.success("Report generation started. You will be notified when ready."),v(!1),N(""),R({dateFrom:"",dateTo:"",format:"PDF",includeCharts:!0,emailDelivery:!1,recipients:""}),F()}catch(e){h.oR.error(e.message||"Failed to generate report")}finally{k(!1)}},T=async e=>{try{let a=await g.ZJ.downloadReport(e),t=window.URL.createObjectURL(a),r=document.createElement("a");r.href=t,r.download="report-".concat(e,".pdf"),document.body.appendChild(r),r.click(),window.URL.revokeObjectURL(t),document.body.removeChild(r),h.oR.success("Report downloaded successfully")}catch(e){h.oR.error("Failed to download report")}},A=e=>new Date(e).toLocaleDateString("en-NG",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),L=[{key:"name",title:"Report Name",render:e=>{var a,t;return(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 ".concat((null==(a=y.find(a=>a.id===e.type))?void 0:a.color)||"bg-gray-600"," rounded-lg flex items-center justify-center"),children:l.createElement((null==(t=y.find(a=>a.id===e.type))?void 0:t.icon)||i.jH2,{className:"text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-white",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:e.description})]})]})}},{key:"type",title:"Type",render:e=>{var a;return(0,r.jsx)(c.E,{variant:"default",children:(null==(a=y.find(a=>a.id===e.type))?void 0:a.name)||e.type})}},{key:"period",title:"Period",render:e=>(0,r.jsxs)("div",{className:"text-sm text-gray-400",children:[(0,r.jsxs)("p",{children:[A(e.dateFrom)," -"]}),(0,r.jsx)("p",{children:A(e.dateTo)})]})},{key:"status",title:"Status",render:e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(e=>{switch(e){case"COMPLETED":return(0,r.jsx)(i.a4x,{className:"text-green-500"});case"GENERATING":return(0,r.jsx)(i.jTZ,{className:"text-yellow-500 animate-spin"});case"FAILED":return(0,r.jsx)(i.jH2,{className:"text-red-500"});case"SCHEDULED":return(0,r.jsx)(i.Ohp,{className:"text-blue-500"});default:return(0,r.jsx)(i.jH2,{className:"text-gray-500"})}})(e.status),(0,r.jsx)(c.E,{variant:(e=>{switch(e){case"COMPLETED":return"success";case"GENERATING":return"warning";case"FAILED":return"error";case"SCHEDULED":return"info";default:return"default"}})(e.status),children:e.status})]})},{key:"size",title:"Size",render:e=>(0,r.jsx)("span",{className:"text-gray-400",children:e.fileSize?"".concat((e.fileSize/1024/1024).toFixed(2)," MB"):"-"})},{key:"createdAt",title:"Generated",render:e=>(0,r.jsx)("span",{className:"text-gray-400",children:A(e.createdAt)})},{key:"actions",title:"Actions",render:e=>(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:["COMPLETED"===e.status&&(0,r.jsx)(d.$n,{size:"sm",variant:"outline",onClick:()=>T(e.id),children:(0,r.jsx)(i.a4x,{})}),(0,r.jsx)(d.$n,{size:"sm",variant:"outline",children:(0,r.jsx)(i.Vap,{})})]})}];return(0,r.jsxs)(n.A,{title:"Reports",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Reports"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Generate and manage platform reports"})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(d.$n,{onClick:()=>v(!0),className:"bg-green-600 hover:bg-green-700",children:[(0,r.jsx)(i.jH2,{className:"mr-2"}),"Generate Report"]}),(0,r.jsxs)(d.$n,{variant:"outline",onClick:F,children:[(0,r.jsx)(i.jTZ,{className:"mr-2"}),"Refresh"]})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:y.map(e=>{let a=e.icon;return(0,r.jsx)(s.P.div,{whileHover:{scale:1.02},className:"cursor-pointer",onClick:()=>{N(e.id),v(!0)},children:(0,r.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6 hover:border-green-500 transition-colors",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 ".concat(e.color," rounded-lg flex items-center justify-center"),children:(0,r.jsx)(a,{className:"text-white text-xl"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-white mb-2",children:e.name}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:e.description})]})]})})},e.id)})}),(0,r.jsx)(o.Z,{className:"bg-gray-800 border-gray-700 p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(p.l,{value:E.type,onChange:e=>D({...E,type:e.target.value}),options:[{value:"",label:"All Types"},...y.map(e=>({value:e.id,label:e.name}))]}),(0,r.jsx)(p.l,{value:E.status,onChange:e=>D({...E,status:e.target.value}),options:[{value:"",label:"All Status"},{value:"COMPLETED",label:"Completed"},{value:"GENERATING",label:"Generating"},{value:"FAILED",label:"Failed"},{value:"SCHEDULED",label:"Scheduled"}]}),(0,r.jsx)(m.p,{type:"date",value:E.dateFrom,onChange:e=>D({...E,dateFrom:e.target.value}),placeholder:"From Date"}),(0,r.jsx)(m.p,{type:"date",value:E.dateTo,onChange:e=>D({...E,dateTo:e.target.value}),placeholder:"To Date"})]})}),(0,r.jsxs)(o.Z,{className:"bg-gray-800 border-gray-700",children:[(0,r.jsx)("div",{className:"p-6 border-b border-gray-700",children:(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Generated Reports"})}),(0,r.jsx)(u.XI,{columns:L,data:e,loading:t,emptyMessage:"No reports found"})]})]}),(0,r.jsx)(x.aF,{isOpen:b,onClose:()=>v(!1),title:"Generate Report",size:"lg",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-400 mb-3",children:"Report Type"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:y.map(e=>{let a=e.icon;return(0,r.jsx)("div",{onClick:()=>N(e.id),className:"p-4 rounded-lg border cursor-pointer transition-colors ".concat(f===e.id?"border-green-500 bg-green-600/10":"border-gray-600 hover:border-gray-500"),children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 ".concat(e.color," rounded-lg flex items-center justify-center"),children:(0,r.jsx)(a,{className:"text-white text-sm"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-white text-sm",children:e.name}),(0,r.jsx)("p",{className:"text-gray-400 text-xs",children:e.description})]})]})},e.id)})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(m.p,{label:"From Date",type:"date",value:C.dateFrom,onChange:e=>R({...C,dateFrom:e.target.value}),required:!0}),(0,r.jsx)(m.p,{label:"To Date",type:"date",value:C.dateTo,onChange:e=>R({...C,dateTo:e.target.value}),required:!0})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(p.l,{label:"Format",value:C.format,onChange:e=>R({...C,format:e.target.value}),options:[{value:"PDF",label:"PDF Document"},{value:"EXCEL",label:"Excel Spreadsheet"},{value:"CSV",label:"CSV File"}]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"includeCharts",checked:C.includeCharts,onChange:e=>R({...C,includeCharts:e.target.checked}),className:"rounded border-gray-600 bg-gray-700 text-green-600"}),(0,r.jsx)("label",{htmlFor:"includeCharts",className:"text-white",children:"Include charts and visualizations"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("input",{type:"checkbox",id:"emailDelivery",checked:C.emailDelivery,onChange:e=>R({...C,emailDelivery:e.target.checked}),className:"rounded border-gray-600 bg-gray-700 text-green-600"}),(0,r.jsx)("label",{htmlFor:"emailDelivery",className:"text-white",children:"Email report when ready"})]}),C.emailDelivery&&(0,r.jsx)(m.p,{label:"Email Recipients",value:C.recipients,onChange:e=>R({...C,recipients:e.target.value}),placeholder:"<EMAIL>, <EMAIL>"})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-3 pt-4 border-t border-gray-700",children:[(0,r.jsx)(d.$n,{variant:"outline",onClick:()=>v(!1),children:"Cancel"}),(0,r.jsx)(d.$n,{onClick:S,className:"bg-green-600 hover:bg-green-700",disabled:w||!f||!C.dateFrom||!C.dateTo,children:w?(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Generating..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.jH2,{className:"mr-2"}),"Generate Report"]})})]})]})})]})}},54212:(e,a,t)=>{Promise.resolve().then(t.bind(t,17345))},66440:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>o,aF:()=>c,k5:()=>d});var r=t(95155),s=t(60760),l=t(68289);t(12115);var i=t(10351);let n={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"};function c(e){let{isOpen:a,onClose:t,title:c,children:d,size:o="md",showCloseButton:m=!0}=e;return(0,r.jsx)(s.N,{children:a&&(0,r.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,r.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:t}),(0,r.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,r.jsxs)(l.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"\n                relative w-full ".concat(n[o]," \n                bg-gray-900 border border-gray-800 rounded-lg shadow-xl\n              "),onClick:e=>e.stopPropagation(),children:[(c||m)&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800",children:[c&&(0,r.jsx)("h2",{className:"text-xl font-semibold text-white",children:c}),m&&(0,r.jsx)("button",{onClick:t,className:"p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,r.jsx)(i.yGN,{className:"w-5 h-5 text-gray-400"})})]}),(0,r.jsx)("div",{className:"p-6",children:d})]})})]})})}function d(e){let{isOpen:a,onClose:t,title:s,children:l,onSubmit:i,submitText:n="Submit",isLoading:d=!1,size:o="md"}=e;return(0,r.jsx)(c,{isOpen:a,onClose:t,title:s,size:o,children:(0,r.jsxs)("form",{onSubmit:i,className:"space-y-6",children:[l,(0,r.jsxs)("div",{className:"flex space-x-3 justify-end pt-4 border-t border-gray-800",children:[(0,r.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 text-gray-300 hover:text-white transition-colors",disabled:d,children:"Cancel"}),(0,r.jsxs)("button",{type:"submit",disabled:d,className:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2",children:[d&&(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:n})]})]})]})})}let o=c},75399:(e,a,t)=>{"use strict";t.d(a,{Ay:()=>o,Wh:()=>c,XI:()=>n,rA:()=>d});var r=t(95155),s=t(68289),l=t(12115),i=t(10351);function n(e){let{data:a,columns:t,loading:n=!1,searchable:c=!1,searchPlaceholder:d="Search...",onSearch:o,emptyMessage:m="No data available",className:x=""}=e,[p,u]=l.useState({key:null,direction:"asc"}),[h,g]=l.useState(""),y=l.useMemo(()=>p.key?[...a].sort((e,a)=>{let t=e[p.key],r=a[p.key];return t<r?"asc"===p.direction?-1:1:t>r?"asc"===p.direction?1:-1:0}):a,[a,p]);return n?(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg ".concat(x),children:[c&&(0,r.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:d,className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500",disabled:!0})]})}),(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-400 mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400 mt-2",children:"Loading..."})]})]}):(0,r.jsxs)("div",{className:"bg-gray-900/50 border border-gray-800 rounded-lg overflow-hidden ".concat(x),children:[c&&(0,r.jsx)("div",{className:"p-4 border-b border-gray-800",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(i.CKj,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,r.jsx)("input",{type:"text",placeholder:d,value:h,onChange:e=>{var a;g(a=e.target.value),null==o||o(a)},className:"w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-green-500"})]})}),0===y.length?(0,r.jsx)("div",{className:"p-8 text-center",children:(0,r.jsx)("p",{className:"text-gray-400",children:m})}):(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-800/50",children:(0,r.jsx)("tr",{children:t.map(e=>(0,r.jsx)("th",{className:"\n                      px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider\n                      ".concat(e.sortable?"cursor-pointer hover:text-white":"","\n                      ").concat(e.width?e.width:"","\n                    "),onClick:()=>{var a;let t;return e.sortable&&(a=e.key,t="asc",void(p.key===a&&"asc"===p.direction&&(t="desc"),u({key:a,direction:t})))},children:(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:e.title}),e.sortable&&(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)(i.wAb,{className:"w-3 h-3 ".concat(p.key===e.key&&"asc"===p.direction?"text-green-400":"text-gray-500")}),(0,r.jsx)(i.fK4,{className:"w-3 h-3 -mt-1 ".concat(p.key===e.key&&"desc"===p.direction?"text-green-400":"text-gray-500")})]})]})},String(e.key)))})}),(0,r.jsx)("tbody",{className:"divide-y divide-gray-800",children:y.map((e,a)=>(0,r.jsx)(s.P.tr,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.05*a},className:"hover:bg-gray-800/30 transition-colors",children:t.map(a=>(0,r.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:a.render?a.render(e[a.key],e):String(e[a.key]||"-")},String(a.key)))},a))})]})})]})}function c(e){let{status:a,variant:t="default"}=e;return(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-medium rounded-full ".concat({default:"bg-gray-500/20 text-gray-400",success:"bg-green-500/20 text-green-400",warning:"bg-yellow-500/20 text-yellow-400",danger:"bg-red-500/20 text-red-400",info:"bg-blue-500/20 text-blue-400"}[t]),children:a})}function d(e){let{onClick:a,children:t,variant:s="default",size:l="sm"}=e;return(0,r.jsx)("button",{onClick:a,className:"rounded transition-colors ".concat({default:"text-gray-400 hover:text-white",primary:"text-green-400 hover:text-green-300",danger:"text-red-400 hover:text-red-300"}[s]," ").concat({sm:"p-1",md:"p-2"}[l]),children:t})}let o=n}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,411,6071,8441,5964,7358],()=>e(e.s=54212)),_N_E=e.O()}]);