(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8869],{19958:(e,t,r)=>{"use strict";r.d(t,{YO:()=>x,uk:()=>d});var a=r(95155),s=r(12115),n=r(8619),i=r(37602),l=r(58829),o=r(68289),c=r(57740);function d(e){let{children:t,className:r="",intensity:d="medium",glowEffect:x=!0,hoverScale:m=!0,borderGradient:u=!1,elevation:p=2,onClick:h}=e,{theme:y}=(0,c.DP)(),g=(0,c.Yx)(y),b=(0,s.useRef)(null),j=(0,n.d)(0),f=(0,n.d)(0),v=(0,i.z)(j),N=(0,i.z)(f),w=(0,l.G)(N,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),P=(0,l.G)(v,[-.5,.5],"light"===d?[-5,5]:"medium"===d?[-10,10]:[-15,15]),k=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r={1:"light"===y?"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)":"0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.5)",2:"light"===y?"0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)":"0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.6)",3:"light"===y?"0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)":"0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.7)",4:"light"===y?"0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22)":"0 14px 28px rgba(0, 0, 0, 0.6), 0 10px 10px rgba(0, 0, 0, 0.8)",5:"light"===y?"0 19px 38px rgba(0, 0, 0, 0.30), 0 15px 12px rgba(0, 0, 0, 0.22)":"0 19px 38px rgba(0, 0, 0, 0.7), 0 15px 12px rgba(0, 0, 0, 0.9)"};return r[t?Math.min(e+2,5):e]||r[2]},F="\n    relative rounded-xl overflow-hidden transition-all duration-300 group\n    ".concat(g.bg.card,"\n    ").concat(g.border.primary,"\n    ").concat(u?"border-2 border-transparent bg-gradient-to-r from-green-400/20 to-blue-400/20 p-[1px]":"border","\n    ").concat(r,"\n  "),C=(0,a.jsxs)(o.P.div,{ref:b,className:F,style:{rotateY:P,rotateX:w,transformStyle:"preserve-3d",boxShadow:k(p)},onMouseMove:e=>{if(!b.current)return;let t=b.current.getBoundingClientRect(),r=t.width,a=t.height,s=(e.clientX-t.left)/r-.5,n=(e.clientY-t.top)/a-.5;j.set(s),f.set(n)},onMouseLeave:()=>{j.set(0),f.set(0)},whileHover:m?{scale:1.02,boxShadow:k(p,!0),transition:{duration:.2,ease:"easeOut"}}:{},transition:{type:"spring",stiffness:300,damping:30},onClick:h,children:[x&&(0,a.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-r from-green-400/5 to-blue-400/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-xl blur-xl",initial:{scale:.8},whileHover:{scale:1.1},transition:{duration:.3}}),(0,a.jsx)(o.P.div,{className:"absolute inset-0 bg-green-400/10 opacity-0 group-hover:opacity-20 rounded-xl",initial:{scale:0},whileHover:{scale:1},transition:{duration:.4,ease:"easeOut"}}),(0,a.jsx)("div",{className:"relative z-10 ".concat(u?"".concat(g.bg.card," rounded-xl"):""),children:t}),(0,a.jsx)(o.P.div,{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 ".concat("dark"===y?"via-white/5":"via-white/10"),initial:{x:"-100%"},whileHover:{x:"100%"},transition:{duration:.6,ease:"easeInOut"}})]});return(0,a.jsx)("div",{className:"group",children:C})}function x(e){let{title:t,value:r,subtitle:s,icon:n,color:i="green",className:l=""}=e,{theme:o}=(0,c.DP)(),x=(0,c.Yx)(o);return(0,a.jsx)(d,{className:"p-6 ".concat(l),glowEffect:!0,borderGradient:!0,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium ".concat(x.text.secondary),children:t}),(0,a.jsx)("p",{className:"text-2xl font-bold ".concat(x.text.primary," mt-1"),children:r}),s&&(0,a.jsx)("p",{className:"text-xs ".concat(x.text.tertiary," mt-1"),children:s})]}),n&&(0,a.jsx)("div",{className:"w-12 h-12 rounded-lg bg-gradient-to-r ".concat({green:"from-green-400 to-green-600",blue:"from-blue-400 to-blue-600",purple:"from-purple-400 to-purple-600",yellow:"from-yellow-400 to-yellow-600"}[i]," flex items-center justify-center"),children:(0,a.jsx)(n,{className:"w-6 h-6 text-white"})})]})})}},44001:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(95155),s=r(68289),n=r(12115),i=r(10351),l=r(11846),o=r(31246),c=r(19958),d=r(64198);class x{async makeRequest(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat(this.baseUrl).concat(this.apiPath).concat(e),a={"Content-Type":"application/json"},s=localStorage.getItem("token");s&&(a.Authorization="Bearer ".concat(s));let n={...t,headers:{...a,...t.headers}};try{let e=await fetch(r,n);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.error||"HTTP error! status: ".concat(e.status))}return await e.json()}catch(t){throw console.error("API request failed: ".concat(e),t),t}}async getAllGroups(){return this.makeRequest("")}async getMyGroups(){return this.makeRequest("/my")}async createGroup(e){return this.makeRequest("/rgs",{method:"POST",body:JSON.stringify(e)})}async joinGroup(e,t){return this.makeRequest("/".concat(e,"/join"),{method:"POST",body:JSON.stringify({userId:t})})}async leaveGroup(e,t){return this.makeRequest("/".concat(e,"/leave"),{method:"POST",body:JSON.stringify({userId:t})})}async makePayment(e,t){return this.makeRequest("/".concat(e,"/pay"),{method:"POST",body:JSON.stringify({userId:t})})}async getGroupStatus(e){return this.makeRequest("/".concat(e,"/status"))}async deleteGroup(e,t){return this.makeRequest("/".concat(e),{method:"DELETE",body:JSON.stringify({userId:t})})}calculateNextPayoutRecipient(e){let t=e.members.filter(e=>!e.hasReceivedPayout);return 0===t.length?null:t[0].userId._id}calculateTotalContribution(e){return e.amountPerInterval*e.members.length}getUserPaymentStatus(e,t){let r=e.members.find(e=>e.userId._id===t);if(!r)return{hasPaid:!1,hasReceivedPayout:!1,isNextRecipient:!1};let a=this.calculateNextPayoutRecipient(e);return{hasPaid:r.hasPaid,hasReceivedPayout:r.hasReceivedPayout,isNextRecipient:a===t}}formatIntervalText(e){return({daily:"Daily",weekly:"Weekly",monthly:"Monthly",yearly:"Yearly"})[e]||e}getDaysUntilNextPayout(e){let t=new Date;return Math.max(0,Math.ceil((new Date(e).getTime()-t.getTime())/864e5))}validateGroupData(e){let t=[];return((!e.name||e.name.trim().length<3)&&t.push("Group name must be at least 3 characters long"),(!e.amountPerInterval||e.amountPerInterval<1e3)&&t.push("Amount per interval must be at least ₦1,000"),e.intervalType&&["daily","weekly","monthly","yearly"].includes(e.intervalType)||t.push("Invalid interval type"),e.nextPayoutDate)?new Date(e.nextPayoutDate)<=new Date&&t.push("Next payout date must be in the future"):t.push("Next payout date is required"),t}getGroupStatistics(e,t){let r=e.filter(e=>e.members.some(e=>e.userId._id===t)),a=r.reduce((e,t)=>e+t.amountPerInterval,0),s=r.filter(e=>{let r=e.members.find(e=>e.userId._id===t);return r&&!r.hasPaid}).length,n=r.filter(e=>{let r=e.members.find(e=>e.userId._id===t);return r&&r.hasReceivedPayout}).length;return{totalGroups:r.length,totalContributions:a,pendingPayments:s,completedPayouts:n,activeGroups:r.filter(e=>e.isActive).length}}constructor(){this.baseUrl="http://localhost:8080",this.apiPath="/api/rotational-group-savings"}}let m=new x;function u(){let[e,t]=(0,n.useState)([]),[r,s]=(0,n.useState)([]),[x,u]=(0,n.useState)(!0),[y,g]=(0,n.useState)(!1),[b,j]=(0,n.useState)("my-groups"),[f,v]=(0,n.useState)({name:"",description:"",amountPerInterval:0,intervalType:"monthly",nextPayoutDate:""}),N="507f1f77bcf86cd799439011";(0,n.useEffect)(()=>{w(),P()},[]);let w=async()=>{try{u(!0);let e=await m.getMyGroups();t(e)}catch(e){console.error("Error fetching groups:",e),t([{_id:"1",name:"Monthly Savers Circle",description:"A group for monthly savings with friends",amountPerInterval:5e4,intervalType:"monthly",members:[{userId:{_id:"1",name:"John Doe",email:"<EMAIL>"},hasPaid:!0,hasReceivedPayout:!1,joinedAt:"2024-01-01T00:00:00Z"},{userId:{_id:"2",name:"Jane Smith",email:"<EMAIL>"},hasPaid:!1,hasReceivedPayout:!1,joinedAt:"2024-01-02T00:00:00Z"}],currentCycle:1,nextPayoutDate:"2024-02-01T00:00:00Z",createdBy:{_id:"1",name:"John Doe",email:"<EMAIL>"},createdAt:"2024-01-01T00:00:00Z",isActive:!0,payouts:[]}]),d.P0.error("Failed to fetch your groups")}finally{u(!1)}},P=async()=>{try{let e=await m.getAllGroups();s(e)}catch(e){console.error("Error fetching all groups:",e),s([{_id:"2",name:"Weekly Hustlers",description:"Fast-paced weekly savings for entrepreneurs",amountPerInterval:1e4,intervalType:"weekly",members:[{userId:{_id:"3",name:"Mike Johnson",email:"<EMAIL>"},hasPaid:!0,hasReceivedPayout:!1,joinedAt:"2024-01-01T00:00:00Z"}],currentCycle:1,nextPayoutDate:"2024-01-15T00:00:00Z",createdBy:{_id:"3",name:"Mike Johnson",email:"<EMAIL>"},createdAt:"2024-01-01T00:00:00Z",isActive:!0,payouts:[]}])}},k=async e=>{e.preventDefault();try{let e=await m.createGroup({...f,createdBy:N});t(t=>[e,...t]),g(!1),v({name:"",description:"",amountPerInterval:0,intervalType:"monthly",nextPayoutDate:""}),d.P0.success("Rotational group created successfully!")}catch(e){console.error("Error creating group:",e),d.P0.error("Failed to create group")}},F=async e=>{try{await m.joinGroup(e,N),d.P0.success("Successfully joined the group!"),w(),P()}catch(e){console.error("Error joining group:",e),d.P0.error("Failed to join group")}},C=async e=>{try{await m.makePayment(e,N),d.P0.success("Payment recorded successfully!"),w()}catch(e){console.error("Error making payment:",e),d.P0.error("Failed to record payment")}},D=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e),T=e=>new Date(e).toLocaleDateString("en-NG",{year:"numeric",month:"short",day:"numeric"}),G=e=>({daily:"Daily",weekly:"Weekly",monthly:"Monthly",yearly:"Yearly"})[e]||e,I=e=>e.members.some(e=>e.userId._id===N),S=e=>{let t=e.members.find(e=>e.userId._id===N);return(null==t?void 0:t.hasPaid)||!1};return(0,a.jsx)(l.A,{title:"Rotational Savings",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Rotational Savings"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Join rotating savings groups and grow your wealth together"})]}),(0,a.jsxs)(o.jn,{onClick:()=>g(!0),children:[(0,a.jsx)(i.GGD,{className:"mr-2"}),"Create Group"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(c.uk,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"My Groups"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.length})]}),(0,a.jsx)(i.cfS,{className:"text-green-500 text-3xl"})]})}),(0,a.jsx)(c.uk,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Contributions"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:D(e.reduce((e,t)=>e+t.amountPerInterval,0))})]}),(0,a.jsx)(i.z8N,{className:"text-blue-500 text-3xl"})]})}),(0,a.jsx)(c.uk,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Active Cycles"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.filter(e=>e.isActive).length})]}),(0,a.jsx)(i.ARf,{className:"text-purple-500 text-3xl"})]})})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-800 p-1 rounded-lg",children:[(0,a.jsx)("button",{onClick:()=>j("my-groups"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("my-groups"===b?"bg-green-600 text-white":"text-gray-400 hover:text-white"),children:"My Groups"}),(0,a.jsx)("button",{onClick:()=>j("all-groups"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("all-groups"===b?"bg-green-600 text-white":"text-gray-400 hover:text-white"),children:"Browse Groups"})]}),"my-groups"===b?(0,a.jsx)("div",{className:"space-y-4",children:x?(0,a.jsx)("div",{className:"flex justify-center py-12",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})}):0===e.length?(0,a.jsxs)(c.uk,{className:"bg-gray-800 border-gray-700 p-8 text-center",children:[(0,a.jsx)(i.cfS,{className:"text-gray-500 text-4xl mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-400 mb-2",children:"No Groups Yet"}),(0,a.jsx)("p",{className:"text-gray-500 mb-4",children:"You haven't joined any rotational savings groups yet."}),(0,a.jsx)(o.jn,{onClick:()=>g(!0),children:"Create Your First Group"})]}):e.map(e=>(0,a.jsx)(p,{group:e,isUserGroup:!0,onMakePayment:C,formatAmount:D,formatDate:T,getIntervalText:G,getUserPaymentStatus:S,currentUserId:N},e._id))}):(0,a.jsx)("div",{className:"space-y-4",children:0===r.filter(e=>!I(e)).length?(0,a.jsxs)(c.uk,{className:"bg-gray-800 border-gray-700 p-8 text-center",children:[(0,a.jsx)(i.cfS,{className:"text-gray-500 text-4xl mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-400 mb-2",children:"No Available Groups"}),(0,a.jsx)("p",{className:"text-gray-500",children:"There are no groups available to join at the moment."})]}):r.filter(e=>!I(e)).map(e=>(0,a.jsx)(p,{group:e,isUserGroup:!1,onJoinGroup:F,formatAmount:D,formatDate:T,getIntervalText:G,currentUserId:N},e._id))}),y&&(0,a.jsx)(h,{formData:f,setFormData:v,onSubmit:k,onClose:()=>g(!1)})]})})}function p(e){let{group:t,isUserGroup:r,onMakePayment:n,onJoinGroup:l,formatAmount:d,formatDate:x,getIntervalText:m,getUserPaymentStatus:u,currentUserId:p}=e,h=!!u&&u(t),y=t.createdBy._id===p,g=t.amountPerInterval*t.members.length;return(0,a.jsx)(s.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full",children:(0,a.jsx)(c.uk,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex flex-col lg:flex-row lg:items-center lg:justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:t.name}),(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:t.description})]}),y&&(0,a.jsx)("span",{className:"bg-green-600 text-white text-xs px-2 py-1 rounded-full",children:"Creator"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 gap-4 mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:"Contribution"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:d(t.amountPerInterval)}),(0,a.jsx)("p",{className:"text-gray-500 text-xs",children:m(t.intervalType)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:"Members"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:t.members.length}),(0,a.jsx)("p",{className:"text-gray-500 text-xs",children:"Active"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:"Total Payout"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:d(g)}),(0,a.jsx)("p",{className:"text-gray-500 text-xs",children:"Per cycle"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:"Next Payout"}),(0,a.jsx)("p",{className:"text-white font-semibold",children:x(t.nextPayoutDate)}),(0,a.jsxs)("p",{className:"text-gray-500 text-xs",children:["Cycle ",t.currentCycle]})]})]}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:"Members:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.members.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-1 rounded-full text-xs ".concat(e.hasPaid?"bg-green-600/20 text-green-400":"bg-gray-700 text-gray-400"),children:[(0,a.jsx)("span",{children:e.userId.name}),e.hasPaid?(0,a.jsx)(i.YrT,{size:12}):(0,a.jsx)(i.Ohp,{size:12})]},e.userId._id))})]})]}),(0,a.jsx)("div",{className:"flex flex-col space-y-2 lg:ml-6",children:r?(0,a.jsxs)(a.Fragment,{children:[h?(0,a.jsxs)("div",{className:"flex items-center text-green-400 text-sm",children:[(0,a.jsx)(i.YrT,{className:"mr-2"}),"Payment Complete"]}):(0,a.jsxs)(o.jn,{onClick:()=>null==n?void 0:n(t._id),className:"whitespace-nowrap",children:[(0,a.jsx)(i.z8N,{className:"mr-2"}),"Make Payment"]}),(0,a.jsxs)(o.rp,{className:"whitespace-nowrap",children:[(0,a.jsx)(i.Vap,{className:"mr-2"}),"View Details"]})]}):(0,a.jsxs)(o.jn,{onClick:()=>null==l?void 0:l(t._id),className:"whitespace-nowrap",children:[(0,a.jsx)(i.vq3,{className:"mr-2"}),"Join Group"]})})]})})})}function h(e){let{formData:t,setFormData:r,onSubmit:n,onClose:l}=e,c=e=>{let{name:t,value:a}=e.target;r(e=>({...e,[t]:"amountPerInterval"===t?Number(a):a}))};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4",children:(0,a.jsxs)(s.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},className:"bg-gray-900 rounded-2xl border border-gray-700 p-6 w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-bold text-white",children:"Create Rotational Group"}),(0,a.jsx)("button",{onClick:l,className:"text-gray-400 hover:text-white transition-colors",children:(0,a.jsx)(i.yGN,{size:24})})]}),(0,a.jsxs)("form",{onSubmit:n,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Group Name"}),(0,a.jsx)("input",{type:"text",name:"name",value:t.name,onChange:c,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white",placeholder:"e.g., Monthly Savers Circle",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Description"}),(0,a.jsx)("textarea",{name:"description",value:t.description,onChange:c,rows:3,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white",placeholder:"Describe your group..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Amount Per Interval (₦)"}),(0,a.jsx)("input",{type:"number",name:"amountPerInterval",value:t.amountPerInterval,onChange:c,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white",placeholder:"50000",min:"1000",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Interval Type"}),(0,a.jsxs)("select",{name:"intervalType",value:t.intervalType,onChange:c,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white",children:[(0,a.jsx)("option",{value:"daily",children:"Daily"}),(0,a.jsx)("option",{value:"weekly",children:"Weekly"}),(0,a.jsx)("option",{value:"monthly",children:"Monthly"}),(0,a.jsx)("option",{value:"yearly",children:"Yearly"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"First Payout Date"}),(0,a.jsx)("input",{type:"date",name:"nextPayoutDate",value:t.nextPayoutDate,onChange:c,className:"w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:border-green-500 focus:outline-none text-white",required:!0})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)(o.jn,{type:"submit",className:"flex-1",children:"Create Group"}),(0,a.jsx)(o.rp,{onClick:l,className:"flex-1",children:"Cancel"})]})]})]})})}},64198:(e,t,r)=>{"use strict";r.d(t,{CustomToaster:()=>o,P0:()=>l,oR:()=>n.Ay});var a=r(95155),s=r(68289),n=r(13568),i=r(10351);let l={success:e=>{n.Ay.success(e,{duration:4e3,position:"top-right",style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}})},error:e=>{n.Ay.error(e,{duration:5e3,position:"top-right",style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}})},warning:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"⚠️",style:{background:"#92400E",color:"#FFFBEB",border:"1px solid #F59E0B"}})},info:e=>{(0,n.Ay)(e,{duration:4e3,position:"top-right",icon:"ℹ️",style:{background:"#1E3A8A",color:"#EFF6FF",border:"1px solid #3B82F6"}})},loading:e=>n.Ay.loading(e,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"}}),dismiss:e=>{n.Ay.dismiss(e)},promise:(e,t)=>n.Ay.promise(e,t,{position:"top-right",style:{background:"#374151",color:"#F9FAFB",border:"1px solid #6B7280"},success:{style:{background:"#065F46",color:"#ECFDF5",border:"1px solid #10B981"},iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{style:{background:"#7F1D1D",color:"#FEF2F2",border:"1px solid #EF4444"},iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}})};function o(){return(0,a.jsx)(n.l$,{position:"top-right",reverseOrder:!1,gutter:8,containerClassName:"",containerStyle:{},toastOptions:{className:"",duration:4e3,style:{background:"#1F2937",color:"#F9FAFB",border:"1px solid #374151",borderRadius:"8px",fontSize:"14px",fontWeight:"500",padding:"12px 16px",boxShadow:"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"},success:{iconTheme:{primary:"#10B981",secondary:"#ECFDF5"}},error:{iconTheme:{primary:"#EF4444",secondary:"#FEF2F2"}}},children:e=>(0,a.jsx)(n.bv,{toast:e,children:t=>{let{icon:r,message:l}=t;return(0,a.jsxs)(s.P.div,{initial:{opacity:0,y:-50,scale:.3},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.3},transition:{type:"spring",stiffness:500,damping:30,duration:.3},className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:r}),(0,a.jsx)("div",{className:"flex-1",children:l}),"loading"!==e.type&&(0,a.jsx)("button",{onClick:()=>n.Ay.dismiss(e.id),className:"flex-shrink-0 p-1 rounded-full hover:bg-gray-600 transition-colors",children:(0,a.jsx)(i.yGN,{className:"w-4 h-4"})})]})}})})}},95607:(e,t,r)=>{Promise.resolve().then(r.bind(r,44001))}},e=>{e.O(0,[844,5236,6874,6766,3568,2574,3289,1846,8441,5964,7358],()=>e(e.s=95607)),_N_E=e.O()}]);