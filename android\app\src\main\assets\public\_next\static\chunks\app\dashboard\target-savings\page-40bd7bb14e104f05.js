(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8197],{47220:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var a=s(95155),r=s(12115),l=s(68289),n=s(10351),i=s(11846),c=s(24630),d=s(46142),o=s(13741),x=s(17703),m=s(52814),u=s(66440),g=s(93915),h=s(30353),y=s(64198);let b={EMERGENCY:"\uD83D\uDEA8",VACATION:"✈️",EDUCATION:"\uD83C\uDF93",HOUSE:"\uD83C\uDFE0",CAR:"\uD83D\uDE97",BUSINESS:"\uD83D\uDCBC",WEDDING:"\uD83D\uDC92",HEALTH:"\uD83C\uDFE5",OTHER:"\uD83C\uDFAF"},j={LOW:"bg-green-500",MEDIUM:"bg-yellow-500",HIGH:"bg-red-500"};function p(){let[e,t]=(0,r.useState)([]),[s,p]=(0,r.useState)(!0),[f,N]=(0,r.useState)(!1),[v,w]=(0,r.useState)(null),[A,C]=(0,r.useState)(!1),[T,S]=(0,r.useState)(!1),[E,D]=(0,r.useState)(null),[k,M]=(0,r.useState)({title:"",targetAmount:0,timelineMonths:1,frequency:"monthly"});(0,r.useEffect)(()=>{O()},[]);let O=async()=>{try{p(!0);let e=await c.Uy.getTargetSavings();t(e.targets)}catch(e){y.P0.error("Failed to load target savings")}finally{p(!1)}},P=async()=>{try{if(!k.title||!k.targetAmount||!k.timelineMonths||!k.frequency)return void y.P0.error("Please fill in all required fields");let e={...k,description:k.title,targetDate:new Date(Date.now()+30*k.timelineMonths*864e5).toISOString().split("T")[0],category:"OTHER",priority:"MEDIUM",autoContribution:!1};await c.Uy.createTargetSavings(e),y.P0.success("Target savings created successfully"),N(!1),M({title:"",targetAmount:0,timelineMonths:1,frequency:"monthly"}),O()}catch(e){y.P0.error("Failed to create target savings")}},I=e=>new Intl.NumberFormat("en-NG",{style:"currency",currency:"NGN"}).format(e);return s?(0,a.jsx)(i.A,{title:"Target Savings",children:(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"})})}):(0,a.jsxs)(i.A,{title:"Target Savings",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white",children:"Target Savings"}),(0,a.jsx)("p",{className:"text-gray-400 mt-2",children:"Set and achieve your financial goals"})]}),(0,a.jsxs)(o.Ay,{onClick:()=>N(!0),className:"bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(n.GGD,{className:"mr-2"}),"Create Target"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)(x.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Targets"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.length})]}),(0,a.jsx)(n.x_j,{className:"text-green-500 text-2xl"})]})}),(0,a.jsx)(x.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Active Targets"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.filter(e=>"ACTIVE"===e.status).length})]}),(0,a.jsx)(n.ARf,{className:"text-blue-500 text-2xl"})]})}),(0,a.jsx)(x.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Total Saved"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:I(e.reduce((e,t)=>e+t.currentAmount,0))})]}),(0,a.jsx)(n.z8N,{className:"text-yellow-500 text-2xl"})]})}),(0,a.jsx)(x.A,{className:"bg-gray-800 border-gray-700 p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"Completed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-white",children:e.filter(e=>{if("COMPLETED"===e.status)return!0;let t="number"==typeof e.currentAmount?e.currentAmount:e.savedAmount||0,s="number"==typeof e.targetAmount?e.targetAmount:0;return(void 0!==e.progress?e.progress:s>0?t/s*100:0)>=100}).length})]}),(0,a.jsx)(n.A3x,{className:"text-green-500 text-2xl"})]})})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>{let t=e.goalName||e.title||"",s=e.description||"",r="number"==typeof e.currentAmount?e.currentAmount:e.savedAmount||0,i="number"==typeof e.targetAmount?e.targetAmount:0,c=void 0!==e.progress?e.progress:Math.min(r/i*100,100),d=e.interestAccrued||e.interestEarned||0,u=e.interestRate||0,g=(e.frequency||e.contributionFrequency||"").toUpperCase(),h=e.timelineMonths||1,y=e.startDate||e.targetDate||e.createdAt||"",p=e.status||(c>=100?"COMPLETED":"ACTIVE"),f="";if(y&&h){let e=new Date(y);e.setMonth(e.getMonth()+h),f=e.toISOString().split("T")[0]}let N=0;if(f){let e=new Date;N=Math.ceil((new Date(f).getTime()-e.getTime())/864e5)}let v="COMPLETED"===p||c>=100,A=async()=>{D(e),S(!0)};return(0,a.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"cursor-pointer",onClick:()=>{w(e),C(!0)},children:(0,a.jsx)(x.A,{className:"bg-gray-800 border-gray-700 p-6 hover:border-green-500 transition-colors",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:b[e.category]||"\uD83C\uDFAF"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-white",children:t}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:s})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(j[e.priority]||"bg-gray-500")}),(0,a.jsx)(m.A,{variant:"ACTIVE"===p?"success":"default",children:p})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,a.jsxs)("span",{className:"text-white",children:[c.toFixed(1),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(c,"%")}})}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-400",children:I(r)}),(0,a.jsx)("span",{className:"text-white",children:I(i)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-400 mt-1",children:[(0,a.jsxs)("span",{children:["Interest: ",I(d)," (",u,"% p.a.)"]}),(0,a.jsxs)("span",{children:["Freq: ",g]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center text-gray-400",children:[(0,a.jsx)(n.wIk,{className:"mr-1"}),N>0?"".concat(N," days left"):"Overdue"]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-400",children:[(0,a.jsxs)("span",{children:["Start: ",y?new Date(y).toLocaleDateString():"-"]}),f&&(0,a.jsxs)("span",{className:"ml-2",children:["End: ",new Date(f).toLocaleDateString()]})]})]}),v&&(0,a.jsx)("div",{className:"flex gap-2 pt-2",children:(0,a.jsx)(o.Ay,{className:"bg-blue-600 hover:bg-blue-700",onClick:A,children:"Withdraw & Close Target"})})]})})},e._id||e.id)})}),0===e.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(n.x_j,{className:"mx-auto text-6xl text-gray-600 mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No Target Savings Yet"}),(0,a.jsx)("p",{className:"text-gray-400 mb-6",children:"Create your first savings target to get started"}),(0,a.jsxs)(o.Ay,{onClick:()=>N(!0),className:"bg-green-600 hover:bg-green-700",children:[(0,a.jsx)(n.GGD,{className:"mr-2"}),"Create Your First Target"]})]})]}),(0,a.jsx)(u.Ay,{isOpen:f,onClose:()=>N(!1),title:"Create Target Savings",size:"lg",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(g.A,{label:"Target Title",value:k.title,onChange:e=>M({...k,title:e.target.value}),placeholder:"e.g., Emergency Fund",required:!0}),(0,a.jsx)(g.A,{label:"Target Amount",type:"number",value:k.targetAmount,onChange:e=>M({...k,targetAmount:Number(e.target.value)}),placeholder:"0",required:!0}),(0,a.jsx)(g.A,{label:"Timeline (months)",type:"number",value:k.timelineMonths,onChange:e=>M({...k,timelineMonths:Number(e.target.value)}),placeholder:"e.g., 12",required:!0}),(0,a.jsx)(h.A,{label:"Frequency",value:k.frequency,onChange:e=>M({...k,frequency:e.target.value}),options:[{value:"daily",label:"Daily"},{value:"weekly",label:"Weekly"},{value:"monthly",label:"Monthly"}],required:!0}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(o.Ay,{variant:"outline",onClick:()=>N(!1),children:"Cancel"}),(0,a.jsx)(o.Ay,{onClick:P,className:"bg-green-600 hover:bg-green-700",children:"Create Target"})]})]})}),(0,a.jsx)(u.Ay,{isOpen:T,onClose:()=>{S(!1),D(null)},title:"Withdraw & Close Target",size:"md",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-200 text-lg font-semibold",children:"Are you sure you want to withdraw and close this target?"}),(0,a.jsx)("p",{className:"text-gray-400",children:"This will credit your in-app balance with the saved amount. This action cannot be undone."}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,a.jsx)(o.Ay,{variant:"outline",onClick:()=>{S(!1),D(null)},children:"Cancel"}),(0,a.jsx)(o.Ay,{className:"bg-blue-600 hover:bg-blue-700",onClick:async()=>{if(E)try{await d.u.initiatePlanClosure({planId:E._id||E.id,amount:E.currentAmount||E.savedAmount||0}),y.P0.success("Withdrawal successful! Funds credited to your in-app balance."),S(!1),D(null),O()}catch(e){y.P0.error(e.message||"Withdrawal failed")}},children:"Yes, Withdraw & Close"})]})]})})]})}},52814:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,E:()=>r});var a=s(95155);function r(e){let{children:t,variant:s="default",size:r="md",className:l=""}=e,n="\n    ".concat("\n    inline-flex items-center justify-center font-medium rounded-full\n    transition-all duration-200\n  "," \n    ").concat({default:"bg-gray-700 text-gray-300 border border-gray-600",success:"bg-green-500/20 text-green-400 border border-green-500/30",warning:"bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",error:"bg-red-500/20 text-red-400 border border-red-500/30",info:"bg-blue-500/20 text-blue-400 border border-blue-500/30"}[s]," \n    ").concat({sm:"px-2 py-1 text-xs",md:"px-3 py-1 text-sm",lg:"px-4 py-2 text-base"}[r]," \n    ").concat(l,"\n  ");return(0,a.jsx)("span",{className:n,children:t})}s(12115);let l=r},60703:(e,t,s)=>{Promise.resolve().then(s.bind(s,47220))},66440:(e,t,s)=>{"use strict";s.d(t,{Ay:()=>o,aF:()=>c,k5:()=>d});var a=s(95155),r=s(60760),l=s(68289);s(12115);var n=s(10351);let i={sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-7xl"};function c(e){let{isOpen:t,onClose:s,title:c,children:d,size:o="md",showCloseButton:x=!0}=e;return(0,a.jsx)(r.N,{children:t&&(0,a.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,a.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm",onClick:s}),(0,a.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,a.jsxs)(l.P.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},className:"\n                relative w-full ".concat(i[o]," \n                bg-gray-900 border border-gray-800 rounded-lg shadow-xl\n              "),onClick:e=>e.stopPropagation(),children:[(c||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-800",children:[c&&(0,a.jsx)("h2",{className:"text-xl font-semibold text-white",children:c}),x&&(0,a.jsx)("button",{onClick:s,className:"p-2 rounded-lg hover:bg-gray-800 transition-colors",children:(0,a.jsx)(n.yGN,{className:"w-5 h-5 text-gray-400"})})]}),(0,a.jsx)("div",{className:"p-6",children:d})]})})]})})}function d(e){let{isOpen:t,onClose:s,title:r,children:l,onSubmit:n,submitText:i="Submit",isLoading:d=!1,size:o="md"}=e;return(0,a.jsx)(c,{isOpen:t,onClose:s,title:r,size:o,children:(0,a.jsxs)("form",{onSubmit:n,className:"space-y-6",children:[l,(0,a.jsxs)("div",{className:"flex space-x-3 justify-end pt-4 border-t border-gray-800",children:[(0,a.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 text-gray-300 hover:text-white transition-colors",disabled:d,children:"Cancel"}),(0,a.jsxs)("button",{type:"submit",disabled:d,className:"px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 text-white rounded-lg transition-colors flex items-center space-x-2",children:[d&&(0,a.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,a.jsx)("span",{children:i})]})]})]})})}let o=c}},e=>{e.O(0,[844,9268,5236,6874,6766,3568,5221,3289,4630,1846,411,8441,5964,7358],()=>e(e.s=60703)),_N_E=e.O()}]);